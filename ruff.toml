# ruff.toml

# Match line length with black and flake8
line-length = 88
# Set the target Python version to 3.12
target-version = "py312"

[lint]
# Select the same rule families that flake8 covers by default (E, W, F)
# plus the ones you wanted (I, <PERSON>, SIM).
select = ["E", "W", "F", "I", "UP", "SIM"]

# CRITICAL: Ignore rules that are incompatible with Black.
# Note: W503 is no longer needed in modern ruff versions
ignore = [
    "E203",  # whitespace before ':', handled by Black
    "E501",  # line-length, handled by <PERSON>
]

# Exclude the same files as in .flake8
exclude = [
    ".git",
    "__pycache__",
    ".venv",
    "venv",
    "build",
    "dist",
    ".tox",
    ".eggs",
    "*.egg-info",
]

[lint.isort]
known-first-party = ["src"]