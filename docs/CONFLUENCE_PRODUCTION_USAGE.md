# 🚀 Confluence Retry Solution - Guide de Production

**Note**: Ce fichier sera complété avec le contenu de CONFLUENCE_PRODUCTION_USAGE.md

## ✅ **Solution Validée et Prête pour Production**

Cette solution a été validée avec succès le 15 juin 2025 avec des tests réseau réels montrant un taux de succès de 100%.

## 🎯 **Intégration Rapide (Recommandée)**

### Option 1: Patch sur Client Existant

```python
from kbotloadscheduler.loader.confluence.client.confluence_retry_patch import add_retry_capability

# Votre client existant
client = ConfluenceClient(credentials)

# Ajoutez la capacité de retry (non-invasif)
add_retry_capability(client)

# Configurez selon vos besoins
client.configure_retry(
    max_retries=3,  # 3 tentatives max
    base_delay=2.0,  # 2 secondes de base
    enable_jitter=True  # Randomisation pour éviter thundering herd
)

# Utilisez la nouvelle méthode avec retry automatique
try:
    results = client.search_content_with_retry(
        cql="space = 'MYSPACE'",
        limit=50
    )
    print(f"✅ Récupéré {len(results)} documents")

    # Obtenez les statistiques de performance
    stats = client.get_retry_stats()
    print(f"📊 Taux de succès: {stats['success_rate']}")

except Exception as e:
    print(f"❌ Échec après tous les retries: {e}")
```

## 📊 **Configuration Recommandée Production**

### Pour Usage Normal
```python
client.configure_retry(
    max_retries=3,      # Équilibre entre fiabilité et vitesse
    base_delay=2.0,     # Respectueux du serveur
    enable_jitter=True  # Évite la congestion
)
```

## 🚨 **Gestion d'Erreurs Production**

### Pattern Recommandé

```python
import time
from kbotloadscheduler.loader.confluence.client.confluence_retry_patch import add_retry_capability


def process_confluence_safely(client, cql_query):
    """Pattern production-ready pour traiter Confluence."""

    # Ajout du retry si pas déjà fait
    if not hasattr(client, 'search_content_with_retry'):
        add_retry_capability(client)
        client.configure_retry(max_retries=3, base_delay=2.0)

    try:
        # Tentative avec retry automatique
        results = client.search_content_with_retry(cql_query, limit=100)

        # Log du succès
        stats = client.get_retry_stats()
        logger.info(f"✅ Confluence query successful: {len(results)} results, "
                    f"stats: {stats}")

        return results

    except Exception as e:
        logger.error(f"❌ Confluence completely failed: {e}")
        raise

    return []
```

**🎉 Solution entièrement validée et recommandée pour déploiement production!**
