# Documentation kbot-load-scheduler

Index de toute la documentation disponible pour le projet kbot-load-scheduler.

## 📚 Documentation Principale

### Architecture et Concepts
- **[Vue d'ensemble du système](SYSTEM_OVERVIEW.md)** - Architecture complète, composants et flux de données
- **[Workflow Confluence GCP](CONFLUENCE_LOADER_GCP_WORKFLOW.md)** - Workflow détaillé pour Confluence
- **[Déploiement Confluence RAG Cloud Run](CONFLUENCE_RAG_CLOUDRUN_DEPLOYMENT.md)** - Guide de déploiement pour Cloud Run

### Guides de Test et Développement
- **[Guide des Mocks GCS](GCS_MOCKING_GUIDE.md)** - Tests locaux avec mocks GCS et authentification
- **[Guide Confluence Mode Hybride](CONFLUENCE_HYBRID_TESTING_GUIDE.md)** ⭐ - Tester avec vraies données Confluence + mocks infrastructure
- **[Guide de Tests](TESTING_GUIDE.md)** - Stratégies et exemples de tests
- **[Guide de Debugging](DEBUGGING_GUIDE.md)** - Résolution de problèmes et debugging

## 🧪 Tests et Mocking

### Modes de Test Disponibles

| Mode | Description | Usage |
|------|-------------|-------|
| **Mock Complet** | Tous les services mockés | Développement local, tests unitaires |
| **Mode Hybride** | Mocks GCS/Auth + Vrai Confluence | Test avec vraies données, validation |
| **Mode Production** | Tous les services réels | Déploiement final, tests E2E |

### Démarrage Rapide - Tests

```bash
# 1. Tests avec mocks complets (recommandé pour développement)
make start-mock
make test-mock-endpoint

# 2. Tests hybrides avec vrai Confluence
export USE_MOCKS=true MOCK_CONFLUENCE=false
export CONFLUENCE_URL="https://company.atlassian.net"
export CONFLUENCE_USERNAME="..." CONFLUENCE_API_TOKEN="..."
make start-mock

# 3. Test d'un endpoint spécifique
curl -X POST "http://localhost:8092/loader/list/ebotman" \
  -H "Content-Type: application/json" \
  -d '{"get_list_file": "gs://mock-bucket-ebotman/test-data/getlist.json"}'
```

## 🔧 Configuration Avancée

### Variables d'Environnement Clés

#### Mode Mock
```bash
USE_MOCKS=true              # Active tous les mocks
MOCK_CONFLUENCE=false       # Désactive le mock Confluence (mode hybride)
ENV=tests                   # Environnement de test
```

#### Confluence
```bash
CONFLUENCE_URL="..."        # URL de l'instance Confluence
CONFLUENCE_PAT_TOKEN="..."  # Personal Access Token (utilisé dans la cas du Confluence d'Orange)
CONFLUENCE_CLOUD=false       # true pour Cloud, false pour Server (cas du server Confluence d'Orange)
```

#### GCS et Infrastructure
```bash
GCP_PROJECT_ID="..."                    # Projet Google Cloud
KBOT_WORK_BUCKET_PREFIX="gs://..."      # Préfixe des buckets de travail
GOOGLE_APPLICATION_CREDENTIALS="..."    # Chemin vers les credentials GCP
```

## 📖 Guides par Cas d'Usage

### Je veux...

#### 🚀 Commencer rapidement avec des tests locaux
→ [Guide des Mocks GCS](GCS_MOCKING_GUIDE.md)

#### 🧪 Tester avec de vraies données Confluence
→ [Guide Confluence Mode Hybride](CONFLUENCE_HYBRID_TESTING_GUIDE.md)

#### 🔍 Déboguer un problème
→ [Guide de Debugging](DEBUGGING_GUIDE.md)

#### 🏗️ Comprendre l'architecture
→ [Vue d'ensemble du système](SYSTEM_OVERVIEW.md)

#### ☁️ Déployer en production
→ [Déploiement Confluence RAG Cloud Run](CONFLUENCE_RAG_CLOUDRUN_DEPLOYMENT.md)

## 🆘 Support et Dépannage

### Problèmes Courants

| Problème | Guide | Section |
|----------|-------|---------|
| Mocks ne s'activent pas | [Mocks GCS](GCS_MOCKING_GUIDE.md) | Variables d'Environnement |
| Confluence 401/403 | [Mode Hybride](CONFLUENCE_HYBRID_TESTING_GUIDE.md) | Dépannage |
| Erreurs GCS en prod | [Debugging](DEBUGGING_GUIDE.md) | Erreurs GCS |
| Performance lente | [Vue d'ensemble](SYSTEM_OVERVIEW.md) | Optimisations |

### Logs Importants à Surveiller

```bash
# Activation des mocks
🔧 Enabling unified mocking for local testing...
✅ All mocks enabled successfully

# Connexion Confluence réelle
🌐 ConfluenceLoader connecting to REAL Confluence: https://...
🔍 Processing Confluence space: MONSPACE

# Erreurs à investiguer
❌ Failed to get confluence client: HTTPError 401
❌ Error: No such bucket: mock-bucket-ebotman
```

---

*Dernière mise à jour : Juin 2025*
