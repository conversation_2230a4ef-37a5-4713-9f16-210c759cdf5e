# Guide d'installation de Google Secret Manager pour le Loader Confluence

## Pourquoi utiliser Secret Manager

Votre loader Confluence est conçu pour fonctionner avec **Google Secret Manager** pour le stockage sécurisé des identifiants. Le loader s'attend à ce que les identifiants soient stockés en suivant un modèle de nommage spécifique :

- **Principal** : `{perimeter_code}-confluence-credentials` (ex: `mktsearch-confluence-credentials`)
- **De secours** : `confluence-credentials` (global)

## Avantages de Secret Manager pour Cloud Run

1.  **Sécurité** : Les identifiants n'apparaissent jamais dans le code ou les variables d'environnement.
2.  **Rotation automatique** : Facile à mettre à jour sans redéploiement.
3.  **Intégration IAM** : Contrôle d'accès précis et granulaire.
4.  **Piste d'audit** : Suivi de l'accès aux identifiants.
5.  **Disponibilité régionale** : Fonctionne avec tous les services GCP.

## Processus d'installation

### Étape 1 : Configurer Secret Manager

```bash
# Mettez d'abord à jour la configuration dans le script
vim scripts/setup_gcp_confluence_secrets.sh

# Exécutez le script d'installation
./scripts/setup_gcp_confluence_secrets.sh
```

**Ce que fait ce script :**
- Crée le secret `mktsearch-confluence-credentials` dans Secret Manager.
- Accorde l'accès à votre compte de service Cloud Run.
- Crée un secret global de secours.
- Vérifie l'installation.

### Étape 2 : Obtenir votre jeton d'accès personnel (PAT) Confluence

1.  Rendez-vous sur : https://espace.agir.orange.com
2.  Naviguez vers : Profil → Personal Access Tokens (Jetons d'accès personnels)
3.  Créez un nouveau jeton :
    - **Nom** : "kbot-rag-loader"
    - **Autorisations** : Accès en LECTURE (READ) aux espaces requis.
    - **Expiration** : 1 an (ou selon la politique en vigueur).
4.  Copiez le jeton généré.

### Étape 3 : Configurer le secret

Le secret doit contenir la structure JSON suivante :

```json
{
  "pat_token": "VOTRE_VRAI_JETON_PAT",
  "confluence_url": "https://espace.agir.orange.com",
  "cloud": false,
  "timeout": 60,
  "max_retries": 3,
  "verify_ssl": true
}
```

### Étape 4 : Vérifier la configuration

```bash
# Mettez à jour l'ID du projet dans le script de vérification
vim scripts/verify_gcp_confluence_secrets.py

# Exécutez la vérification
python scripts/verify_gcp_confluence_secrets.py
```

## Structure attendue dans Secret Manager

Votre Secret Manager aura la structure suivante :

```
Google Secret Manager
├── mktsearch-confluence-credentials  (Principal - pour le périmètre mktsearch)
│   └── Dernière version : identifiants JSON
├── confluence-credentials           (De secours - global)
│   └── Dernière version : identifiants JSON
└── [autres secrets...]
```

## Comment le loader utilise Secret Manager

1.  **Flux d'authentification** :
    ```python
    # Le loader essaie automatiquement (dans cet ordre) :
    # 1. mktsearch-confluence-credentials  (spécifique au périmètre)
    # 2. confluence-credentials           (global de secours)
    # 3. Variables d'environnement        (développement)
    ```

2.  **Hiérarchie des identifiants** :
    - **Priorité la plus élevée** : `{perimeter_code}-confluence-credentials`
    - **Priorité moyenne** : `confluence-credentials`
    - **Priorité la plus basse** : Variables d'environnement

## Intégration avec Cloud Run

Votre service Cloud Run nécessite l'autorisation IAM suivante :

```bash
# Le compte de service Cloud Run a besoin du rôle
roles/secretmanager.secretAccessor
```

Le loader effectuera automatiquement les actions suivantes :
- Détecter qu'il s'exécute dans Cloud Run.
- Utiliser l'authentification du compte de service.
- Récupérer les identifiants depuis Secret Manager.
- Mettre en cache les identifiants pour améliorer les performances.

## Tester votre configuration

Une fois Secret Manager configuré :

1.  **Testez les identifiants localement** :
    ```bash
    gcloud secrets versions access latest --secret="mktsearch-confluence-credentials"
    ```

2.  **Testez l'accès depuis Cloud Run** :
    ```bash
    # Mettez à jour la configuration et lancez le test du planificateur
    ./scripts/test_cloudrun_confluence_scheduler.sh
    ```

3.  **Vérifiez avec votre fichier téléversé** :
    ```bash
    # Votre fichier getlist.json est déjà téléversé ici :
    # gs://ofr-ekb-knowledgebot-work-dev-mktsearch-dev/202506190800/CiblesRurales/testconfluence27284/getlist/getlist.json
    ```

## Dépannage

### Problèmes courants

1.  **Secret non trouvé** :
    - Vérifiez que le nom du secret correspond au code du périmètre.
    - Vérifiez les autorisations IAM.

2.  **Échec de l'authentification** :
    - Vérifiez que le jeton PAT est valide.
    - Vérifiez que l'URL de Confluence est accessible.

3.  **Autorisation refusée (Permission denied)** :
    - Assurez-vous que le compte de service Cloud Run a le rôle `secretAccessor`.
    - Vérifiez que le secret existe dans le bon projet.

### Commandes de débogage

```bash
# Lister tous les secrets
gcloud secrets list

# Vérifier l'accès à un secret
gcloud secrets versions access latest --secret="mktsearch-confluence-credentials"

# Vérifier la politique IAM d'un secret
gcloud secrets get-iam-policy mktsearch-confluence-credentials
```

## Prochaines étapes

Après avoir configuré Secret Manager :

1.  **Testez avec Google Cloud Scheduler** (approche recommandée).
2.  **Utilisez le script d'authentification Python** pour des tests interactifs.
3.  **Surveillez les journaux (logs) de Cloud Run** pour confirmer le succès de l'authentification.

L'approche avec Google Cloud Scheduler est idéale car elle contourne les problèmes d'authentification locale et s'exécute avec les bonnes autorisations de compte de service GCP.