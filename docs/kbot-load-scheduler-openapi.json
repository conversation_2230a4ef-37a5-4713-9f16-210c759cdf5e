{"openapi": "3.1.0", "info": {"title": "API load scheduler pour l'Enabler Knowledge Bot", "description": "\nLe load-scheduler permet d'automatiser l'embedding des documents\n  - récupération des sources à charger\n  - récupération des listes de documents présents dans chaque source\n  - comparaison de la liste des documents de la source avec la liste des documents déjà dans la base vecteur\n  - récupération des nouveaux documents à embedder de la source vers GCP\n  - appel de l'embedding pour ajouter les documents dans la base vecteur\n  - suppression de la base vecteur des documents qui ne sont plus dans la source\n\n\nenvironnement : local\n\nversion : dev", "version": "0.1.0"}, "paths": {"/schedule/treatments/{perimeter_code}/{date}": {"get": {"tags": ["schedule"], "summary": "List Treatments", "description": "Lancement de la récupération des traitements à réaliser", "operationId": "list_treatments_schedule_treatments__perimeter_code___date__get", "parameters": [{"name": "perimeter_code", "in": "path", "required": true, "schema": {"type": "string", "description": "Perimetre (ebotman/mktsearch ....)", "title": "Perimeter Code"}, "description": "Perimetre (ebotman/mktsearch ....)"}, {"name": "date", "in": "path", "required": true, "schema": {"type": "string", "description": "Date courante au format YYYYMMDDHHMMSS", "title": "Date"}, "description": "Date courante au format YYYYMMDDHHMMSS"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TreatmentBean"}, "title": "Response List Treatments Schedule Treatments  Perimeter Code   Date  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/schedule/treatments/{perimeter_code}/launch": {"post": {"tags": ["schedule"], "summary": "Launch Treatments", "description": "Launch treatments needed\nTo call from a cloud scheduler\nArgs:\n    schedule_service:\n    perimeter_code", "operationId": "launch_treatments_schedule_treatments__perimeter_code__launch_post", "parameters": [{"name": "perimeter_code", "in": "path", "required": true, "schema": {"type": "string", "description": "Perimetre (ebotman/mktsearch ....)", "title": "Perimeter Code"}, "description": "Perimetre (ebotman/mktsearch ....)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultBean"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/sources/loadall": {"post": {"tags": ["sources"], "summary": "Load All Sources", "description": "Lancement de la récupération des documents pour toutes les sources de tous les périmètres", "operationId": "load_all_sources_sources_loadall_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/SourceBean"}, "type": "array", "title": "Response Load All Sources Sources Loadall Post"}}}}}}}, "/sources/load/{perimeter_code}": {"post": {"tags": ["sources"], "summary": "Load Sources", "description": "Lancement de la récupération des documents pour toutes le sources du périmètre", "operationId": "load_sources_sources_load__perimeter_code__post", "parameters": [{"name": "perimeter_code", "in": "path", "required": true, "schema": {"type": "string", "description": "Code du périmètre pour lequel on veut remonter les sources", "title": "Perimeter Code"}, "description": "Code du périmètre pour lequel on veut remonter les sources"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SourceBean"}, "title": "Response Load Sources Sources Load  Perimeter Code  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/loader/list/{perimeter_code}": {"post": {"tags": ["loader"], "summary": "Get Document List", "description": "Lancement de la récupération de la liste des documents présents dans la source", "operationId": "get_document_list_loader_list__perimeter_code__post", "parameters": [{"name": "perimeter_code", "in": "path", "required": true, "schema": {"type": "string", "description": "Code du périmètre concerné", "title": "Perimeter Code"}, "description": "Code du périmètre concerné"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/LoaderGetListFileBean"}], "description": "<PERSON><PERSON>er getlist contenant la définition de la source\n            pour laquelle on veut récupérer la liste des documents", "title": "Get List File Bean"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentAnswerBean"}, "title": "Response Get Document List Loader List  Perimeter Code  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/loader/document/{perimeter_code}": {"post": {"tags": ["loader"], "summary": "Get Document", "description": "Lancement de la récupération des documents d'une source", "operationId": "get_document_loader_document__perimeter_code__post", "parameters": [{"name": "perimeter_code", "in": "path", "required": true, "schema": {"type": "string", "description": "Code du périmètre concerné", "title": "Perimeter Code"}, "description": "Code du périmètre concerné"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/GetDocumentFileBean"}], "description": "<PERSON><PERSON>er getdoc contenant la définition de la source\n            et le document à récupérer pour cette source", "title": "Document Get File Bean"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentWithMetadataBean"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/document/compare/{perimeter_code}": {"post": {"tags": ["document"], "summary": "Compare List", "description": "Comparaison de la liste des documents sur le repo et de la liste des documents embeddés.\n\nLa date de dernière modification est prise en compte et si la date de l'embedding est plus ancienne\nalors le document est marqué à récupérer", "operationId": "compare_list_document_compare__perimeter_code__post", "parameters": [{"name": "perimeter_code", "in": "path", "required": true, "schema": {"type": "string", "description": "Code du périmètre concerné", "title": "Perimeter Code"}, "description": "Code du périmètre concerné"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/RepoDocumentListFileBean"}], "description": "<PERSON><PERSON><PERSON> list contenant la liste des documents présents sur la source\n            ", "title": "Repo Document List File Bean"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentAnswerBean"}, "title": "Response Compare List Document Compare  Perimeter Code  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/document/embedd/{perimeter_code}": {"post": {"tags": ["document"], "summary": "Embedd Document", "description": "Lancement de l'embedding d'un document récupéré", "operationId": "embedd_document_document_embedd__perimeter_code__post", "parameters": [{"name": "perimeter_code", "in": "path", "required": true, "schema": {"type": "string", "description": "Code du périmètre concerné", "title": "Perimeter Code"}, "description": "Code du périmètre concerné"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/EmbeddDocumentFileBean"}], "description": "Fichier doc .metdata.json contenant les metadata du document à embedder", "title": "Embedd Document File Bean"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentAnswerBean"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/document/remove/{perimeter_code}": {"post": {"tags": ["document"], "summary": "Remove Documents", "description": "Lancement de la suppression de documents", "operationId": "remove_documents_document_remove__perimeter_code__post", "parameters": [{"name": "perimeter_code", "in": "path", "required": true, "schema": {"type": "string", "description": "Code du périmètre concerné", "title": "Perimeter Code"}, "description": "Code du périmètre concerné"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/RemoveDocumentFileBean"}], "description": "<PERSON><PERSON><PERSON> removedoc contenan la définition de la source et\n            la liste des documents à supprimer", "title": "Remove Document File Bean"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentAnswerBean"}, "title": "Response Remove Documents Document Remove  Perimeter Code  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"DocumentAnswerBean": {"properties": {"id": {"type": "string", "title": "Id", "description": "Identifiant externe du document"}, "path": {"type": "string", "title": "Path", "description": "Chemin relatif du document"}, "name": {"type": "string", "title": "Name", "description": "Nom ou chemin du document"}, "modification_time": {"type": "string", "title": "Modification Time", "description": "Date et heure de dernière modification du document"}}, "type": "object", "required": ["id", "path", "name", "modification_time"], "title": "DocumentAnswerBean", "description": "Format d'un document dans la réponse des api"}, "DocumentWithMetadataBean": {"properties": {"document": {"allOf": [{"$ref": "#/components/schemas/DocumentAnswerBean"}], "description": "Le document"}, "metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "description": "Les metadata du documents"}}, "type": "object", "required": ["document", "metadata"], "title": "DocumentWithMetadataBean", "description": "Représentation d'un document avec ses metadatas"}, "EmbeddDocumentFileBean": {"properties": {"embedd_document_metadata_file": {"type": "string", "title": "Embedd Document Metadata File"}}, "type": "object", "required": ["embedd_document_metadata_file"], "title": "EmbeddDocumentFileBean"}, "GetDocumentFileBean": {"properties": {"document_get_file": {"type": "string", "title": "Document Get File"}}, "type": "object", "required": ["document_get_file"], "title": "GetDocumentFileBean"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "LoaderGetListFileBean": {"properties": {"get_list_file": {"type": "string", "title": "Get List File"}}, "type": "object", "required": ["get_list_file"], "title": "LoaderGetListFileBean"}, "RemoveDocumentFileBean": {"properties": {"remove_document_file": {"type": "string", "title": "Remove Document File"}}, "type": "object", "required": ["remove_document_file"], "title": "RemoveDocumentFileBean"}, "RepoDocumentListFileBean": {"properties": {"repo_document_list_file": {"type": "string", "title": "Repo Document List File"}}, "type": "object", "required": ["repo_document_list_file"], "title": "RepoDocumentListFileBean"}, "ResultBean": {"properties": {"status": {"type": "string", "title": "Status", "description": "Status du traitement : ok ou ko"}, "error": {"type": "string", "title": "Error", "description": "Message d'erreur si le status est ko"}}, "type": "object", "required": ["status", "error"], "title": "ResultBean", "description": "Résultat d'un appel d'api"}, "SourceBean": {"properties": {"id": {"type": "integer", "title": "Id", "description": "Identifiant de la source"}, "code": {"type": "string", "title": "Code", "description": "Code de la source : utilisé comme répertoire de travail"}, "label": {"type": "string", "title": "Label", "description": "Label de la source"}, "src_type": {"type": "string", "title": "Src Type", "description": "Type de la source : associé au type de loader à utiliser"}, "configuration": {"type": "string", "title": "Configuration", "description": "Configuration json de la source pour le loader"}, "last_load_time": {"type": "integer", "title": "Last Load Time", "description": "Timestamp de dernier chargement des documents pour cette source"}, "load_interval": {"type": "integer", "title": "Load Interval", "description": "<PERSON><PERSON><PERSON> en heure entre deux chargements pour cette source"}, "domain_code": {"type": "string", "title": "Domain Code", "description": "Code du domaine auquel la source est rattaché"}, "perimeter_code": {"type": "string", "title": "Perimeter Code", "description": "Code du périmètre : relié à la base vecteur à utiliser"}, "force_embedding": {"type": "boolean", "title": "Force Embedding", "description": "Permet de forcer recuperation et embedding de tous les docs de la source", "default": false}}, "type": "object", "required": ["id", "code", "label", "src_type", "configuration", "last_load_time", "load_interval", "domain_code", "perimeter_code"], "title": "SourceBean", "description": "Représentation d'une source de documents"}, "TreatmentBean": {"properties": {"file_type": {"type": "string", "title": "File Type", "description": "Type de traitement : getlist, list, getdoc, removedoc, docs"}, "url": {"type": "string", "title": "Url", "description": "Url relative à appeler : contient l'action à réaliser et le périmètre"}, "params": {"type": "object", "title": "Params", "description": "Paramètres à passer à l'url : contient le fichier à traiter"}}, "type": "object", "required": ["file_type", "url", "params"], "title": "TreatmentBean", "description": "Représentation d'un appel url pour realiser un traitement dans la chaine de récupération des documents"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}, "tags": [{"name": "schedule", "description": "Récupération des url à appeler pour lancer les traitements à réaliser", "summary": "Récupération des url à appeler pour lancer les traitements à réaliser"}, {"name": "sources", "description": "Récupération des sources à charger", "summary": "Récupération des sources à charger"}, {"name": "loader", "description": "Appels du loader associé à la source à charger\n                pour lister les documents de la source et les récupérer sur GCP", "summary": "Appels du loader associé à la source à charger\n                pour lister les documents de la source et les récupérer sur GCP"}, {"name": "document", "description": "Gestion de l'embedding des documents", "summary": "Gestion de l'embedding des documents"}]}