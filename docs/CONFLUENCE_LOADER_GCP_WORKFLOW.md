# Confluence Loader - GCP Workflow Documentation

## Overview

The ConfluenceLoader is designed to operate within the kbot-loader-scheduler GCP architecture, providing automated document retrieval from Confluence instances with production-grade reliability and performance.

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Cloud Scheduler │───▶│ Cloud Run        │───▶│ Google Cloud    │
│ (Periodic       │    │ (kbot-loader)    │    │ Storage (GCS)   │
│  Triggers)      │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │                          │
                              ▼                          │
                       ┌──────────────────┐             │
                       │ Secret Manager   │             │
                       │ (Confluence      │             │
                       │  Credentials)    │             │
                       └──────────────────┘             │
                              │                          │
                              ▼                          ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │ Confluence       │    │ Document        │
                       │ Server/Cloud     │    │ Metadata &      │
                       │                  │    │ Content         │
                       └──────────────────┘    └─────────────────┘
```

## Workflow Phases

### Phase 1: Scheduler Trigger

**Endpoint**: `GET /schedule/treatments/{perimeter_code}/{date}`

Google Cloud Scheduler triggers the kbot-loader-scheduler service which:
1. Connects to kbot-back API to get perimeter configurations
2. Scans GCS for treatment files that need processing
3. Identifies "getlist" files for Confluence sources

### Phase 2: Document List Retrieval

**Endpoint**: `POST /loader/list/{perimeter_code}`

For each Confluence source:
1. **Read Configuration**: Loads JSON config from GCS "getlist" file
2. **Parse Criteria**: Processes filtering options (spaces, labels, dates, etc.)
3. **Connect to Confluence**: Uses credentials from Secret Manager
4. **Retrieve Documents**: Calls `get_document_list()` with filtering
5. **Save Results**: Stores document list in GCS "list" file

### Phase 3: Document Download

**Endpoint**: `POST /loader/document/{perimeter_code}` (called per document)

For each document in the list:
1. **Read Request**: Loads "getdoc" file containing document ID
2. **Download Content**: Calls `get_document()` to retrieve file
3. **Store on GCS**: Saves content with comprehensive metadata
4. **Update Status**: Marks document as processed

## Configuration Examples

### Basic Configuration (Legacy Format)

```json
{
    "space": "DOCUMENTATION",
    "include_attachments": true,
    "confluence_url": "https://confluence.company.com"
}
```

### Advanced Configuration (Production Format)

```json
{
    "spaces": ["DOCS", "WIKI", "SUPPORT"],
    "labels": ["public", "documentation"],
    "exclude_labels": ["draft", "private", "deprecated"],
    "last_modified_days": 30,
    "file_extensions": ["pdf", "docx", "xlsx", "png", "jpg"],
    "export_format": "markdown",
    "max_results": 1000,
    "parallel_downloads": true,
    "max_parallel_workers": 4,
    "enable_caching": true,
    "cache_ttl_minutes": 60,
    "retry_attempts": 3,
    "retry_delay_seconds": 2,
    "circuit_breaker_threshold": 5,
    "include_child_pages": true,
    "child_page_depth": 2,
    "custom_cql": "space in ('DOCS','WIKI') AND lastModified >= '-30d' AND label in ('public','documentation')"
}
```

## GCS File Structure

```
gs://kbot-loader-[perimeter_code]/
├── ************/                    # Timestamp: YYYYMMDDHHMM
│   ├── mycompany/                   # Domain code
│   │   ├── confluence_docs/         # Source code
│   │   │   ├── getlist/
│   │   │   │   └── ************_mycompany_confluence_docs_getlist.json
│   │   │   ├── list/
│   │   │   │   └── ************_mycompany_confluence_docs_list.json
│   │   │   ├── getdoc/
│   │   │   │   ├── ************_mycompany_confluence_docs_getdoc_123456.json
│   │   │   │   ├── ************_mycompany_confluence_docs_getdoc_789012.json
│   │   │   │   └── ************_mycompany_confluence_docs_getdoc_att345678.json
│   │   │   └── docs/
│   │   │       ├── important-documentation.md
│   │   │       ├── api-reference-guide.html
│   │   │       ├── architecture-diagram.png
│   │   │       └── user-manual.pdf
```

## Document ID Format

The ConfluenceLoader uses a standardized ID format for tracking documents:

- **Format**: `{domain_code}|{source_code}|{confluence_id}`
- **Pages**: `mycompany|confluence_docs|123456`
- **Attachments**: `mycompany|confluence_docs|att123456`

## Metadata Schema

Each downloaded document includes comprehensive metadata:

```json
{
    "document_id": "mycompany|confluence_docs|123456",
    "document_name": "API Documentation",
    "location": "gs://kbot-loader-prod/************/mycompany/confluence_docs/docs/api-documentation.md",
    "creationTime": "2024-01-15T10:30:45.123Z",
    "modificationDate": "2024-06-13T14:30:00.000Z",
    "export_format": "markdown",
    "source_url": "https://confluence.company.com/display/DOCS/API+Documentation",
    "space_key": "DOCS",
    "page_id": "123456",
    "labels": ["api", "documentation", "public"]
}
```

## Performance Features

### Parallel Processing

- **Multiple Spaces**: Processed concurrently when `parallel_downloads=true`
- **Document Downloads**: Concurrent downloads within worker limits
- **Configurable Workers**: Control concurrency with `max_parallel_workers`

### Caching Strategy

- **In-Memory Cache**: Reduces API calls during processing
- **TTL-Based**: Configurable cache lifetime (`cache_ttl_minutes`)
- **Automatic Cleanup**: Expired entries removed automatically

### Error Handling

- **Circuit Breaker**: Protects against cascading failures
- **Automatic Retry**: Exponential backoff for transient errors
- **Detailed Logging**: Comprehensive error tracking for troubleshooting

### Metrics Collection

```json
{
    "total_execution_time": 245.67,
    "pages_processed": 156,
    "attachments_processed": 89,
    "total_documents": 245,
    "errors_count": 3,
    "cache_hits": 78,
    "cache_misses": 167,
    "cache_hit_rate": 0.318,
    "average_download_time": 1.23,
    "average_processing_time": 0.98,
    "documents_per_second": 1.0
}
```

## Secret Manager Integration

Confluence credentials are stored in Google Secret Manager:

```json
{
    "confluence_url": "https://confluence.company.com",
    "pat_token": "your-personal-access-token"
}
```

## Cloud Run Considerations

### Memory and CPU

- **Memory**: 2GB recommended for large spaces
- **CPU**: 2 vCPUs for optimal parallel processing
- **Timeout**: 900 seconds (15 minutes) for large document sets

### Environment Variables

```bash
CONFLUENCE_URL=https://confluence.company.com
GCS_BUCKET_TEMPLATE=kbot-loader-[perimeter_code]
LOG_LEVEL=INFO
PARALLEL_WORKERS=4
```

## Monitoring and Alerting

### Cloud Monitoring Metrics

- Document processing rate
- Error rates by source
- Cache hit ratios
- Download latencies

### Alert Conditions

- High error rates (>5%)
- Processing timeouts
- Credential authentication failures
- GCS storage failures

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Check Secret Manager credentials
   - Verify Confluence URL accessibility
   - Validate PAT token permissions

2. **Timeout Errors**
   - Reduce `max_results` in configuration
   - Increase Cloud Run timeout
   - Enable parallel processing

3. **Missing Documents**
   - Check filtering criteria (labels, dates)
   - Verify space access permissions
   - Review CQL query syntax

### Debug Configuration

For troubleshooting, use this configuration:

```json
{
    "spaces": ["TEST"],
    "max_results": 10,
    "enable_metrics": true,
    "parallel_downloads": false,
    "retry_attempts": 1,
    "enable_caching": false
}
```

## Best Practices

1. **Configuration Management**
   - Use specific spaces rather than organization-wide scanning
   - Apply appropriate filters to reduce API load
   - Configure reasonable `max_results` limits

2. **Performance Optimization**
   - Enable parallel processing for multiple spaces
   - Use caching for repeated operations
   - Monitor and tune worker counts

3. **Error Handling**
   - Set appropriate retry limits
   - Configure circuit breaker thresholds
   - Monitor error rates and patterns

4. **Security**
   - Store credentials in Secret Manager only
   - Use service accounts with minimal permissions
   - Regularly rotate access tokens
