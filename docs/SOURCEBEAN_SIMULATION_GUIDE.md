# Guide de Simulation des SourceBean pour kbot-load-scheduler

## 📋 Vue d'Ensemble

Ce guide détaille comment simuler fidèlement les objets `SourceBean` dans l'environnement local pour reproduire le comportement de production de kbot-load-scheduler, particulièrement pour le module Confluence.

## 🎯 Objectif

Les `SourceBean` représentent les sources de données dans kbot-load-scheduler. En production, ils sont fournis par l'API kbot-back avec une configuration JSON complexe. Pour les tests locaux, nous devons les simuler avec précision pour valider le comportement du système.

## 📚 Structure du SourceBean

### Champs Obligatoires

```python
from kbotloadscheduler.bean.beans import SourceBean

source_bean = SourceBean(
    id=1,                           # ID unique de la source
    code="confluence_test",         # Code unique de la source
    label="Test Confluence",        # Libellé descriptif
    src_type="confluence",          # Type de source (confluence, gcs, sharepoint, basic)
    configuration='{"json": "config"}',  # Configuration JSON (clé principale)
    last_load_time=1234567890,      # Timestamp Unix du dernier chargement
    load_interval=24,               # Intervalle de rechargement (heures)
    domain_code="test_domain",      # Code du domaine
    perimeter_code="test_perimeter", # Code du périmètre
    force_embedding=False           # Force le recalcul des embeddings
)
```

### Configuration JSON pour Confluence

Le champ `configuration` contient une chaîne JSON avec la structure suivante :

```json
{
  "spaces": ["DOCS", "TECH"],
  "max_results": 100,
  "include_attachments": true,
  "content_types": ["page", "blogpost"],
  "labels": ["public", "api"],
  "last_modified_days": 30,
  "title_contains": "API",
  "exclude_labels": ["draft", "internal"],
  "exclude_spaces": ["PRIVATE"],
  "parallel_pagination": {
    "enabled": true,
    "batch_size": 20,
    "max_workers": 5
  },
  "processing": {
    "enable_change_tracking": true,
    "max_attachment_size_mb": 10,
    "allowed_extensions": [".pdf", ".docx", ".txt"],
    "chunk_size": 1000,
    "chunk_overlap": 200
  },
  "storage": {
    "type": "gcs",
    "bucket": "confluence-data",
    "prefix": "confluence-sync",
    "raw_downloads": false
  }
}
```

## 🔧 Méthodes de Simulation

### 1. Fixtures Pytest (Recommandé)

#### Fixture Basique

```python
import pytest
import json
from datetime import datetime, timezone
from kbotloadscheduler.bean.beans import SourceBean

@pytest.fixture
def confluence_source_bean():
    """Source bean de base pour Confluence"""
    config = {
        "spaces": ["TEST"],
        "max_results": 50,
        "include_attachments": True,
        "content_types": ["page", "blogpost"]
    }
    
    return SourceBean(
        id=1,
        code="test_confluence",
        label="Test Confluence Source",
        src_type="confluence",
        configuration=json.dumps(config),
        last_load_time=int(datetime.now(timezone.utc).timestamp()),
        load_interval=24,
        domain_code="test_domain",
        perimeter_code="test_perimeter",
        force_embedding=False,
    )
```

#### Fixture Paramétrisée

```python
@pytest.fixture(params=[
    # Configuration minimale
    {
        "spaces": ["DOCS"],
        "max_results": 10
    },
    # Configuration complète
    {
        "spaces": ["DOCS", "TECH"],
        "max_results": 100,
        "include_attachments": True,
        "content_types": ["page", "blogpost"],
        "labels": ["public"],
        "processing": {
            "enable_change_tracking": True,
            "chunk_size": 1000
        }
    },
    # Configuration avec stockage GCS
    {
        "spaces": ["PROD"],
        "max_results": 200,
        "storage": {
            "type": "gcs",
            "bucket": "test-bucket",
            "prefix": "confluence-data"
        }
    }
])
def parametrized_confluence_source(request):
    """Source bean paramétrisé pour différents scénarios"""
    return SourceBean(
        id=request.param.get("id", 1),
        code=f"confluence_{hash(str(request.param)) % 1000}",
        label="Parametrized Confluence Source",
        src_type="confluence",
        configuration=json.dumps(request.param),
        last_load_time=int(datetime.now(timezone.utc).timestamp()),
        load_interval=24,
        domain_code="test_domain",
        perimeter_code="test_perimeter",
        force_embedding=False,
    )
```

### 2. Factory Pattern

#### SourceBean Factory

```python
class SourceBeanFactory:
    """Factory pour créer des SourceBean standardisés"""
    
    @staticmethod
    def create_confluence_source(
        spaces: list = None,
        max_results: int = 100,
        include_attachments: bool = True,
        **kwargs
    ) -> SourceBean:
        """Crée un SourceBean pour Confluence avec configuration par défaut"""
        
        config = {
            "spaces": spaces or ["TEST"],
            "max_results": max_results,
            "include_attachments": include_attachments,
            "content_types": ["page", "blogpost"]
        }
        
        # Merger avec les kwargs
        config.update(kwargs.get("config_overrides", {}))
        
        return SourceBean(
            id=kwargs.get("id", 1),
            code=kwargs.get("code", "test_confluence"),
            label=kwargs.get("label", "Test Confluence Source"),
            src_type="confluence",
            configuration=json.dumps(config),
            last_load_time=kwargs.get("last_load_time", int(datetime.now(timezone.utc).timestamp())),
            load_interval=kwargs.get("load_interval", 24),
            domain_code=kwargs.get("domain_code", "test_domain"),
            perimeter_code=kwargs.get("perimeter_code", "test_perimeter"),
            force_embedding=kwargs.get("force_embedding", False),
        )
    
    @staticmethod
    def create_production_like_source() -> SourceBean:
        """Crée un SourceBean similaire à la production"""
        config = {
            "spaces": ["ENGINEERING", "PRODUCT", "SUPPORT"],
            "max_results": 1000,
            "include_attachments": True,
            "content_types": ["page", "blogpost"],
            "labels": ["public", "external"],
            "exclude_labels": ["draft", "internal", "confidential"],
            "last_modified_days": 90,
            "parallel_pagination": {
                "enabled": True,
                "batch_size": 50,
                "max_workers": 3
            },
            "processing": {
                "enable_change_tracking": True,
                "max_attachment_size_mb": 25,
                "allowed_extensions": [".pdf", ".docx", ".txt", ".md"],
                "chunk_size": 1500,
                "chunk_overlap": 300
            },
            "storage": {
                "type": "gcs",
                "bucket": "confluence-production-data",
                "prefix": "sync-data/engineering",
                "raw_downloads": False
            }
        }
        
        return SourceBean(
            id=1001,
            code="confluence_engineering_prod",
            label="Confluence Engineering - Production",
            src_type="confluence",
            configuration=json.dumps(config),
            last_load_time=int(datetime.now(timezone.utc).timestamp()) - 3600,  # 1h ago
            load_interval=6,  # Every 6 hours
            domain_code="engineering",
            perimeter_code="production",
            force_embedding=False,
        )
```

### 3. Templates de Configuration

#### Templates JSON Réutilisables

```python
class ConfluenceConfigTemplates:
    """Templates de configuration pour différents scénarios"""
    
    MINIMAL = {
        "spaces": ["TEST"],
        "max_results": 10
    }
    
    DEVELOPMENT = {
        "spaces": ["DEV", "STAGING"],
        "max_results": 100,
        "include_attachments": True,
        "content_types": ["page"],
        "labels": ["dev-ready"],
        "processing": {
            "enable_change_tracking": False,  # Simplifié pour le dev
            "chunk_size": 500
        }
    }
    
    INTEGRATION_TEST = {
        "spaces": ["TEST", "INTEGRATION"],
        "max_results": 50,
        "include_attachments": True,
        "content_types": ["page", "blogpost"],
        "labels": ["test", "public"],
        "last_modified_days": 7,
        "processing": {
            "enable_change_tracking": True,
            "max_attachment_size_mb": 5,
            "allowed_extensions": [".pdf", ".txt"],
            "chunk_size": 1000,
            "chunk_overlap": 200
        },
        "storage": {
            "type": "filesystem",  # Plus simple pour les tests
            "path": "/tmp/confluence-test"
        }
    }
    
    PRODUCTION_LIKE = {
        "spaces": ["DOCS", "API", "SUPPORT", "PRODUCT"],
        "max_results": 1000,
        "include_attachments": True,
        "content_types": ["page", "blogpost"],
        "labels": ["public", "external", "customer-facing"],
        "exclude_labels": ["draft", "internal", "confidential", "deprecated"],
        "exclude_spaces": ["PRIVATE", "HR", "FINANCE"],
        "last_modified_days": 30,
        "title_contains": None,
        "parallel_pagination": {
            "enabled": True,
            "batch_size": 25,
            "max_workers": 4
        },
        "processing": {
            "enable_change_tracking": True,
            "max_attachment_size_mb": 20,
            "allowed_extensions": [".pdf", ".docx", ".txt", ".md", ".xlsx"],
            "chunk_size": 1200,
            "chunk_overlap": 240,
            "extract_images": False,
            "sanitize_html": True
        },
        "storage": {
            "type": "gcs",
            "bucket": "confluence-production-sync",
            "prefix": "sync-data",
            "raw_downloads": False,
            "compression": True
        },
        "retry": {
            "max_attempts": 3,
            "backoff_factor": 2.0,
            "timeout_seconds": 30
        },
        "circuit_breaker": {
            "failure_threshold": 5,
            "timeout_seconds": 60,
            "expected_errors": ["RateLimitExceededError", "AuthenticationError"]
        }
    }

def create_source_from_template(template_name: str, overrides: dict = None) -> SourceBean:
    """Crée un SourceBean à partir d'un template"""
    template = getattr(ConfluenceConfigTemplates, template_name.upper())
    config = template.copy()
    
    if overrides:
        config.update(overrides)
    
    return SourceBean(
        id=1,
        code=f"confluence_{template_name.lower()}",
        label=f"Confluence {template_name.title()}",
        src_type="confluence",
        configuration=json.dumps(config),
        last_load_time=int(datetime.now(timezone.utc).timestamp()),
        load_interval=24,
        domain_code="test_domain",
        perimeter_code="test_perimeter",
        force_embedding=False,
    )
```

## 🧪 Exemples d'Utilisation dans les Tests

### Test avec Fixture Simple (Confluence)

```python
def test_confluence_loader_initialization(confluence_source_bean):
    """Test l'initialisation du loader avec un SourceBean"""
    from kbotloadscheduler.loader.confluence.confluence_loader import ConfluenceLoader
    
    # Mock des dépendances
    with patch('kbotloadscheduler.container.ConfigWithSecret') as mock_config:
        mock_config.get_confluence_credentials.return_value = {
            "pat_token": "test_token"
        }
        
        loader = ConfluenceLoader(confluence_source_bean, mock_config)
        assert loader.source == confluence_source_bean
        
        # Vérifier que la configuration JSON est bien parsée
        parsed_config = loader.confluence_config
        assert "spaces" in parsed_config
        assert parsed_config["spaces"] == ["TEST"]
```

### Exemple Réel SharePoint (Pattern Existant) ⭐

Les tests SharePoint montrent déjà un excellent pattern de simulation :

```python
def test_sharepoint_loader_with_mock_secrets(mocker, requests_mock):
    """Exemple réel tiré des tests SharePoint existants"""
    # 1. Simulation du ConfigWithSecret
    config = providers.Configuration()
    config.env.from_env("ENV", "local")
    config_with_secret = ConfigWithSecret(config=config)
    
    # Mock des méthodes de récupération de secrets
    config_with_secret.get_sharepoint_client_config = MagicMock(
        return_value=json.loads(SharepointRepoTestData.FAKE_CLIENT_CONFIG)
    )
    config_with_secret.get_sharepoint_client_private_key = MagicMock(
        return_value="private_key"
    )
    
    # Mock du token d'accès
    def get_token(obj):
        return "fake_token"
    mocker.patch.object(sharepoint_loader.SharepointCredentials, "get_access_token", get_token)
    
    # 2. Simulation du SourceBean avec données de test
    source = SourceBean(**SharepointRepoTestData.SOURCE)
    
    # 3. Simulation GCS
    mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
    mock_gcs.add_bucket("fakebucket")
    
    # 4. Mock des appels HTTP SharePoint
    requests_mock.get("url_pattern", json=SharepointRepoTestData.GET_FOLDER_RESPONSE)
    
    # 5. Test du loader
    loader = SharepointLoader(config_with_secret)
    result = loader.get_document_list(source)
    
    assert len(result) > 0
```

### Test avec Factory

```python
def test_confluence_loader_with_production_config():
    """Test avec une configuration similaire à la production"""
    source = SourceBeanFactory.create_production_like_source()
    
    with patch('kbotloadscheduler.container.ConfigWithSecret') as mock_config:
        mock_config.get_confluence_credentials.return_value = {
            "pat_token": "test_token"
        }
        
        loader = ConfluenceLoader(source, mock_config)
        
        # Vérifier les paramètres de production
        config = loader.confluence_config
        assert config["max_results"] == 1000
        assert "parallel_pagination" in config
        assert config["parallel_pagination"]["enabled"] is True
        assert config["processing"]["enable_change_tracking"] is True
```

### Test avec Template et Overrides

```python
def test_confluence_loader_custom_storage():
    """Test avec override de configuration de stockage"""
    storage_override = {
        "storage": {
            "type": "gcs",
            "bucket": "custom-test-bucket",
            "prefix": "custom-prefix"
        }
    }
    
    source = create_source_from_template("integration_test", storage_override)
    
    with patch('kbotloadscheduler.container.ConfigWithSecret') as mock_config:
        mock_config.get_confluence_credentials.return_value = {
            "pat_token": "test_token"
        }
        
        loader = ConfluenceLoader(source, mock_config)
        config = loader.confluence_config
        
        assert config["storage"]["bucket"] == "custom-test-bucket"
        assert config["storage"]["prefix"] == "custom-prefix"
```

## 🎛️ Configuration d'Environnement

### Variables d'Environnement pour les Tests

```python
import os
import pytest
from unittest.mock import patch

@pytest.fixture(autouse=True)
def setup_test_environment():
    """Configure l'environnement de test"""
    test_env = {
        "CONFLUENCE_URL": "https://test-confluence.example.com",
        "DEFAULT_SPACE_KEY": "TEST",
        "CONFLUENCE_TIMEOUT": "30",
        "GCS_BUCKET": "test-bucket",
        "ENVIRONMENT": "test"
    }
    
    with patch.dict(os.environ, test_env):
        yield
```

### Mock des Secrets et Credentials

```python
@pytest.fixture
def mock_config_with_secret():
    """Mock du ConfigWithSecret pour les tests"""
    from unittest.mock import MagicMock
    
    config_mock = MagicMock()
    config_mock.get_confluence_credentials.return_value = {
        "pat_token": "test_pat_token_12345",
        "username": "<EMAIL>",
        "api_token": "test_api_token_67890"
    }
    return config_mock
```

## 📊 Validation des SourceBean

### Fonction de Validation

```python
def validate_source_bean_for_confluence(source_bean: SourceBean) -> tuple[bool, list[str]]:
    """Valide qu'un SourceBean est correct pour Confluence"""
    errors = []
    
    # Validation des champs obligatoires
    if not source_bean.code:
        errors.append("Le code source est obligatoire")
    
    if source_bean.src_type != "confluence":
        errors.append(f"Type de source incorrect: {source_bean.src_type} (attendu: confluence)")
    
    # Validation de la configuration JSON
    try:
        config = json.loads(source_bean.configuration)
    except json.JSONDecodeError as e:
        errors.append(f"Configuration JSON invalide: {e}")
        return False, errors
    
    # Validation de la structure de configuration
    if "spaces" not in config:
        errors.append("Le champ 'spaces' est obligatoire dans la configuration")
    
    if not isinstance(config.get("spaces"), list):
        errors.append("Le champ 'spaces' doit être une liste")
    
    if "max_results" in config and not isinstance(config["max_results"], int):
        errors.append("Le champ 'max_results' doit être un entier")
    
    return len(errors) == 0, errors

# Usage
source = SourceBeanFactory.create_confluence_source()
is_valid, validation_errors = validate_source_bean_for_confluence(source)
assert is_valid, f"SourceBean invalide: {validation_errors}"
```

## 🔄 Intégration avec MockConfluence

### Combinaison SourceBean + MockConfluence

```python
def test_end_to_end_with_mock_confluence():
    """Test end-to-end avec SourceBean et MockConfluence"""
    from tests.testutils.mock_confluence import MockConfluence, setup_sample_confluence_data
    
    # 1. Créer un SourceBean réaliste
    source = SourceBeanFactory.create_confluence_source(
        spaces=["DOCS", "TECH"],
        max_results=100,
        config_overrides={
            "include_attachments": True,
            "content_types": ["page", "blogpost"],
            "labels": ["public"]
        }
    )
    
    # 2. Configurer MockConfluence avec des données correspondantes
    mock_confluence = MockConfluence()
    setup_sample_confluence_data(mock_confluence)
    
    # 3. Vérifier la cohérence
    config = json.loads(source.configuration)
    mock_results = mock_confluence.search_content(
        spaces=config["spaces"],
        max_results=config["max_results"]
    )
    
    assert len(mock_results) > 0
    assert all(result["space"]["key"] in config["spaces"] for result in mock_results)
```

## 📚 Patterns Éprouvés dans la Codebase

### 1. Pattern SharePoint (Référence Existante) ⭐

Le projet dispose déjà d'un excellent exemple de simulation avec les tests SharePoint :

#### Structure des Données de Test
```python
# tests/data/sharepoint_repo_test_data.py
class SharepointRepoTestData:
    CONF_SOURCE = {
        "site_name": "EquipeIAGDCCM",
        "relative_directory": "Documents partages/Test Knowledge Bot",
    }
    
    SOURCE = {
        "id": 1,
        "code": "srcA1",
        "label": "source A 1",
        "src_type": "sharepoint",
        "configuration": json.dumps(CONF_SOURCE),
        "last_load_time": **********,
        "load_interval": "24",
        "domain_code": "domA",
        "perimeter_code": "perimB",
    }
    
    FAKE_CLIENT_CONFIG = """
    {"tenant_id": "tenant", "client_id": "client_id", "certificate_thumbprint": "ct", "password": "pwd"}
    """
```

#### Simulation Complète (Secret Manager + SourceBean + GCS)
```python
def __init_config__(mocker):
    """Pattern éprouvé pour simuler ConfigWithSecret"""
    config = providers.Configuration()
    config.env.from_env("ENV", "local")
    config_with_secret = ConfigWithSecret(config=config)
    
    # Mock des secrets SharePoint
    config_with_secret.get_sharepoint_client_config = MagicMock(
        return_value=json.loads(SharepointRepoTestData.FAKE_CLIENT_CONFIG)
    )
    config_with_secret.get_sharepoint_client_private_key = MagicMock(
        return_value="private_key"
    )
    
    # Mock des credentials dynamiques
    def get_token(obj):
        return "fake_token"
    mocker.patch.object(sharepoint_loader.SharepointCredentials, "get_access_token", get_token)
    
    return config_with_secret

# Usage dans les tests
def test_sharepoint_functionality(mocker, requests_mock):
    config_with_secret = __init_config__(mocker)
    source = SourceBean(**SharepointRepoTestData.SOURCE)
    
    # Simulation GCS
    mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
    mock_gcs.add_bucket("fakebucket")
    
    # Test du loader
    loader = SharepointLoader(config_with_secret)
    result = loader.get_document_list(source)
    
    assert len(result) > 0
```

### 2. Adaptation pour Confluence

En s'inspirant du pattern SharePoint, voici comment adapter pour Confluence :

```python
# tests/data/confluence_repo_test_data.py (à créer)
class ConfluenceRepoTestData:
    CONF_SOURCE = {
        "spaces": ["DOCS", "TECH"],
        "max_results": 100,
        "include_attachments": True,
        "content_types": ["page", "blogpost"]
    }
    
    SOURCE = {
        "id": 1,
        "code": "confluence_test",
        "label": "Test Confluence Source",
        "src_type": "confluence",
        "configuration": json.dumps(CONF_SOURCE),
        "last_load_time": **********,
        "load_interval": 24,
        "domain_code": "test_domain",
        "perimeter_code": "test_perimeter",
        "force_embedding": False,
    }

def init_confluence_config(mocker):
    """Pattern inspiré de SharePoint pour Confluence"""
    config = providers.Configuration()
    config.env.from_env("ENV", "local") 
    config_with_secret = ConfigWithSecret(config=config)
    
    # Mock des credentials Confluence
    config_with_secret.get_confluence_credentials = MagicMock(
        return_value={
            "pat_token": "test_pat_token",
            "username": "<EMAIL>"
        }
    )
    
    return config_with_secret
```

### 3. Bonnes Pratiques Validées

**S'inspirer des patterns existants** :
- Les tests SharePoint montrent la voie à suivre
- Pattern de simulation du `ConfigWithSecret` éprouvé
- Intégration harmonieuse avec `MockGcs`
- Structure de données de test organisée

## 🚨 Pièges à Éviter

### 1. JSON mal formaté

```python
# ❌ Incorrect
configuration = "{'spaces': ['TEST']}"  # Simple quotes

# ✅ Correct  
configuration = '{"spaces": ["TEST"]}'  # JSON valide
```

### 2. Types de données incorrects

```python
# ❌ Incorrect
config = {"max_results": "100"}  # String au lieu d'int

# ✅ Correct
config = {"max_results": 100}  # Integer
```

### 3. Timestamps incorrects

```python
# ❌ Incorrect
last_load_time = "2024-01-01"  # String au lieu de timestamp

# ✅ Correct
last_load_time = int(datetime.now(timezone.utc).timestamp())
```

## 📋 Checklist de Validation

Avant d'utiliser un SourceBean simulé :

- [ ] Le JSON de configuration est-il valide ?
- [ ] Les champs obligatoires sont-ils présents ?
- [ ] Les types de données sont-ils corrects ?
- [ ] La configuration est-elle cohérente avec le type de source ?
- [ ] Les mocks correspondants sont-ils configurés ?
- [ ] Les variables d'environnement sont-elles définies ?
- [ ] Les credentials sont-ils mockés ?

## 🎯 Conclusion

Vous avez absolument raison ! **Les tests SharePoint dans le projet utilisent déjà efficacement** la simulation du secret manager et des SourceBean, fournissant un excellent modèle à suivre.

### 🏆 Patterns Validés Existants

1. **Simulation ConfigWithSecret** : Pattern `__init_config__` éprouvé
2. **Données de test structurées** : `SharepointRepoTestData` comme référence
3. **Intégration MockGCS** : Simulation harmonieuse du stockage
4. **SourceBean standardisés** : Structure de données cohérente

### 📋 Recommandations Finales

1. **Réutilisez les patterns SharePoint** existants pour Confluence
2. **Adaptez `SharepointRepoTestData`** en `ConfluenceRepoTestData`
3. **Suivez la structure `__init_config__`** pour les mocks de secrets
4. **Intégrez avec les utilitaires existants** (`MockConfluence`, `MockGCS`)

La simulation fidèle des SourceBean est cruciale pour tester efficacement kbot-load-scheduler. Le projet dispose déjà d'excellents exemples avec SharePoint qu'il suffit d'adapter pour les autres loaders.

Références supplémentaires :
- [Configuration JSON Documentation](./CONFIGURATION_JSON.md)
- [Test Utilities README](../tests/testutils/README.md)
- [Integration Tests Guide](../tests/loader/confluence/README_INTEGRATION.md)
- **Tests SharePoint** : `tests/loader/sharepoint/test_sharepoint_loader.py` ⭐
