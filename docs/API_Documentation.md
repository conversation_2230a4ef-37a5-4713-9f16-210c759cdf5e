# 📚 Documentation des API FastAPI - kbot-load-scheduler

## Vue d'ensemble

Le **kbot-load-scheduler** expose une API REST basée sur FastAPI pour automatiser l'embedding des documents. L'API est organisée en 4 groupes principaux de routes qui orchestrent le workflow complet de traitement des documents.

## 🏗️ Architecture des API

```mermaid
graph TD
    A[Sources API] --> B[Loader API]
    B --> C[Document API]
    C --> D[Schedule API]

    A --> E[Configuration des sources]
    B --> F[Récupération des documents]
    C --> G[Embedding & Suppression]
    D --> H[Orchestration globale]
```

---

## 1. 🗂️ **Sources Routes** (`/sources/*`)

### Description
Gère la configuration et le chargement des sources de données (Confluence, SharePoint, GCS, etc.).

### **POST `/sources/loadall`**

**Objectif** : Lancement de la récupération des documents pour toutes les sources de tous les périmètres

**Description technique** : Cette API permet de charger et traiter automatiquement l'ensemble des sources configurées, quel que soit leur périmètre. Elle initialise le processus de récupération des documents qui seront ensuite disponibles pour l'embedding.

**Headers** :
```http
Content-Type: application/json
```

**Corps de la requête** : Aucun

**Réponse** (`SourceBean[]`) :
```json
[
  {
    "id": 1,
    "code": "confluence_docs",
    "label": "Documentation Confluence",
    "src_type": "confluence",
    "configuration": "{\"spaces\":[\"DOCS\",\"API\"],\"include_attachments\":true}",
    "last_load_time": 1702819200,
    "load_interval": 24,
    "domain_code": "engineering",
    "perimeter_code": "production",
    "force_embedding": false
  }
]
```

**Description des champs de réponse** :
- `id` (integer, requis) : Identifiant de la source
- `code` (string, requis) : Code de la source, utilisé comme répertoire de travail
- `label` (string, requis) : Label de la source
- `src_type` (string, requis) : Type de la source, associé au type de loader à utiliser
- `configuration` (string, requis) : Configuration JSON de la source pour le loader
- `last_load_time` (integer, requis) : Timestamp de dernier chargement des documents pour cette source
- `load_interval` (integer, requis) : Délai en heure entre deux chargements pour cette source
- `domain_code` (string, requis) : Code du domaine auquel la source est rattachée
- `perimeter_code` (string, requis) : Code du périmètre, relié à la base vecteur à utiliser
- `force_embedding` (boolean, optionnel, défaut: false) : Permet de forcer récupération et embedding de tous les docs de la source

**Exemple d'appel** :
```bash
curl -X POST "http://localhost:8092/sources/loadall" \
     -H "Content-Type: application/json"
```

### **POST `/sources/load/{perimeter_code}`**

**Objectif** : Lancement de la récupération des documents pour toutes les sources du périmètre

**Description technique** : Cette API charge uniquement les sources associées à un périmètre spécifique. Le périmètre détermine quelles sources seront traitées et vers quelle base vecteur les documents seront dirigés.

**Paramètres** :
- `perimeter_code` (string, requis) : Code du périmètre pour lequel on veut remonter les sources (ex: "production", "ebotman", "mktsearch")

**Exemple d'appel** :
```bash
curl -X POST "http://localhost:8092/sources/load/production" \
     -H "Content-Type: application/json"
```

**Réponse** : Même format que `/sources/loadall` mais filtré par périmètre

---

## 2. 📥 **Loader Routes** (`/loader/*`)

### Description
Orchestre les appels aux loaders spécifiques pour récupérer les listes de documents et télécharger les documents individuels.

### **POST `/loader/list/{perimeter_code}`**

**Objectif** : Lancement de la récupération de la liste des documents présents dans la source

**Description technique** : Cette API utilise le fichier `getlist.json` (généré par `/sources/load`) pour identifier la source à traiter, puis appelle le loader correspondant pour récupérer la liste complète des documents disponibles. La réponse est également sauvegardée dans un fichier `list.json` sur GCS.

**Paramètres** :
- `perimeter_code` (string, requis) : Code du périmètre concerné

**Corps de la requête** :
```json
{
  "get_list_file": "gs://ofr-ekb-knowledgebot-work-dev-production-dev/**************/engineering/confluence_docs/getlist/getlist.json"
}
```

**⚠️ Format Important** : Le paramètre `get_list_file` doit contenir un **chemin GCS complet** vers un fichier JSON, pas le contenu JSON lui-même. Ce fichier est généré par l'API `/sources/load/{perimeter_code}`.

**Réponse** (`DocumentAnswerBean[]`) :
```json
[
  {
    "id": "engineering|confluence_docs|123456",
    "path": "/spaces/DOCS/page/Architecture-Guide",
    "name": "Architecture Guide",
    "modification_time": "**************"
  },
  {
    "id": "engineering|confluence_docs|att789012",
    "path": "/spaces/DOCS/attachments/diagram.png",
    "name": "Architecture Diagram",
    "modification_time": "**************"
  }
]
```

**Description des champs de réponse** :
- `id` (string, requis) : Identifiant externe du document
- `path` (string, requis) : Chemin relatif du document
- `name` (string, requis) : Nom ou chemin du document
- `modification_time` (string, requis) : Date et heure de dernière modification du document au format YYYYMMDDHHMMSS

**Exemple d'appel** :
```bash
curl -X POST "http://localhost:8092/loader/list/production" \
     -H "Content-Type: application/json" \
     -d '{
       "get_list_file": "gs://ofr-ekb-knowledgebot-work-dev-production-dev/**************/engineering/confluence_docs/getlist/getlist.json"
     }'
```

### **POST `/loader/document/{perimeter_code}`**

**Objectif** : Lancement de la récupération des documents d'une source

**Description technique** : Cette API utilise le fichier `*.getdoc.json` (généré par `/document/compare`) pour télécharger un document spécifique depuis sa source vers GCS. Elle génère à la fois le document et un fichier de métadonnées `.metadata.json` nécessaire pour l'embedding.

**Paramètres** :
- `perimeter_code` (string, requis) : Code du périmètre concerné

**Corps de la requête** :
```json
{
  "document_get_file": "gs://ofr-ekb-knowledgebot-work-dev-production-dev/**************/engineering/confluence_docs/getdoc/architecture-guide.getdoc.json"
}
```

**⚠️ Format Important** : Le paramètre `document_get_file` doit contenir un **chemin GCS complet** vers un fichier JSON, pas le contenu JSON lui-même. Ce fichier est généré par l'API `/document/compare/{perimeter_code}`.

**Réponse** (`DocumentWithMetadataBean`) :
```json
{
  "document": {
    "id": "engineering|confluence_docs|123456",
    "path": "/spaces/DOCS/page/Architecture-Guide",
    "name": "Architecture Guide",
    "modification_time": "**************"
  },
  "metadata": {
    "document_id": "engineering|confluence_docs|123456",
    "document_name": "Architecture Guide",
    "source_path": "/spaces/DOCS/page/Architecture-Guide",
    "location": "gs://bucket/**************/engineering/confluence_docs/docs/architecture-guide.md",
    "domain": "engineering",
    "source": "confluence_docs",
    "source_type": "confluence",
    "source_conf": "{\"spaces\":[\"DOCS\"]}",
    "creationDate": "**************",
    "modificationDate": "**************"
  }
}
```

**Description des champs de réponse** :
- `document` (DocumentAnswerBean, requis) : Le document récupéré
  - `id` (string, requis) : Identifiant externe du document
  - `path` (string, requis) : Chemin relatif du document
  - `name` (string, requis) : Nom ou chemin du document
  - `modification_time` (string, requis) : Date et heure de dernière modification au format YYYYMMDDHHMMSS
- `metadata` (object, requis) : Les métadonnées du document pour l'embedding

**Exemple d'appel** :
```bash
curl -X POST "http://localhost:8092/loader/document/production" \
     -H "Content-Type: application/json" \
     -d '{
       "document_get_file": "gs://ofr-ekb-knowledgebot-work-dev-production-dev/**************/engineering/confluence_docs/getdoc/architecture-guide.getdoc.json"
     }'
```

---

## 3. 🎯 **Document Routes** (`/document/*`)

### Description
Gère l'embedding des documents, la comparaison avec les documents existants et la suppression des documents obsolètes.

### **POST `/document/compare/{perimeter_code}`**

**Objectif** : Comparaison de la liste des documents sur le repo et de la liste des documents embeddés

**Description technique** : Compare les documents présents dans la source avec ceux déjà embeddés dans la base vecteur. La date de dernière modification est prise en compte et si la date de l'embedding est plus ancienne, alors le document est marqué à récupérer. Cette API génère les fichiers `*.getdoc.json` nécessaires pour le téléchargement des documents.

**Paramètres** :
- `perimeter_code` (string, requis) : Code du périmètre concerné

**Corps de la requête** :
```json
{
  "repo_document_list_file": "gs://ofr-ekb-knowledgebot-work-dev-production-dev/**************/engineering/confluence_docs/list/list.json"
}
```

**⚠️ Format Important** : Le paramètre `repo_document_list_file` doit contenir un **chemin GCS complet** vers un fichier JSON, pas le contenu JSON lui-même. Ce fichier est généré par l'API `/loader/list/{perimeter_code}`.

**Réponse** :
```json
[
  {
    "id": "engineering|confluence_docs|123456",
    "path": "/spaces/DOCS/page/Architecture-Guide",
    "name": "Architecture Guide",
    "modification_time": "**************"
  }
]
```

### **POST `/document/embedd/{perimeter_code}`**

**Objectif** : Lancement de l'embedding d'un document récupéré

**Description technique** : Cette API prend en charge l'embedding d'un document en utilisant le fichier de métadonnées `.metadata.json` généré par `/loader/document`. Elle communique avec l'API d'embedding pour ajouter le document dans la base vecteur associée au périmètre.

**Paramètres** :
- `perimeter_code` (string, requis) : Code du périmètre concerné

**Corps de la requête** :
```json
{
  "embedd_document_metadata_file": "gs://ofr-ekb-knowledgebot-work-dev-production-dev/**************/engineering/confluence_docs/docs/architecture-guide.md.metadata.json"
}
```

**⚠️ Format Important** : Le paramètre `embedd_document_metadata_file` doit contenir un **chemin GCS complet** vers un fichier JSON de métadonnées, pas le contenu JSON lui-même. Ce fichier est généré par l'API `/loader/document/{perimeter_code}`.

**Réponse** :
```json
{
  "id": "engineering|confluence_docs|123456",
  "path": "/spaces/DOCS/page/Architecture-Guide",
  "name": "Architecture Guide",
  "modification_time": "**************"
}
```

### **POST `/document/remove/{perimeter_code}`**

**Objectif** : Lancement de la suppression de documents

**Description technique** : Cette API supprime de la base vecteur les documents qui ne sont plus présents dans la source ou qui ont été marqués pour suppression. Elle utilise un fichier `remove.json` contenant la liste des documents à supprimer.

**Paramètres** :
- `perimeter_code` (string, requis) : Code du périmètre concerné

**Corps de la requête** :
```json
{
  "remove_document_file": "gs://ofr-ekb-knowledgebot-work-dev-production-dev/**************/engineering/confluence_docs/remove/remove_documents.json"
}
```

**⚠️ Format Important** : Le paramètre `remove_document_file` doit contenir un **chemin GCS complet** vers un fichier JSON, pas le contenu JSON lui-même. Ce fichier est généré par l'API `/document/compare/{perimeter_code}`.

**Réponse** :
```json
[
  {
    "id": "engineering|confluence_docs|old123",
    "path": "/spaces/DOCS/page/Old-Guide",
    "name": "Old Guide",
    "modification_time": "20241201120000"
  }
]
```

---

## 4. ⏰ **Schedule Routes** (`/schedule/*`)

### Description
Orchestre les traitements automatisés et fournit l'interface pour Cloud Scheduler.

### **GET `/schedule/treatments/{perimeter_code}/{date}`**

**Objectif** : Lancement de la récupération des traitements à réaliser

**Description technique** : Cette API analyse l'état des fichiers sur GCS pour déterminer quels traitements doivent être exécutés. Elle retourne une liste ordonnée d'appels API à effectuer pour compléter le workflow d'embedding.

**Paramètres** :
- `perimeter_code` (string, requis) : Périmètre (ebotman/mktsearch, etc.)
- `date` (string, requis) : Date courante au format YYYYMMDDHHMMSS

**Réponse** (`TreatmentBean[]`) :
```json
[
  {
    "file_type": "getlist",
    "url": "/loader/list/production",
    "params": {
      "get_list_file": "gs://bucket/**************/engineering/confluence_docs/getlist/getlist.json"
    }
  }
]
```

**Description des champs de réponse** :
- `file_type` (string, requis) : Type de traitement (getlist, list, getdoc, removedoc, docs)
- `url` (string, requis) : URL relative à appeler, contient l'action à réaliser et le périmètre
- `params` (object, requis) : Paramètres à passer à l'URL, contient le fichier à traiter

**Exemple d'appel** :
```bash
curl -X GET "http://localhost:8092/schedule/treatments/production/**************"
```

### **POST `/schedule/treatments/{perimeter_code}/launch`**

**Objectif** : Point d'entrée principal pour Cloud Scheduler - Lance l'orchestration complète

**Description technique** : Cette API est conçue pour être appelée par Cloud Scheduler. Elle lance automatiquement l'ensemble du workflow de traitement des documents pour un périmètre donné, depuis la récupération des sources jusqu'à l'embedding final.

**Paramètres** :
- `perimeter_code` (string, requis) : Périmètre (ebotman/mktsearch, etc.)

**Réponse** (`ResultBean`) :
```json
{
  "status": "ok",
  "error": ""
}
```

**Description des champs de réponse** :
- `status` (string, requis) : Status du traitement (ok ou ko)
- `error` (string, requis) : Message d'erreur si le status est ko

**Exemple d'appel** :
```bash
curl -X POST "http://localhost:8092/schedule/treatments/production/launch" \
     -H "Content-Type: application/json"
```

---

## 🔧 **Exemples de Configuration des Sources**

### Configuration Confluence

```json
{
  "id": 1,
  "code": "confluence_docs",
  "label": "Documentation Confluence",
  "src_type": "confluence",
  "configuration": {
    "spaces": ["DOCS", "API", "GUIDES"],
    "confluence_url": "https://company.atlassian.net",
    "include_attachments": true,
    "export_format": "markdown",
    "file_extensions": ["pdf", "docx", "md", "png", "svg"],
    "labels": ["public", "documentation"],
    "exclude_labels": ["draft", "private"],
    "max_results": 1000,
    "parallel_downloads": false,
    "enable_caching": true,
    "retry_attempts": 3
  },
  "domain_code": "engineering",
  "perimeter_code": "production",
  "last_load_time": 0,
  "load_interval": 24,
  "force_embedding": false
}
```

### Configuration SharePoint

```json
{
  "id": 2,
  "code": "sharepoint_files",
  "label": "Fichiers SharePoint",
  "src_type": "sharepoint",
  "configuration": {
    "site_name": "TeamSite",
    "relative_directory": "Documents partages/Knowledge Bot",
    "file_types": [".pdf", ".docx", ".xlsx"],
    "max_depth": 3
  },
  "domain_code": "marketing",
  "perimeter_code": "production",
  "last_load_time": 0,
  "load_interval": 12,
  "force_embedding": false
}
```

---

## 📁 **IMPORTANT : Compréhension des Formats de Fichiers**

### 📅 **Formats de Dates et Timestamps**

**⚠️ ATTENTION** : L'API utilise deux formats de date différents selon le contexte :

| Champ | Type | Format | Exemple | Usage |
|-------|------|--------|---------|-------|
| `last_load_time` | `integer` | Timestamp Unix (secondes) | `1702819200` | Sources - Dernière exécution |
| `modification_time` | `string` | YYYYMMDDHHMMSS | `"**************"` | Documents - Modification |
| `creationDate` | `string` | YYYYMMDDHHMMSS | `"**************"` | Métadonnées - Création |
| `modificationDate` | `string` | YYYYMMDDHHMMSS | `"**************"` | Métadonnées - Modification |
| `date` (URL param) | `string` | YYYYMMDDHHMMSS | `"**************"` | Schedule - Timestamp traitement |

**Conversion entre formats** :
```bash
# Timestamp Unix vers YYYYMMDDHHMMSS
date -r 1702819200 +"%Y%m%d%H%M%S"  # → 20231217140000

# YYYYMMDDHHMMSS vers timestamp Unix
date -j -f "%Y%m%d%H%M%S" "**************" +"%s"  # → 1734443400
```

### 🎯 **Format des Paramètres API**

**⚠️ ATTENTION** : Toutes les API `/loader/*` et `/document/*` attendent des **chemins GCS complets**, pas le contenu JSON en string.

| API | Paramètre | Type attendu | Exemple |
|-----|-----------|-------------|---------|
| `/loader/list/{perimeter}` | `get_list_file` | Chemin GCS | `"gs://bucket/date/domain/source/getlist/getlist.json"` |
| `/loader/document/{perimeter}` | `document_get_file` | Chemin GCS | `"gs://bucket/date/domain/source/getdoc/doc.getdoc.json"` |
| `/document/compare/{perimeter}` | `repo_document_list_file` | Chemin GCS | `"gs://bucket/date/domain/source/list/list.json"` |
| `/document/embedd/{perimeter}` | `embedd_document_metadata_file` | Chemin GCS | `"gs://bucket/date/domain/source/docs/file.metadata.json"` |
| `/document/remove/{perimeter}` | `remove_document_file` | Chemin GCS | `"gs://bucket/date/domain/source/remove/remove.json"` |

### 📋 **Structure des Fichiers GCS**

Les fichiers sont organisés selon la hiérarchie suivante :
```
gs://{bucket_prefix}/{load_date}/{domain_code}/{source_code}/{file_type}/
```

**Exemple concret** :
```
gs://ofr-ekb-knowledgebot-work-dev-production-dev/
└── **************/                    # Timestamp du traitement
    └── engineering/                   # domain_code
        └── confluence_docs/           # source_code
            ├── getlist/
            │   └── getlist.json       # Configuration source
            ├── list/
            │   └── list.json          # Liste des documents
            ├── getdoc/
            │   └── doc.getdoc.json    # Configuration document
            ├── docs/
            │   ├── file.pdf           # Document téléchargé
            │   └── file.pdf.metadata.json  # Métadonnées
            └── remove/
                └── remove.json        # Documents à supprimer
```

### 🔄 **Workflow des Fichiers**

1. **`/sources/load/{perimeter}`** → Génère `getlist.json`
2. **`/loader/list/{perimeter}`** → Lit `getlist.json` → Génère `list.json`
3. **`/document/compare/{perimeter}`** → Lit `list.json` → Génère `*.getdoc.json`
4. **`/loader/document/{perimeter}`** → Lit `*.getdoc.json` → Génère document + `*.metadata.json`
5. **`/document/embedd/{perimeter}`** → Lit `*.metadata.json` → Lance l'embedding

---

## 🚦 **Workflow Complet d'Usage**

### 1. Workflow Manuel Complet

```bash
# 1. Charger les sources (génère les fichiers getlist.json)
curl -X POST "http://localhost:8092/sources/load/production"

# 2. Récupérer la liste des documents (utilise getlist.json, génère list.json)
curl -X POST "http://localhost:8092/loader/list/production" \
     -H "Content-Type: application/json" \
     -d '{
       "get_list_file": "gs://bucket/**************/engineering/confluence_docs/getlist/getlist.json"
     }'

# 3. Comparer avec les documents existants (utilise list.json, génère *.getdoc.json)
curl -X POST "http://localhost:8092/document/compare/production" \
     -H "Content-Type: application/json" \
     -d '{
       "repo_document_list_file": "gs://bucket/**************/engineering/confluence_docs/list/list.json"
     }'

# 4. Télécharger un document spécifique (utilise *.getdoc.json, génère document + *.metadata.json)
curl -X POST "http://localhost:8092/loader/document/production" \
     -H "Content-Type: application/json" \
     -d '{
       "document_get_file": "gs://bucket/**************/engineering/confluence_docs/getdoc/architecture-guide.getdoc.json"
     }'

# 5. Embedder le document (utilise *.metadata.json)
curl -X POST "http://localhost:8092/document/embedd/production" \
     -H "Content-Type: application/json" \
     -d '{
       "embedd_document_metadata_file": "gs://bucket/**************/engineering/confluence_docs/docs/architecture-guide.md.metadata.json"
     }'

# 6. Supprimer des documents obsolètes (utilise remove.json)
curl -X POST "http://localhost:8092/document/remove/production" \
     -H "Content-Type: application/json" \
     -d '{
       "remove_document_file": "gs://bucket/**************/engineering/confluence_docs/remove/remove_documents.json"
     }'
```

### 2. Workflow Automatisé (Cloud Scheduler)

```bash
# Lancement complet automatisé
curl -X POST "http://localhost:8092/schedule/treatments/production/launch"
```

### 3. Monitoring et Debug

```bash
# Vérifier les traitements en attente
curl -X GET "http://localhost:8092/schedule/treatments/production/**************"
```

---

## ⚠️ **Gestion d'Erreurs**

### Codes d'Erreur HTTP

| Code | Nom | Description | API Concernées |
|------|-----|-------------|----------------|
| **200** | `OK` | Succès de la requête | Toutes |
| **422** | `Validation Error` | Erreur de validation des paramètres d'entrée | Toutes sauf GET |
| **500** | `Internal Server Error` | Erreur de traitement interne (ex: LoaderException) | Toutes |

### Réponses d'Erreur

**Erreur 422 - Validation Error** :
```json
{
  "detail": [
    {
      "type": "missing",
      "loc": ["body", "get_list_file"],
      "msg": "Field required",
      "input": {}
    }
  ]
}
```

**Erreur 500 - Internal Server Error** :
```json
{
  "detail": "loader_exception"
}
```

### Types d'Erreurs Courantes

- **`perimeter_code_not_valid`** : Le code périmètre contient des caractères non alphanumériques
- **`loader_exception`** : Erreur lors de l'exécution du loader (credentials, réseau, etc.)
- **`source_not_found`** : Source non trouvée pour le périmètre spécifié
- **`file_not_found`** : Fichier GCS spécifié introuvable
- **`invalid_json_format`** : Format JSON invalide dans le fichier GCS

---

## 🔍 **Documentation OpenAPI Interactive**

L'API expose automatiquement une documentation interactive accessible via :

- **Swagger UI** : `http://localhost:8092/docs`
- **ReDoc** : `http://localhost:8092/redoc`
- **OpenAPI JSON** : `http://localhost:8092/openapi.json`

Cette documentation permet de :
- Tester les endpoints directement
- Voir les schémas de données
- Comprendre les paramètres requis
- Visualiser les exemples de réponses

---

## 🚀 **Démarrage Rapide**

### 1. Lancement Local

```bash
# Dans le répertoire du projet
make run-load-scheduler
```

### 2. Test de Base

```bash
# Tester l'API de santé
curl -X GET "http://localhost:8092/docs"

# Charger toutes les sources
curl -X POST "http://localhost:8092/sources/loadall"
```

### 3. Configuration d'une Source

```bash
# Exemple avec une source Confluence
curl -X POST "http://localhost:8092/sources/load/production" \
     -H "Content-Type: application/json"
```

---

## 🧪 **Comment tester les API Loader en local**

### 1. 🚀 **Démarrage de l'application en mode local**

#### Étape 1 : Configuration de l'environnement

```bash
# Installer les dépendances
make init

# Démarrer l'application en mode local
make start
```

L'application sera disponible sur `http://localhost:8092` (port défini par `LOAD_SCHEDULER_PORT`)

#### Étape 2 : Vérifier que l'API fonctionne

```bash
# Accéder à la documentation Swagger
open http://localhost:8092/docs

# Ou tester un endpoint simple
curl -X GET "http://localhost:8092/docs"
```

### 2. 📋 **Pré-requis pour tester les Loader APIs**

#### Variables d'environnement nécessaires

Le fichier `Makefile` configure automatiquement ces variables :

```bash
export ENV="local"
export GCP_PROJECT_ID="ofr-ekb-knowledgebot-dev"
export PATH_TO_SECRET_CONFIG="${ROOT_DIR}/conf/etc/secrets/local"
export KBOT_BACK_API_URL="http://localhost:8091"
export KBOT_EMBEDDING_API_URL="http://localhost:8093"
export KBOT_WORK_BUCKET_PREFIX="gs://ofr-ekb-knowledgebot-work-dev-[perimeter_code]-dev"
```

#### Configuration des credentials

Assurez-vous d'avoir les fichiers de configuration dans :
```
conf/etc/secrets/local/
├── confluence_credentials.json
├── sharepoint_credentials.json
└── basic_credentials.json
```

### 3. 🎯 **Tests des API Loader avec des exemples concrets**

#### Test 1 : `/loader/list/{perimeter_code}` - Récupérer la liste des documents

**Étape A : Préparer une source (optionnel)**
```bash
# D'abord charger les sources disponibles
curl -X POST "http://localhost:8092/sources/loadall" \
     -H "Content-Type: application/json"
```

**Étape B : Tester l'API de listing**
```bash
# Exemple avec une source Confluence
#!/bin/bash
set -euo pipefail

BASE_URL="http://localhost:8092"
PERIMETER="${1:-production}"

echo "📋 Test de /loader/list"
echo "========================"

curl -s -X POST "${BASE_URL}/loader/list/${PERIMETER}" \
     -H "Content-Type: application/json" \
     -d "$(jq -n \
        --arg perimeter "$PERIMETER" \
        '{
          get_list_file: (
            {
              source: {
                id: 1,
                code: "confluence_docs",
                src_type: "confluence",
                configuration: {
                  spaces: ["TEST"]
                },
                domain_code: "test",
                perimeter_code: $perimeter
              }
            } | @json
          )
        }')"
```

**Réponse attendue :**
```json
[
  {
    "id": "engineering|confluence_docs|123456",
    "path": "/spaces/DOCS/page/Architecture-Guide",
    "name": "Architecture Guide",
    "modification_time": "**************"
  }
]
```

#### Test 2 : `/loader/document/{perimeter_code}` - Récupérer un document spécifique

```bash
#!/bin/bash
set -euo pipefail

BASE_URL="http://localhost:8092"
PERIMETER="${1:-production}"

echo "📄 Test de /loader/document"
echo "==========================="

curl -s -X POST "${BASE_URL}/loader/document/${PERIMETER}" \
     -H "Content-Type: application/json" \
     -d "$(jq -n \
        --arg perimeter "$PERIMETER" \
        '{
          document_get_file: (
            {
              source: {
                id: 1,
                code: "confluence_docs",
                src_type: "confluence",
                configuration: {
                  spaces: ["TEST"]
                },
                domain_code: "test",
                perimeter_code: $perimeter
              },
              document: {
                id: "engineering|confluence_docs|123456",
                path: "/spaces/DOCS/page/Architecture-Guide",
                name: "Architecture Guide",
                modification_time: "**************"
              }
            } | @json
          )
        }')"
```

### 4. 🔍 **Comment obtenir les chemins de fichiers GCS**

#### Option 1 : Lister les fichiers avec gsutil
```bash
# Lister tous les fichiers d'un traitement
gsutil ls -r gs://bucket/**************/engineering/confluence_docs/

# Exemple de sortie :
# gs://bucket/**************/engineering/confluence_docs/getlist/getlist.json
# gs://bucket/**************/engineering/confluence_docs/list/list.json
# gs://bucket/**************/engineering/confluence_docs/getdoc/doc1.getdoc.json
```

#### Option 2 : Via l'API Schedule (recommandé)
```bash
# Obtenir les traitements à effectuer avec leurs chemins
curl -X GET "http://localhost:8092/schedule/treatments/production/**************"

# La réponse contient les chemins de fichiers à utiliser
```

#### Option 3 : Via Google Cloud Console
1. Allez sur [console.cloud.google.com](https://console.cloud.google.com)
2. Storage > Browser
3. Naviguez vers votre bucket et copiez les chemins

---

### 4. 🔧 **Tests avec des données de test prédéfinies**

#### Utilisation des fixtures de test

Le projet contient des données de test dans `tests/data/` :

```bash
# Lancer les tests unitaires pour voir des exemples
pipenv run python -m pytest tests/routes/test_loader_routes.py -v

# Examiner les données de test
cat tests/data/confluence_repo_test_data.py
cat tests/data/gcs_repo_test_data.py
```

#### Structure des données de test pour Confluence

```python
# Exemple tiré de tests/data/confluence_repo_test_data.py
FAKE_CREDENTIALS_CONFIG = {
    "url": "https://test-instance.atlassian.net",
    "pat_token": "fake_pat_token",
    "cloud": False,
}

SPACE_CONTENT_RESPONSE = [
    {
        "id": "123456",
        "type": "page",
        "title": "Test Page 1",
        "space": {"key": "TEST"},
        "version": {"when": "2024-01-15T10:30:00.000Z", "number": 1}
    }
]
```

### 5. 🐛 **Debug et résolution des problèmes courants**

#### Problèmes de credentials

```bash
# Vérifier que les credentials sont bien configurés
curl -X POST "http://localhost:8092/sources/loadall" \
     -H "Content-Type: application/json"

# Si erreur 500, vérifier les logs de l'application
# Les logs apparaissent dans le terminal où vous avez lancé `make start`
```

#### Problèmes de format JSON

```bash
# Utilisez des outils pour valider le JSON
echo '{"get_list_file": "..."}' | python -m json.tool

# Ou utilisez jq pour formatter
echo '{"test": "value"}' | jq .
```

#### Test avec des paramètres minimaux

```bash
# ⚠️ INCORRECT - Ne fait pas ça (ancien format)
# curl -X POST "http://localhost:8092/sources/load/production" ...

# ✅ CORRECT - Utiliser un chemin GCS valide
curl -X POST "http://localhost:8092/loader/list/production" \
     -H "Content-Type: application/json" \
     -d '{
       "get_list_file": "gs://ofr-ekb-knowledgebot-work-dev-production-dev/**************/engineering/confluence_docs/getlist/getlist.json"
     }'
```

### 6. 📊 **Tests automatisés disponibles**

#### Lancer les tests de routes

```bash
# Tests des routes loader spécifiquement
pipenv run python -m pytest tests/routes/test_loader_routes.py -v -s

# Tests avec plus de détails
pipenv run python -m pytest tests/routes/test_loader_routes.py::TestLoaderRoutes::test_get_document_list -v -s
```

#### Tests d'intégration

```bash
# Tests d'intégration Confluence (nécessite des credentials réels)
make integration-tests-confluence

# Test des credentials seulement
make test-credentials
```

### 7. 🎯 **Exemples complets par type de source**

#### Confluence
```bash
curl -X POST "http://localhost:8092/loader/list/production" \
     -H "Content-Type: application/json" \
     -d '{
       "get_list_file": "gs://ofr-ekb-knowledgebot-work-dev-production-dev/**************/engineering/confluence_docs/getlist/getlist.json"
     }'
```

#### SharePoint
```bash
curl -X POST "http://localhost:8092/loader/list/production" \
     -H "Content-Type: application/json" \
     -d '{
       "get_list_file": "gs://ofr-ekb-knowledgebot-work-dev-production-dev/**************/marketing/sharepoint_files/getlist/getlist.json"
     }'
```

#### GCS (Google Cloud Storage)
```bash
curl -X POST "http://localhost:8092/loader/list/production" \
     -H "Content-Type: application/json" \
     -d '{
       "get_list_file": "gs://ofr-ekb-knowledgebot-work-dev-production-dev/**************/operations/gcs_docs/getlist/getlist.json"
     }'
```

### 8. 🔍 **Monitoring et logs**

#### Activer les logs détaillés

```bash
# Modifier le niveau de log dans le terminal de démarrage
export LOG_LEVEL=DEBUG

# Puis relancer l'application
make start
```

#### Vérifier les appels réseau

```bash
# Utiliser un proxy comme Charles ou mitmproxy pour intercepter les requêtes
# Ou analyser les logs de l'application directement
```

### 9. ⚡ **Script de test rapide**

⚠️ **IMPORTANT** : Ces scripts nécessitent des fichiers GCS réels créés par les API précédentes.

#### Script complet avec workflow séquentiel

Créez un fichier `test_loader_workflow.sh` :

```bash
#!/bin/bash
set -euo pipefail

BASE_URL="http://localhost:8092"
PERIMETER="${1:-production}"
CURRENT_DATE=$(date +"%Y%m%d%H%M%S")

echo "🧪 Test complet du workflow Loader"
echo "=================================="
echo "Périmètre: $PERIMETER"
echo "Date: $CURRENT_DATE"
echo ""

# Étape 1: Charger les sources (génère getlist.json)
echo "📂 1. Chargement des sources..."
curl -s -X POST "${BASE_URL}/sources/load/${PERIMETER}" \
     -H "Content-Type: application/json" | jq .

echo -e "\n⏱️  Attente 5 secondes pour la création des fichiers..."
sleep 5

# Étape 2: Récupérer les traitements pour obtenir les chemins GCS
echo "📋 2. Récupération des traitements disponibles..."
TREATMENTS=$(curl -s -X GET "${BASE_URL}/schedule/treatments/${PERIMETER}/${CURRENT_DATE}")
echo "$TREATMENTS" | jq .

# Extraire le chemin getlist.json du premier traitement
GETLIST_FILE=$(echo "$TREATMENTS" | jq -r '.[0].params.get_list_file // empty')

if [ -z "$GETLIST_FILE" ]; then
    echo "❌ Aucun fichier getlist trouvé. Vérifiez que des sources existent pour le périmètre $PERIMETER"
    exit 1
fi

echo -e "\n📄 3. Test de /loader/list avec fichier réel:"
echo "Fichier: $GETLIST_FILE"

# Étape 3: Tester l'API loader/list avec un vrai chemin GCS
curl -s -X POST "${BASE_URL}/loader/list/${PERIMETER}" \
     -H "Content-Type: application/json" \
     -d "{\"get_list_file\": \"${GETLIST_FILE}\"}" | jq .

echo -e "\n✅ Test terminé!"
echo "💡 Pour tester /loader/document, utilisez les chemins générés par ce workflow"
```

#### Script de test minimal (avec chemin GCS prédéfini)

Créez un fichier `test_loader_simple.sh` :

```bash
#!/bin/bash
set -euo pipefail

BASE_URL="http://localhost:8092"
PERIMETER="${1:-production}"

echo "🧪 Test simple des API Loader"
echo "============================="

# ⚠️ ATTENTION: Ce chemin doit exister dans GCS
# Remplacez par un chemin réel de votre environnement
GCS_GETLIST_FILE="gs://ofr-ekb-knowledgebot-work-dev-${PERIMETER}-dev/**************/engineering/confluence_docs/getlist/getlist.json"

echo "📋 Test de /loader/list avec fichier GCS:"
echo "Fichier: $GCS_GETLIST_FILE"
echo ""

curl -s -X POST "${BASE_URL}/loader/list/${PERIMETER}" \
     -H "Content-Type: application/json" \
     -d "{\"get_list_file\": \"${GCS_GETLIST_FILE}\"}" | jq .

echo -e "\n� Si erreur 'File not found', utilisez le script workflow complet"
```

```bash
chmod +x test_loader_workflow.sh
chmod +x test_loader_simple.sh

# Test avec workflow complet (recommandé)
./test_loader_workflow.sh production

# Test simple avec chemin GCS prédéfini
./test_loader_simple.sh production
```

#### 🔍 **Comment obtenir un chemin GCS valide**

**Méthode 1 : Via l'API Schedule (automatique)**
```bash
# Obtenez les traitements avec leurs chemins GCS réels
curl -X GET "http://localhost:8092/schedule/treatments/production/$(date +%Y%m%d%H%M%S)"
```

**Méthode 2 : Via gsutil (manuel)**
```bash
# Listez les fichiers getlist existants
gsutil ls "gs://ofr-ekb-knowledgebot-work-dev-mktsearch-dev/**/getlist/getlist.json"
          "gs://ofr-ekb-knowledgebot-work-dev-mktsearch-dev/202506190800/CiblesRurales/testconfluence27284/getlist/getlist.json

# Exemple de sortie:
# gs://ofr-ekb-knowledgebot-work-dev-production-dev/**************/engineering/confluence_docs/getlist/getlist.json
```

**Méthode 3 : Créer d'abord les fichiers nécessaires**
```bash
# 1. Charger les sources pour créer getlist.json
curl -X POST "http://localhost:8092/sources/load/production"

# 2. Attendre quelques secondes pour la création des fichiers

# 3. Utiliser les chemins générés
curl -X GET "http://localhost:8092/schedule/treatments/production/$(date +%Y%m%d%H%M%S)"
```

Cette documentation complète vous permet de tester facilement les API loader en local avec des exemples concrets et des solutions pour les problèmes courants.

### 10. 📁 **Emplacement des fichiers lors des tests en local**

#### ⚠️ **GCS est-il obligatoire ?**

**Réponse courte : OUI, Google Cloud Storage (GCS) est obligatoire pour le stockage des fichiers.**

**Pourquoi GCS est obligatoire :**

1. **Architecture Cloud-native** : Le système est conçu pour Cloud Run et l'écosystème GCP
2. **Intégration profonde** : Le code utilise directement les API GCS (`google.cloud.storage`)
3. **Pas d'abstraction** : Aucune couche d'abstraction pour d'autres systèmes de stockage
4. **Orchestration** : Le workflow s'appuie sur les chemins GCS pour l'orchestration

**Alternatives possibles :**

| Contexte | Solution | Limitation |
|----------|----------|------------|
| **Tests unitaires** | `MockGcs` (simulation en mémoire) | ✅ Fonctionne parfaitement |
| **Développement local** | GCS avec credentials de dev | ✅ Recommandé |
| **Autre cloud provider** | Pas supporté nativement | ❌ Nécessiterait refactoring |
| **Stockage local** | Pas supporté | ❌ APIs GCS câblées en dur |

#### Variables d'environnement de stockage

En mode local, les fichiers sont gérés via les variables d'environnement suivantes (configurées automatiquement par `make start`) :

```bash
# Configuration locale (make start) - OBLIGATOIRE: buckets GCS réels
KBOT_WORK_BUCKET_PREFIX="gs://ofr-ekb-knowledgebot-work-dev-[perimeter_code]"
PATH_TO_SECRET_CONFIG="${ROOT_DIR}/conf/etc/secrets/local"

# Configuration tests (make unit-tests) - Mock GCS en mémoire
KBOT_WORK_BUCKET_PREFIX="gs://mon_bucket-[perimeter_code]"
PATH_TO_SECRET_CONFIG="${ROOT_DIR}/conf/etc/secrets/tests"
```

#### Structure des fichiers générés

Les API loader génèrent différents types de fichiers organisés selon cette hiérarchie :

```
gs://{bucket_prefix}/{load_date}/{domain_code}/{source_code}/{file_type}/
```

**Exemple concret :**
```
gs://ofr-ekb-knowledgebot-work-dev-production-dev/
└── **************/                    # Timestamp du traitement
    └── engineering/                   # domain_code
        └── confluence_docs/           # source_code
            ├── getlist/
            │   └── getlist.json       # Configuration source
            ├── list/
            │   └── list.json          # Liste des documents
            ├── getdoc/
            │   └── doc.getdoc.json    # Configuration document
            ├── docs/
            │   ├── file.pdf           # Document téléchargé
            │   └── file.pdf.metadata.json  # Métadonnées
            └── remove/
                └── remove.json        # Documents à supprimer
```

#### Types de fichiers créés par les API loader

| Type | API | Contenu | Exemple de nom |
|------|-----|---------|----------------|
| **getlist** | `/loader/list/{perimeter}` | Configuration source + liste des documents | `getlist.json` |
| **list** | `/loader/list/{perimeter}` | Liste finale des documents après récupération | `list.json` |
| **getdoc** | `/loader/document/{perimeter}` | Configuration pour télécharger un document spécifique | `{doc_id}.getdoc.json` |
| **docs** | `/loader/document/{perimeter}` | Document téléchargé + métadonnées | `{filename}` + `{filename}.metadata.json` |

#### Simulation locale avec Mock GCS

Lors des **tests unitaires**, le système utilise un mock GCS (`MockGcs`) qui simule le stockage :

```python
# Les buckets sont simulés en mémoire
my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
my_mock_gcs.add_bucket("gs://mon_bucket-production")

# Les fichiers sont stockés dans des objets Mock
blob = my_mock_gcs.return_blob("mon_bucket-production", "path/to/file.json")
content = blob.download_as_string()  # Récupération du contenu
```

#### Configuration pour les tests d'intégration

Pour les **tests d'intégration** avec de vrais credentials GCS :

```bash
# 1. Configurez les credentials GCP
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account.json"

# 2. Utilisez un bucket de test réel
export KBOT_WORK_BUCKET_PREFIX="gs://mon-bucket-test-[perimeter_code]"

# 3. Lancez les tests d'intégration
make integration-tests
```

#### Accès aux fichiers créés

En mode local avec de vrais buckets GCS, vous pouvez accéder aux fichiers via :

**1. Google Cloud Console :**
- Allez sur [console.cloud.google.com](https://console.cloud.google.com)
- Storage > Browser
- Naviguez vers votre bucket configuré

**2. CLI gsutil :**
```bash
# Lister les fichiers créés
gsutil ls gs://ofr-ekb-knowledgebot-work-dev-production-dev/

# Télécharger un fichier spécifique
gsutil cp gs://bucket/path/file.json ./local-file.json

# Voir le contenu d'un fichier JSON
gsutil cat gs://bucket/path/file.json | jq .
```

**3. Programmatiquement :**
```python
from google.cloud import storage

client = storage.Client()
bucket = client.bucket("ofr-ekb-knowledgebot-work-dev-production-dev")
blob = bucket.blob("**************/engineering/confluence_docs/getlist/getlist.json")
content = blob.download_as_text()
print(content)
```

#### Debug et vérification des fichiers

**Vérifier qu'un traitement a créé les bons fichiers :**
```bash
# Exemple après un appel à /loader/list/production
curl -X POST "http://localhost:8092/loader/list/production" \
     -H "Content-Type: application/json" \
     -d '{"get_list_file": "..."}'

# Vérifier que les fichiers ont été créés
gsutil ls gs://votre-bucket/*/engineering/confluence_docs/list/
```

**Examiner le contenu des métadonnées :**
```bash
# Voir les métadonnées d'un document téléchargé
gsutil cat gs://bucket/date/domain/source/docs/document.pdf.metadata.json | jq .
```

#### Nettoyage après tests

```bash
# Supprimer les fichiers de test
gsutil -m rm -r gs://votre-bucket-test/**

# Ou nettoyer seulement les fichiers anciens
gsutil -m rm -r gs://votre-bucket/$(date -v-1d +%Y%m%d)*
```

Cette organisation permet un suivi précis de tous les fichiers créés par chaque API et facilite le debug en cas de problème.

---

## Configuration minimum pour tests locaux avec GCS

**Option 1 : Utiliser un bucket GCS de développement**
```bash
# 1. Authentifiez-vous avec gcloud
gcloud auth application-default login

# 2. Créez un bucket de test (si pas déjà fait)
gsutil mb gs://mon-bucket-dev-test

# 3. Configurez les variables d'environnement
export KBOT_WORK_BUCKET_PREFIX="gs://mon-bucket-dev-[perimeter_code]"
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account.json"
```

**Option 2 : Tests unitaires avec Mock (sans GCS réel)**
```bash
# Les tests unitaires utilisent automatiquement MockGcs
make unit-tests  # Pas besoin de vrais buckets GCS
```

**Option 3 : Docker avec émulation GCS locale**
```bash
# Utilisation de fake-gcs-server pour émulation locale
docker run -d --name fake-gcs -p 4443:4443 \
  fsouza/fake-gcs-server -scheme http -host 0.0.0.0 -port 4443

# Puis configurez l'endpoint
export STORAGE_EMULATOR_HOST=localhost:4443
```

#### Pourquoi cette dépendance à GCS ?

Le code utilise directement les bibliothèques GCS dans plusieurs endroits :

```python
# src/kbotloadscheduler/gcs/gcs_utils.py
from google.cloud import storage

def get_gcs_blob(gcs_uri):
    bucket_name, object_name = get_bucket_and_object_name(gcs_uri)
    client = storage.Client()  # Client GCS spécifique
    bucket = client.get_bucket(bucket_name)
    return bucket.get_blob(object_name)
```

Cette architecture est intimement liée à GCP et ne peut pas facilement être adaptée à d'autres providers de stockage sans refactoring significatif.

---

## ⚠️ **RÉSUMÉ IMPORTANT - FORMATS CORRECTS**

### 🎯 **Points Clés à Retenir**

1. **TOUS les paramètres des API `/loader/*` et `/document/*` attendent des CHEMINS GCS, pas du JSON en string**

2. **Format CORRECT** :
   ```json
   {
     "get_list_file": "gs://bucket/date/domain/source/getlist/getlist.json"
   }
   ```

3. **Workflow des Fichiers** :
   - `sources/load` → Génère `getlist.json`
   - `loader/list` → Lit `getlist.json`, génère `list.json`
   - `document/compare` → Lit `list.json`, génère `*.getdoc.json`
   - `loader/document` → Lit `*.getdoc.json`, génère document + `*.metadata.json`
   - `document/embedd` → Lit `*.metadata.json`

4. **Comment obtenir les chemins** :
   - Utilisez `gsutil ls` ou `GET /schedule/treatments/{perimeter}/{date}`
   - Les chemins suivent le pattern : `gs://bucket/{load_date}/{domain}/{source}/{type}/`

### 🚨 **Erreurs Courantes à Éviter**

- ❌ Passer du JSON en string dans les paramètres
- ❌ Utiliser des chemins de fichiers locaux au lieu de chemins GCS
- ❌ Oublier le préfixe `gs://` dans les chemins
- ❌ Utiliser des fichiers qui n'existent pas dans GCS

---

## ✅ **Validation OpenAPI - Documentation Entièrement Alignée**

### 🔍 **Vérification des Spécifications**

Cette documentation a été validée contre les spécifications OpenAPI réelles de l'API (`http://localhost:8092/openapi.json`) et entièrement mise à jour pour corriger tous les écarts identifiés.

### 🔧 **URLs de Validation**

Pour vérifier l'alignement en temps réel :
```bash
# Récupérer les spécifications actuelles
curl -s http://localhost:8092/openapi.json | jq .

# Accéder à la documentation interactive
open http://localhost:8092/docs

# Tester les endpoints directement
curl -X POST http://localhost:8092/sources/loadall
```

### 📅 **Dernière Vérification Complète**

- **Date** : 19 juin 2025
- **Version API** : 0.1.0 (dev)
- **OpenAPI Version** : 3.1.0
- **Status** : ✅ Documentation entièrement alignée avec OpenAPI
- **Corrections** : ✅ Descriptions techniques, schémas complets, formats de dates standardisés

---
