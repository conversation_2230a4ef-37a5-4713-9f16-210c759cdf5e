# Testing Cheat Sheet

## 🚀 Quick Start (3 commands)

```bash
make setup-mock-data    # Setup test data
make start-mock         # Start server with mocks
make test-mock-endpoint # Test API (in another terminal)
```

## 📋 Command Reference

### Server Modes
```bash
make start        # Development (real GCP, needs auth)
make start-test   # Testing (real GCS + local secrets, needs auth)
make start-mock   # Local only (mocked GCS, no auth needed)
```

### Testing
```bash
make unit-tests              # Fast tests (no external deps)
make integration-tests       # Full tests (needs auth + external services)
make test-mock-endpoint     # Test API with mocks
make all-tests              # Everything (unit + integration)
```

### Code Quality
```bash
make lint-check    # Check code quality
make fix-all       # Auto-fix formatting issues
make bandit        # Security scan
```

## 🎯 When to Use What

| Scenario | Command | Prerequisites |
|----------|---------|---------------|
| **Quick development feedback** | `make unit-tests` | None |
| **Test API locally** | `make start-mock` → `make test-mock-endpoint` | None |
| **Test with real GCS** | `make start-test` | `gcloud auth` |
| **Full integration testing** | `make integration-tests` | `gcloud auth` + external services |
| **Before commit** | `make lint-check` + `make unit-tests` | None |
| **Before release** | `make all-tests` | Full credentials |

## 🔍 Troubleshooting

### "Authentication failed"
```bash
# For real GCS testing:
gcloud auth application-default login

# For mock testing (no auth needed):
make start-mock
```

### "GCS file not found"
```bash
# For mock testing:
make setup-mock-data
make start-mock

# For real GCS: ensure file exists in your bucket
```

### "Secret not found"
```bash
# Check test secrets exist:
ls conf/etc/secrets/tests/

# Copy from local if needed:
cp conf/etc/secrets/local/* conf/etc/secrets/tests/
```

## 📁 Mock GCS Paths

When using `make start-mock`, these paths are available:
```
gs://mock-bucket-engineering/test-data/getlist.json
gs://mock-bucket-marketing/test-data/getlist.json
gs://mock-bucket-operations/test-data/getlist.json
```

## 🧪 Test Examples

### Test Different Perimeters
```bash
# Engineering
curl -X POST http://localhost:8092/loader/list/engineering \
  -H "Content-Type: application/json" \
  -d '{"get_list_file": "gs://mock-bucket-engineering/test-data/getlist.json"}'

# Marketing
curl -X POST http://localhost:8092/loader/list/marketing \
  -H "Content-Type: application/json" \
  -d '{"get_list_file": "gs://mock-bucket-marketing/test-data/getlist.json"}'
```

### Test Error Cases
```bash
# Non-existent file (should error)
curl -X POST http://localhost:8092/loader/list/engineering \
  -H "Content-Type: application/json" \
  -d '{"get_list_file": "gs://mock-bucket-engineering/nonexistent.json"}'

# Invalid URI (should error - doesn't start with gs://)
curl -X POST http://localhost:8092/loader/list/engineering \
  -H "Content-Type: application/json" \
  -d '{"get_list_file": "file:///local/file.json"}'
```

## 🔄 Development Workflow

### Daily Development
```bash
1. make unit-tests           # Quick feedback
2. make start-mock          # Local API testing
3. make test-mock-endpoint  # Verify endpoints work
```

### Feature Development
```bash
1. make lint-check          # Code quality
2. make unit-tests          # Unit validation
3. make start-test          # Real GCS testing
4. make integration-tests   # Full validation
```

### CI/CD
```bash
1. make init               # Setup
2. make unit-tests         # Fast tests
3. make lint-check         # Quality
4. make start-mock &       # Background server
5. make test-mock-endpoint # API tests
```

## 📚 Documentation Links

- [Complete Testing Guide](TESTING_GUIDE.md)
- [GCS Mocking Guide](GCS_MOCKING_GUIDE.md)
- [Makefile Reference](MAKEFILE_REFERENCE.md)
- [API Documentation](API_Documentation.md)
