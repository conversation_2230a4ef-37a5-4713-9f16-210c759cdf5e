# Déploiement Cloud Run pour Confluence RAG Loader

Guide complet pour déployer le Confluence Loader optimisé RAG sur Google Cloud Run.

## 🎯 Architecture de Déploiement

```mermaid
graph TB
    CS[Cloud Scheduler] --> CR[Cloud Run Service]
    CR --> CF[Confluence Server On-Premise]
    CR --> SM[Secret Manager]
    CR --> GCS[Google Cloud Storage]
    GCS --> VDB[Vector Database RAG]

    subgraph "Cloud Run Instance"
        CL[Confluence Loader]
        MD[Markdown Converter]
        DIO[DrawIO Processor]
        REL[Relationship Extractor]
    end

    CR --> CL
    CL --> MD
    CL --> DIO
    CL --> REL
```

## 🔧 Configuration Cloud Run

### 1. Service Configuration

```yaml
# cloud-run-confluence-rag.yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: kbot-confluence-rag-loader
  labels:
    app: kbot-loader
    component: confluence
    purpose: rag
spec:
  template:
    metadata:
      annotations:
        # Scaling
        autoscaling.knative.dev/minScale: "0"
        autoscaling.knative.dev/maxScale: "10"

        # Resources
        run.googleapis.com/memory: "2Gi"
        run.googleapis.com/cpu: "1000m"
        run.googleapis.com/execution-environment: gen2

        # Networking
        run.googleapis.com/vpc-access-connector: projects/PROJECT_ID/locations/REGION/connectors/confluence-connector
        run.googleapis.com/vpc-access-egress: private-ranges-only

    spec:
      containerConcurrency: 1
      timeoutSeconds: 900  # 15 minutes

      containers:
      - name: confluence-loader
        image: gcr.io/PROJECT_ID/kbot-confluence-loader:latest

        ports:
        - containerPort: 8080
          name: http1

        env:
        # Confluence Configuration
        - name: CONFLUENCE_URL
          value: "https://confluence.company.internal"
        - name: CONFLUENCE_CLOUD
          value: "false"
        - name: CONFLUENCE_PAT_TOKEN
          valueFrom:
            secretKeyRef:
              name: confluence-rag-credentials
              key: pat_token

        # Cloud Run Optimizations
        - name: PYTHONUNBUFFERED
          value: "1"
        - name: GOOGLE_CLOUD_PROJECT
          value: "PROJECT_ID"
        - name: GCS_BUCKET
          value: "kbot-rag-documents"

        # RAG Specific
        - name: RAG_MODE
          value: "true"
        - name: EXPORT_FORMAT
          value: "markdown"
        - name: ENABLE_RELATIONSHIPS
          value: "true"

        resources:
          limits:
            memory: "2Gi"
            cpu: "1000m"
          requests:
            memory: "1Gi"
            cpu: "500m"

        # Health checks
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10

        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
```

### 2. Secret Manager Setup

```bash
# Créer les secrets pour authentification
gcloud secrets create confluence-rag-credentials \
    --data-file=confluence-credentials.json

# Accorder l'accès au service Cloud Run
gcloud secrets add-iam-policy-binding confluence-rag-credentials \
    --member="serviceAccount:kbot-loader@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor"
```

**confluence-credentials.json :**
```json
{
  "pat_token": "ATATT3xFfGF0T2...",
  "url": "https://confluence.company.internal",
  "cloud": false,
  "timeout": 60,
  "max_retries": 3
}
```

### 3. Cloud Scheduler Configuration

```yaml
# scheduler-confluence-rag.yaml
name: confluence-rag-daily-sync
description: "Daily sync of Confluence content for RAG system"
schedule: "0 2 * * *"  # 2 AM daily
timeZone: "Europe/Paris"

httpTarget:
  uri: https://kbot-confluence-rag-loader-PROJECT_ID.a.run.app/loader/list/rag_production
  httpMethod: POST
  headers:
    Content-Type: application/json
  body: |
    {
      "get_list_file": "202406151000_rag_production_confluence_rag_knowledge_getlist.json"
    }

  oidcToken:
    serviceAccountEmail: kbot-scheduler@PROJECT_ID.iam.gserviceaccount.com
    audience: https://kbot-confluence-rag-loader-PROJECT_ID.a.run.app
```

## 🚀 Déploiement

### 1. Build et Push Image

```bash
# Build de l'image
docker build -t gcr.io/PROJECT_ID/kbot-confluence-loader:latest .

# Push vers Google Container Registry
docker push gcr.io/PROJECT_ID/kbot-confluence-loader:latest
```

### 2. Déploiement Cloud Run

```bash
# Déployer le service
gcloud run deploy kbot-confluence-rag-loader \
  --image gcr.io/PROJECT_ID/kbot-confluence-loader:latest \
  --platform managed \
  --region europe-west1 \
  --memory 2Gi \
  --cpu 1 \
  --timeout 900 \
  --concurrency 1 \
  --max-instances 10 \
  --set-env-vars CONFLUENCE_CLOUD=false,RAG_MODE=true \
  --set-secrets CONFLUENCE_PAT_TOKEN=confluence-rag-credentials:latest \
  --vpc-connector confluence-connector \
  --vpc-egress private-ranges-only \
  --service-account kbot-loader@PROJECT_ID.iam.gserviceaccount.com
```

### 3. Configuration du Scheduler

```bash
# Créer le job Cloud Scheduler
gcloud scheduler jobs create http confluence-rag-daily \
  --schedule="0 2 * * *" \
  --uri="https://kbot-confluence-rag-loader-PROJECT_ID.a.run.app/loader/list/rag_production" \
  --http-method=POST \
  --headers="Content-Type=application/json" \
  --message-body-from-file=scheduler-body.json \
  --oidc-service-account-email=kbot-scheduler@PROJECT_ID.iam.gserviceaccount.com \
  --oidc-token-audience="https://kbot-confluence-rag-loader-PROJECT_ID.a.run.app"
```

## 📊 Monitoring et Observabilité

### 1. Métriques Cloud Run

```bash
# CPU et Mémoire
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=kbot-confluence-rag-loader" --limit=50

# Logs applicatifs
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=kbot-confluence-rag-loader AND severity>=WARNING" --limit=20
```

### 2. Alertes Recommandées

```yaml
# alerting-policy.yaml
displayName: "Confluence RAG Loader Failures"
conditions:
  - displayName: "High error rate"
    conditionThreshold:
      filter: resource.type="cloud_run_revision" resource.label.service_name="kbot-confluence-rag-loader"
      comparison: COMPARISON_GREATER_THAN
      thresholdValue: 0.1  # 10% error rate
      duration: 300s

  - displayName: "Memory usage high"
    conditionThreshold:
      filter: resource.type="cloud_run_revision" resource.label.service_name="kbot-confluence-rag-loader"
      comparison: COMPARISON_GREATER_THAN
      thresholdValue: 1.8  # 1.8GB
      duration: 180s
```

### 3. Dashboard

Métriques à surveiller :
- **Durée d'exécution** : < 15 minutes par espace
- **Utilisation mémoire** : < 1.8GB
- **Taux d'erreur** : < 5%
- **Pages traitées/minute** : 10-15
- **Attachments/minute** : 5-8

## 🔧 Troubleshooting

### Problèmes Courants

| Problème | Cause | Solution |
|----------|-------|----------|
| **Timeout 900s** | Trop de pages | Réduire `max_results` ou `last_modified_days` |
| **Memory limit** | Attachments lourds | Filtrer `file_extensions`, limiter taille |
| **Connection refused** | VPC config | Vérifier `vpc-connector` et firewall |
| **401 Unauthorized** | PAT token | Régénérer token, vérifier Secret Manager |
| **Rate limiting** | Trop d'appels API | Activer `retry_delay_seconds`, réduire workers |

### Optimisations Performance

```json
{
  "_cloud_run_optimizations": {
    "small_spaces": {
      "max_results": 100,
      "parallel_downloads": false,
      "memory": "1Gi"
    },
    "large_spaces": {
      "max_results": 50,
      "last_modified_days": 30,
      "memory": "2Gi",
      "timeout": 900
    }
  }
}
```

## ✅ Validation du Déploiement

### Tests Post-Déploiement

```bash
# 1. Test de santé
curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
  https://kbot-confluence-rag-loader-PROJECT_ID.a.run.app/health

# 2. Test de configuration
curl -X POST \
  -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
  -H "Content-Type: application/json" \
  -d @test-config.json \
  https://kbot-confluence-rag-loader-PROJECT_ID.a.run.app/loader/list/test

# 3. Vérification GCS
gsutil ls gs://kbot-rag-documents/test/

# 4. Validation contenu Markdown
gsutil cat gs://kbot-rag-documents/test/docs/page-example.md
```

Ce déploiement optimise spécifiquement le Confluence Loader pour :
- **Confluence Server on-premise** avec authentification PAT
- **Export Markdown** pour traitement RAG optimal
- **Relations préservées** pour contexte sémantique
- **Cloud Run serverless** avec contraintes mémoire/CPU
- **Monitoring** et alertes pour production
