# Guide du Système d'Exceptions Standardisé

Ce guide présente le système d'exceptions standardisé de kbot-load-scheduler, conçu pour offrir une gestion cohérente des erreurs à travers tous les loaders.

## 🎯 Objectifs

- **Cohérence** : Même interface d'exceptions pour tous les loaders
- **Classification automatique** : Détection automatique de la criticité et retryabilité
- **Contexte enrichi** : Informations détaillées pour le debugging et monitoring
- **Compatibilité** : Maintien des APIs existantes pour une migration transparente
- **Observabilité** : Logging intégré avec niveaux appropriés

## 🏗️ Architecture

### Hiérarchie des Exceptions

```
LoaderException (base)
├── LoaderConfigurationError
├── LoaderAuthenticationError
├── LoaderPermissionError
├── LoaderNetworkError
├── LoaderTimeoutError
├── LoaderRateLimitError
├── LoaderNotFoundError
├── LoaderValidationError
└── LoaderCircuitBreakerError

HttpException (HTTP générique)
├── HttpClientError (4xx)
├── HttpServerError (5xx)
├── HttpAuthenticationError (401)
├── HttpPermissionError (403)
├── HttpNotFoundError (404)
├── HttpRateLimitError (429)
└── HttpTimeoutError (408, 504)

ConfluenceException (spécialisé)
├── ConfluenceClientException
├── ConfluenceAuthenticationError
├── ConfluencePermissionError
├── ConfluenceNotFoundError
├── ConfluenceRateLimitError
├── ConfluenceTimeoutError
└── ConfluenceConfigurationError

SharepointException (spécialisé)
├── SharepointClientException
├── SharepointAuthenticationError
├── SharepointPermissionError
├── SharepointNotFoundError
├── SharepointRateLimitError
└── SharepointTimeoutError

BasicException (spécialisé)
├── BasicApiException
├── BasicAuthenticationError
├── BasicPermissionError
├── BasicNotFoundError
├── BasicRateLimitError
└── BasicTimeoutError
```

## 🚀 Usage

### Import des Exceptions

```python
from kbotloadscheduler.exceptions import (
    # Exceptions de base
    LoaderException,
    LoaderConfigurationError,
    LoaderAuthenticationError,
    
    # Exceptions HTTP
    HttpException,
    HttpAuthenticationError,
    
    # Exceptions spécialisées
    ConfluenceClientException,
    ConfluenceAuthenticationError,
    SharepointException,
    BasicApiException
)
```

### Gestion d'Erreurs Basique

```python
try:
    result = loader.perform_operation()
except LoaderAuthenticationError as e:
    logger.error(f"Authentification échouée: {e}")
    # Logique de re-authentification
except LoaderPermissionError as e:
    logger.warning(f"Permissions insuffisantes: {e}")
    # Logique de fallback ou escalade
except LoaderException as e:
    logger.error(f"Erreur loader: {e}")
    # Gestion générique
```

### Gestion d'Erreurs Avancée

```python
try:
    result = loader.perform_operation()
except LoaderException as e:
    # Classification automatique
    if e.is_critical:
        logger.error(f"Erreur critique: {e}")
        # Escalade immédiate
    else:
        logger.warning(f"Erreur non-critique: {e}")
    
    if e.is_retryable:
        logger.info(f"Erreur retryable: {e}")
        # Logique de retry
    
    # Accès au contexte
    context = e.get_context()
    logger.debug(f"Contexte: {context}")
```

## 🔧 Fonctionnalités Avancées

### Classification Automatique

Les exceptions sont automatiquement classifiées selon :

- **Criticité** (`is_critical`) : Détermine si l'erreur nécessite une attention immédiate
- **Retryabilité** (`is_retryable`) : Indique si l'opération peut être retentée

### Contexte Enrichi

Chaque exception peut contenir un contexte avec des informations supplémentaires :

```python
exception.add_context("url", "https://api.example.com/endpoint")
exception.add_context("status_code", 429)
exception.add_context("retry_after", 60)

context = exception.get_context()
# {'url': 'https://api.example.com/endpoint', 'status_code': 429, 'retry_after': 60}
```

### Factory Methods

Création d'exceptions depuis des réponses HTTP :

```python
# Pour Confluence
exception = ConfluenceClientException.from_http_error(response)

# Pour SharePoint
exception = SharepointException.from_response(url, response)

# Pour Basic
exception = BasicApiException.from_response(url, response)
```

## 🔄 Migration depuis les Anciennes Exceptions

### Compatibilité Backward

Les nouvelles exceptions maintiennent la compatibilité avec les anciennes APIs :

```python
# Ancienne API (toujours supportée)
raise ConfluenceClientException("Message d'erreur")
raise SharepointException(url, response)
raise BasicApiException(url, response)

# Nouvelle API (recommandée)
raise ConfluenceClientException(
    "Message d'erreur",
    operation="confluence_operation",
    resource="https://confluence.example.com/page/123",
    is_critical=True
)
```

### Utilitaires de Migration

```python
from kbotloadscheduler.exceptions.utils import (
    create_http_exception,
    is_retryable_exception,
    migrate_legacy_exception
)

# Création d'exception depuis Response
exception = create_http_exception(response, "confluence")

# Test de retryabilité
if is_retryable_exception(exception):
    # Logique de retry

# Migration d'exception legacy
new_exception = migrate_legacy_exception(
    old_exception, 
    "confluence",
    operation="get_page",
    resource="page_123"
)
```

## 📊 Monitoring et Observabilité

### Logging Intégré

Les exceptions loggent automatiquement avec des niveaux appropriés :

- **Erreurs critiques** : `ERROR`
- **Erreurs non-critiques** : `WARNING`
- **Erreurs retryables** : `INFO`

### Métriques

Utilisez les propriétés des exceptions pour créer des métriques :

```python
def track_exception_metrics(exception):
    metrics.increment(f"loader.exception.{exception.__class__.__name__}")
    
    if exception.is_critical:
        metrics.increment("loader.exception.critical")
    
    if exception.is_retryable:
        metrics.increment("loader.exception.retryable")
    
    # Métriques par opération
    operation = exception.get_context().get("operation", "unknown")
    metrics.increment(f"loader.exception.operation.{operation}")
```

## 🧪 Tests

### Test d'Exceptions

```python
import pytest
from kbotloadscheduler.exceptions import ConfluenceClientException

def test_confluence_exception():
    exception = ConfluenceClientException(
        "Test error",
        operation="test_operation",
        is_critical=True,
        is_retryable=False
    )
    
    assert exception.is_critical
    assert not exception.is_retryable
    assert exception.get_context()["operation"] == "test_operation"
```

### Mock d'Exceptions

```python
from unittest.mock import Mock
import requests

def test_exception_from_response():
    # Mock response
    response = Mock(spec=requests.Response)
    response.status_code = 401
    response.url = "https://api.example.com/test"
    
    # Test factory method
    exception = ConfluenceClientException.from_http_error(response)
    
    assert isinstance(exception, ConfluenceAuthenticationError)
    assert exception.is_critical
    assert not exception.is_retryable
```

## 📚 Références

- **Code source** : `src/kbotloadscheduler/exceptions/`
- **Tests** : `tests/loader/*/test_*_exceptions.py`
- **Exemples** : `examples/exception_handling.py`
- **Documentation API** : Disponible via Swagger UI sur `/docs`
