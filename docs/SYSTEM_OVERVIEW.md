# kbot-load-scheduler : Documentation Complète

## 1. Introduction et Vision (Product Manager)

`kbot-load-scheduler` est un composant central de l'écosystème Knowledge Bot. Sa mission principale est d'orchestrer et d'automatiser le chargement, la synchronisation et la préparation des données provenant de diverses sources de connaissance (Confluence, SharePoint, GCS, sources spécifiques "Basic") pour les rendre exploitables par les systèmes d'indexation et d'embedding du Knowledge Bot.

Ce système vise à garantir que le Knowledge Bot dispose toujours d'informations à jour et pertinentes, en gérant de manière robuste et efficace les cycles de vie des documents. Il est conçu pour être flexible, extensible et résilient, capable de s'adapter à différents types de sources et de volumes de données.

**Objectifs clés :**

- **Automatisation :** Réduire l'intervention manuelle dans le processus de mise à jour des connaissances.
- **Fiabilité :** Assurer un chargement de données constant et résilient aux erreurs.
- **Extensibilité :** Permettre l'intégration facile de nouvelles sources de données.
- **Performance :** Optimiser le processus de chargement pour minimiser les délais de mise à disposition des informations.
- **Traçabilité :** Fournir une visibilité claire sur les opérations de chargement.

## 2. Fonctionnalités Clés

- **Gestion de sources multiples :**
    - **Confluence :** Synchronisation avancée des pages, blogs, pièces jointes, avec gestion des versions et détection des changements.
    - **SharePoint :** Accès et récupération de documents depuis des sites SharePoint.
    - **Google Cloud Storage (GCS) :** Chargement de fichiers directement depuis des buckets GCS.
    - **Basic Loader :** Un chargeur générique pour des sources spécifiques (ex: fiches de procédure Basic) nécessitant une logique d'extraction personnalisée.
- **Orchestration des chargements :**
    - Planification des tâches de synchronisation.
    - Décomposition du processus de chargement en étapes granulaires (lister, comparer, récupérer, traiter, embbeder, supprimer).
    - Gestion d'état intermédiaire via des fichiers sur GCS pour la reprise sur erreur et la distribution des tâches.
- **API RESTful :**
    - Endpoints pour déclencher et suivre les chargements.
    - Intégration avec d'autres services de l'écosystème Knowledge Bot (kbot-back, kbot-embedding).
- **Sécurité et Authentification :**
    - Gestion centralisée des secrets via Google Secret Manager (et un fallback local pour les tests).
    - Authentification aux API via tokens (Okapi JWT, Cloud Run ID tokens).
- **Optimisations et Résilience (particulièrement pour Confluence) :**
    - Pagination parallèle et gestion optimisée des threads.
    - Mécanismes de retry avec backoff exponentiel.
    - Circuit Breaker pour protéger contre les services défaillants.
    - Suivi des changements pour ne traiter que les contenus modifiés.
- **Déploiement sur Cloud Run :** Conçu pour s'exécuter comme un service Cloud Run, avec configuration d'accès VPC et de sidecar proxy pour les appels sortants.
- **Planification via Cloud Scheduler :** Configuration pour déclencher les chargements périodiques par périmètre.

## 3. Architecture Générale

`kbot-load-scheduler` est une application FastAPI construite autour d'un système de dépendances injectées (`dependency_injector`). Elle expose une API REST pour interagir avec des services internes qui orchestrent le chargement des données.

```mermaid
%%{init: {'layout': 'elk', 'elk.algorithm': 'force'}}%%
graph TD
    subgraph Cloud_Platform["<font size=5>☁️</font> Plateforme Cloud Google"]
        direction TB
        GCP_Secret_Manager["🗄️ 'Google Secret Manager'"]
        GCP_Cloud_Run["⚙️ 'Google Cloud Run'"]
        GCP_Cloud_Scheduler["⏰ 'Google Cloud Scheduler'"]
        GCP_GCS["📦 'Google Cloud Storage (Bucket de travail)'"]
    end

    subgraph Knowledge_Bot_Ecosystem["<font size=5>🤖</font> Écosystème Knowledge Bot"]
        direction TB
        KBOT_Back_API["📡 'kbot-back API'"]
        KBOT_Embedding_API["📡 'kbot-embedding API'"]
    end

    subgraph External_Data_Sources["<font size=5>🌐</font> Sources de Données Externes"]
        direction TB
        Confluence_Instance["📄 'Instance Confluence'"]
        SharePoint_Instance["📄 'Instance SharePoint'"]
        Basic_Source_API["📡 'API Source Basic'"]
        GCS_Source_Bucket["📦 'Bucket GCS Source'"]
    end

    subgraph KBOT_Load_Scheduler["<font size=5>⚙️</font> kbot-load-scheduler"]
        direction TB
        API_Endpoints["📍 'API Endpoints (FastAPI)'"]

        subgraph Core_Logic["<font size=5>🧠</font> Logique Métier"]
            Schedule_Service["⚙️ 'ScheduleService'"]
            Sources_Service["⚙️ 'SourcesService'"]
            Loader_Service["⚙️ 'LoaderService'"]
            Document_Service["⚙️ 'DocumentService'"]
            Treatment_File_Manager["📂 'TreatmentFileManager'"]
        end

        subgraph Loaders["<font size=5>📥</font> Chargeurs de Données"]
            Abstract_Loader["📤 'AbstractLoader'"]
            Confluence_Loader["📤 'ConfluenceLoader'"]
            SharePoint_Loader["📤 'SharePointLoader'"]
            GCS_Loader["📤 'GCSLoader'"]
            Basic_Loader["📤 'BasicLoader'"]
        end

        Config_With_Secret["🔑 'ConfigWithSecret'"]
        Container_DI["🛠️ 'Container (Dependency Injection)'"]
    end

    GCP_Cloud_Scheduler -->|"'Déclenche'"| GCP_Cloud_Run
    GCP_Cloud_Run -->|"'Héberge'"| KBOT_Load_Scheduler

    API_Endpoints --> Schedule_Service
    API_Endpoints --> Sources_Service
    API_Endpoints --> Loader_Service
    API_Endpoints --> Document_Service

    Schedule_Service --> Treatment_File_Manager
    Schedule_Service --> Sources_Service
    Schedule_Service --> Loader_Service
    Schedule_Service --> Document_Service
    Schedule_Service --> KBOT_Back_API

    Sources_Service --> KBOT_Back_API
    Sources_Service --> Treatment_File_Manager

    Loader_Service --> Loaders
    Loader_Service --> Treatment_File_Manager
    Loaders --> External_Data_Sources
    Loaders --> Config_With_Secret

    Document_Service --> KBOT_Embedding_API
    Document_Service --> Treatment_File_Manager

    Treatment_File_Manager -.->|"'Lit/Écrit'"| GCP_GCS

    Config_With_Secret -->|"'Lit'"| GCP_Secret_Manager
    Config_With_Secret -->|"'Lit'"| Local_Secrets_Tests["🔑 'Secrets Locaux (tests)'"]

    Confluence_Loader -->|"'Appels API'"| Confluence_Instance
    SharePoint_Loader -->|"'Appels API'"| SharePoint_Instance
    Basic_Loader -->|"'Appels API'"| Basic_Source_API
    GCS_Loader -->|"'Lit'"| GCS_Source_Bucket

    %% Styling
    style Cloud_Platform fill:#1E3A5F,stroke:#333,stroke-width:2px,color:#E0E0E0,fill-opacity:0.9
    style Knowledge_Bot_Ecosystem fill:#3D2B56,stroke:#333,stroke-width:2px,color:#E0E0E0,fill-opacity:0.9
    style External_Data_Sources fill:#4A4A4A,stroke:#333,stroke-width:2px,color:#E0E0E0,fill-opacity:0.9
    style KBOT_Load_Scheduler fill:#2E4B6A,stroke:#333,stroke-width:2px,color:#E0E0E0,fill-opacity:0.9
    style Core_Logic fill:#3D2B56,stroke:#333,stroke-width:2px,color:#E0E0E0,fill-opacity:0.9
    style Loaders fill:#4A4A4A,stroke:#333,stroke-width:2px,color:#E0E0E0,fill-opacity:0.9
```

[Mermaid Live](https://mermaid.live/view#pako:eNq1WEtv20YQ_isLBoFcQHIkUQ-LSA1YD7tGbNSoDBRoVRArck2xprjqcplYsX1okUuBFmnhos9DUiB9-NZj7_on_gPNT-gslyuJL9cHV7pQM_PNa2dmhzrXLGoTzdAchmcTdNwf-Qg-QTiWhJ5HQ9s88jA_oWz68Uh7fEJ9jgL3OXm3uX3z4-f__P3y8SNB20ZCiggxImFoj1LHIyPtE6lUfGyXEYu71EfH3RV1r3dkDonFCDcPsY8dwsDS21c_vADtqCTVICmAYoFSQq1QIF39IPQBe_PzT-tQ6Q6wilBDa0Ls0Ivs3rz8KwVccrPwvd4wcvXq9zSGUwZ-oo1uaJ2C2zZBnOGn2PXeWWkhvj3yUxl_4tNnHrEdYnYpNwcWDeYBJ5nUv3315nuV-MWXUmrxJ6R-iUeAv0Pun3TfPza72Do1d472ZSy_otLpmPLKGKgIqMmwI8BgOia27fpOFkUUKwnNi3VwxgnzsWf2McfmkIbMIkE20q--UZHGIiKbfer7i2t4lkpIcIdYe9Q_8ULiW8Tc9wOO4UH6_gKVFGFNKBn3cIIZOaKuz28Br4SS4C4OXCuOMJEyeI6jkjLpEhsqkKyjZbHFZQUCMfw_qkoc2gHFyVJPNnPUMyrT0VF6AKgECnCHBEM05sC3ZyIBgfT1axnjkoo2dnHAgRT3wQq8GjqUEXDWca1sLfzxWnkoBD4LCTpcXHM35V10XrHfMFnYUzc6LDUWFCvmlLJYWWd5UMkpRIocE5YDlIxCXJ9a4ZRAbWWRilWIPWYE8wi860K86yP06gtUWrIFN3d8rlVM4iCkzzkdefWbOoUeFLxDQpboyYyHO-MApp_FTalRuvYGKiOmS3I2srWGTSFXnCLsWr-msCtOEVb0XQoEpCJp2dwp-YiYh0glWwTiOuaHLp_Ed2Ck4rtvZYyuIziSkdQDXI5dH4qtL8fJL6-jclnS0UafzMAaJGmO9v1PZb_mXz85VyGqVLYvRlqpv7i2PNAxEZV3kbxq02AgKdh7i-sxgdKIQDnjR1lOjAwBzjRuoVyySYvEkh1ZJJXuP-Ve2plIOL_hbgHkupormedtrmDW4ULRxBW_DCzpUp5gkVhR-BKRjGAtqKCQf1s-Y2wkl7svZMWyLaV8SydtFXVioblF-PbY87mosil74sDlj2BZYy5fthKMFQXOuq166WANkNyT74o8oBakTfIC85gE8fUsxkxMjWTCM7TBBTdxO2fGsFK_M5sRL4gXvYu8_SquovQsLlCQs2NJBesjtgCbXrHUbBqmYKtkpnYrFe3Dh7C9zz2ohXiJgh8k9SaETlzPMx7UBvpOc7cMlxg9JcYDXdfj58oz1-YToz47K1vUo8x4MKiKb1ngKnSGLZfPjepmZ91Eweof29L79W6zdV-2clspttTYEd97iyo7-2M79UGj27o3O6ut8X9JmBow95UiraxNCZti14a373NhaaTxCZnC-7IBjzZmpyOtLOkentOQSwbxgD7yLwGPQ06Hc9_SDM5CUtbCmQ1v4H0Xw_Y2VcQZ9jXjXDvTjFq9sanXOtVOvd3oNFutWrOszTWjUtcbm1ud6pbe2aq1t1rtTvuyrD2nFFTUNuvNdqfV0qsNvaZ3Om29rBHbhXfbQ_m3QfTvQWTkowggbTIaOhPNOMFesHRrEMGWRCYWE9ajoc_BTLUJvjhM5EJouPwXeGWY5Q)

**Flux principaux :**

1. **Déclenchement :** `Cloud Scheduler` appelle périodiquement un endpoint de `kbot-load-scheduler` (ex: `/schedule/treatments/{perimeter}/launch`).
2. **Initialisation du chargement :**
    - Le `ScheduleService` est activé.
    - Il interroge le `SourcesService` qui, via le `KbotBackApi`, récupère la liste des sources à synchroniser pour un périmètre donné.
    - Pour chaque source, le `TreatmentFileManager` crée un fichier `getlist.json` sur GCS contenant la configuration de la source. Ce fichier sert de tâche pour l'étape suivante.
3. **Logique de traitement des tâches (via `TreatmentFileManager`) :** Le `ScheduleService` identifie les tâches en attente (fichiers `.json` sans fichier `.done` ou `.inprogress` correspondant) dans le bucket de travail GCS. Il lance les appels API correspondants pour chaque étape :
    - **Étape 1: Lister les documents de la source (`/loader/list/{perimeter}`) :**
        - Le `LoaderService` prend le fichier `getlist.json`.
        - Il sélectionne le `Loader` approprié (`ConfluenceLoader`, `SharePointLoader`, etc.) via le `LoaderManager`.
        - Le `Loader` contacte la source de données externe (Confluence, SharePoint), authentifié via `ConfigWithSecret`, pour obtenir la liste brute des documents.
        - Le `TreatmentFileManager` écrit le résultat dans un fichier `list.json` sur GCS.
    - **Étape 2: Comparer et identifier les changements (`/document/compare/{perimeter}`) :**
        - Le `DocumentService` prend le fichier `list.json`.
        - Il appelle le `KbotEmbeddingApi` pour obtenir la liste des documents déjà embeddés pour cette source.
        - Le `DocumentListComparator` compare les deux listes (source vs. embedding) et identifie :
            - Les nouveaux documents à ajouter/mettre à jour.
            - Les documents à supprimer.
        - Le `TreatmentFileManager` crée des fichiers `getdoc.json` (pour chaque document à récupérer) et un fichier `removedoc.json` (liste des documents à supprimer) sur GCS.
    - **Étape 3: Récupérer le contenu des documents (`/loader/document/{perimeter}`) :**
        - Pour chaque `getdoc.json`, le `LoaderService` et le `Loader` approprié récupèrent le contenu réel du document depuis la source externe.
        - Le contenu du document est stocké sur GCS (par le Loader, ex: `ConfluenceLoader` le stocke).
        - Le `TreatmentFileManager` crée un fichier `.metadata.json` sur GCS, pointant vers le contenu stocké et incluant les métadonnées extraites.
    - **Étape 4: Indexation/Embedding (`/document/embedd/{perimeter}`) :**
        - Le `DocumentService` prend le fichier `.metadata.json`.
        - Il appelle le `KbotEmbeddingApi` pour déclencher l'embedding du document.
        - Le `TreatmentFileManager` déplace le fichier `.metadata.json` vers un sous-dossier `docs_done` ou `docs_error`.
    - **Étape 5: Suppression (`/document/remove/{perimeter}`) :**
        - Le `DocumentService` prend le fichier `removedoc.json`.
        - Il appelle le `KbotEmbeddingApi` pour chaque document à supprimer de la base vectorielle.
4. **Gestion d'état :** Chaque étape marque ses fichiers de travail comme `.inprogress` pendant le traitement et `.done` (ou `.error`) à la fin, permettant une reprise et évitant les retraitements.
5. **Nettoyage :** Le `ScheduleService` peut purger les anciens répertoires de traitement sur GCS.

## 4. Composants Détaillés

### 4.1. API Endpoints (`src/kbotloadscheduler/route/`)

L'application FastAPI expose plusieurs groupes de routes :

- **`schedule_routes.py` :**
    - `GET /schedule/treatments/{perimeter_code}/{date}` : Liste les traitements en attente pour un périmètre et une date. Utile pour le débogage ou le déclenchement manuel fin.
    - `POST /schedule/treatments/{perimeter_code}/launch` : Point d'entrée principal déclenché par Cloud Scheduler. Lance l'orchestration complète des traitements pour un périmètre.
- **`sources_routes.py` :**
    - `POST /sources/loadall` : Déclenche la récupération et la création des fichiers `getlist.json` pour toutes les sources de tous les périmètres.
    - `POST /sources/load/{perimeter_code}` : Idem, mais pour un périmètre spécifique.
- **`loader_routes.py` :**
    - `POST /loader/list/{perimeter_code}` : **Endpoint générique** qui fonctionne avec tous les types de loaders. Prend un `get_list_file` contenant la configuration de la source (avec `src_type`) et demande au loader approprié de lister les documents. Le `LoaderManager` sélectionne automatiquement le bon loader (`confluence`, `sharepoint`, `gcs`, `basic`) selon le `src_type`. Écrit un `list.json`.
    - `POST /loader/document/{perimeter_code}` : **Endpoint générique** qui fonctionne avec tous les types de loaders. Prend un `document_get_file` contenant les informations du document à récupérer et sa source. Le système identifie automatiquement le loader approprié, récupère le contenu, le stocke sur GCS et écrit un `.metadata.json`.
- **`document_routes.py` :**
    - `POST /document/compare/{perimeter_code}` : Prend un `repo_document_list_file` (un `list.json`), le compare avec les documents déjà embeddés (via `KbotEmbeddingApi`) et génère des fichiers `getdoc.json` et `removedoc.json`.
    - `POST /document/embedd/{perimeter_code}` : Prend un `embedd_document_metadata_file` (`.metadata.json`) et demande au `KbotEmbeddingApi` de l'indexer.
    - `POST /document/remove/{perimeter_code}` : Prend un `remove_document_file` et demande au `KbotEmbeddingApi` de supprimer les documents listés.

### 4.2. Chargeurs de Données (`src/kbotloadscheduler/loader/`)

Le système utilise un `LoaderManager` pour sélectionner le chargeur approprié en fonction du `src_type` d'une `SourceBean`. Tous les chargeurs héritent d' `AbstractLoader`.

```mermaid
classDiagram
    AbstractLoader <|-- ConfluenceLoader
    AbstractLoader <|-- SharePointLoader
    AbstractLoader <|-- GCSLoader
    AbstractLoader <|-- BasicLoader
    LoaderManager o-- AbstractLoader : gère
    LoaderService ..> LoaderManager : utilise

    class AbstractLoader {
        +String loader_type
        +check_type(source_type)
        +get_document_list(source: SourceBean) List~DocumentBean~
        +get_document(source: SourceBean, document: DocumentBean, output_path: String) Dict
    }

    class ConfluenceLoader {
        +ConfigWithSecret config
        +ConfluenceClient confluence_client
        #get_confluence_client()
        #download_page()
        #download_attachment()
        #create_page_document_bean()
        #create_attachment_document_bean()
        #parse_confluence_datetime()
    }
    note for ConfluenceLoader "Implémentation simple et propre suivant le modèle SharePoint"

    class SharePointLoader {
        +ConfigWithSecret config
        +SharepointClient sharepoint_client
        #get_sharepoint_client()
        #get_files_and_subfolders()
    }

    class BasicLoader {
        +ConfigWithSecret config_with_secret
        +ProcedureSheetClient ps_client
    }

    class GCSLoader {
        #as_document_bean()
    }

    class LoaderManager {
        +get_loader(loader_type: String) AbstractLoader
    }

```

- **`AbstractLoader` :** Définit l'interface commune (`get_document_list`, `get_document`).
- **`ConfluenceLoader` (`src/kbotloadscheduler/loader/confluence/`) :**
    - Implémentation propre et simple suivant le même modèle que `SharePointLoader`.
    - Utilise la bibliothèque `atlassian-python-api` pour interagir avec l'API Confluence.
    - **Caractéristiques principales :**
        - **Architecture modulaire** : Méthodes séparées pour les pages (`download_page`) et les pièces jointes (`download_attachment`).
        - **Support complet** : Télécharge les pages Confluence (format HTML) et leurs pièces jointes.
        - **Gestion robuste des identifiants** : Structure d'ID plus détaillée que SharePoint (`domain|source|doc_type|page_id|item_id`).
        - **Gestion d'erreurs avancée** : Parsing des dates avec fallbacks, gestion gracieuse des erreurs de pièces jointes.
        - **Types bien définis** : Annotations de type complètes et documentation détaillée.
        - **Intégration GCS** : Utilise `gcs_utils` pour le stockage comme les autres loaders.
        - **Authentification flexible** : Support des tokens PAT avec fallback sur les variables d'environnement.
    - **Amélioration par rapport à SharePoint** :
        - Meilleure structure de code avec séparation claire des responsabilités.
        - Gestion plus robuste des types de documents (pages vs pièces jointes).
        - Documentation et annotations de type plus complètes.
        - Parsing des dates plus résilient avec gestion des cas d'erreur.
- **`SharePointLoader` (`src/kbotloadscheduler/loader/sharepoint/`) :**
    - Utilise un `SharepointClient` pour interagir avec l'API SharePoint.
    - Gère l'authentification spécifique à SharePoint (certificats, tokens).
    - Peut naviguer dans les dossiers et lister/télécharger des fichiers.
- **`BasicLoader` (`src/kbotloadscheduler/loader/basic/`) :**
    - Conçu pour des "fiches de procédure Basic".
    - Utilise un `ProcedureSheetClient` pour récupérer des données structurées.
    - Effectue un pré-traitement et une conversion Markdown spécifiques.
- **`GCSLoader` (`src/kbotloadscheduler/loader/gcs/`) :**
    - Chargeur simple pour lister des fichiers depuis un bucket GCS source et les copier vers le bucket de travail.

#### 4.2.1. Patterns d'Implémentation des Loaders

Tous les loaders suivent des patterns communs pour assurer la cohérence et la maintenabilité :

**Pattern d'Identification de Documents :**
- **Séparateur commun** : `ID_SEPARATOR = "|"`
- **Structure SharePoint** : `domain_code|source_code|file_id`
- **Structure Confluence** : `domain_code|source_code|doc_type|page_id|item_id` (plus granulaire)
- **Structure GCS** : `domain_code|source_code|blob_name`
- **Structure Basic** : `domain_code|source_code|content_id`

**Pattern de Métadonnées :**
Tous les loaders retournent les mêmes champs de métadonnées standardisés :
```python
metadata = {
    Metadata.DOCUMENT_ID: document.id,
    Metadata.DOCUMENT_NAME: document.name,
    Metadata.SOURCE_URL: source_url,
    Metadata.LOCATION: destination_path,
    Metadata.CREATION_TIME: creation_time,
    Metadata.MODIFICATION_TIME: modification_time,
}
```

**Pattern d'Intégration GCS :**
- Utilisation de `gcs_utils.create_file_with_*_content()` pour la création de fichiers
- Utilisation de `tidy_file_name()` pour nettoyer les noms de fichiers
- Validation avec `gcs_utils.exists_file_gcs()` après création

**Pattern de Gestion d'Erreurs :**
- Capture des exceptions spécifiques au client (ex: `SharepointException`, `ConfluenceClientException`)
- Transformation en `LoaderException` avec messages informatifs
- Logging approprié pour le debugging

**Comparaison des Implémentations :**

| Aspect | SharePoint | Confluence | Évaluation |
|--------|------------|------------|-----------|
| **Types de retour** | Dict simple | `Dict[str, Any]` typé | ✅ Confluence meilleur |
| **Architecture** | Méthode unique | Méthodes séparées par type | ✅ Confluence plus modulaire |
| **Gestion des dates** | Parsing basique | Parsing avec fallbacks | ✅ Confluence plus robuste |
| **Documentation** | Minimale | Docstrings complètes | ✅ Confluence mieux documenté |
| **Structure d'ID** | Simple (3 parties) | Détaillée (5 parties) | ✅ Confluence plus expressif |
| **Gestion d'erreurs** | Standard | Améliorée avec logging | ✅ Confluence plus résilient |

**Recommandations pour Nouveaux Loaders :**
1. Suivre le modèle Confluence pour l'architecture modulaire
2. Implémenter des annotations de type complètes
3. Ajouter une gestion robuste des dates avec fallbacks
4. Structurer les IDs de manière expressive (inclure le type de document si pertinent)
5. Documenter toutes les méthodes publiques

### 4.3. Services (`src/kbotloadscheduler/service/`)

- **`ScheduleService` :**
    - Cœur de l'orchestration.
    - Récupère les traitements en attente à partir des fichiers sur GCS (`TreatmentFileManager`).
    - Déclenche les appels API appropriés (vers `/loader/*`, `/document/*`) pour chaque étape du processus.
    - Gère le mécanisme de "verrou" (token) pour éviter les exécutions concurrentes pour un même périmètre.
    - Peut purger les anciens répertoires de traitement.
- **`SourcesService` :**
    - Interroge `KbotBackApi` pour obtenir la liste des sources à traiter pour un périmètre.
    - Utilise `TreatmentFileManager` pour créer les fichiers `getlist.json` initiaux.
- **`LoaderService` :**
    - Reçoit les requêtes pour lister ou récupérer des documents.
    - Utilise `LoaderManager` pour déléguer au `Loader` approprié.
    - Interagit avec `TreatmentFileManager` pour lire les fichiers de configuration de tâche (ex: `get_list_file`) et écrire les résultats (ex: `list.json`, `.metadata.json`).
- **`DocumentService` :**
    - Gère la comparaison des listes de documents (source vs. ce qui est déjà embeddé).
    - Interroge `KbotEmbeddingApi` pour obtenir la liste des documents embeddés et pour déclencher l'embedding ou la suppression.
    - Utilise `TreatmentFileManager` pour lire/écrire les fichiers `list.json`, `getdoc.json`, `removedoc.json`, `.metadata.json`.
- **`DocumentListComparator` :** Logique pure pour comparer deux listes de `DocumentBean` et déterminer les ajouts, modifications et suppressions.

### 4.4. Gestion des Secrets (`src/kbotloadscheduler/secret/secret_manager.py`)

- **`ConfigWithSecret` :**
    - Centralise l'accès aux secrets.
    - Implémente une stratégie de fallback :
        1. Tente de lire le secret depuis un fichier local (monté via des secrets Kubernetes/Cloud Run, ou présent localement pour les tests). Chemin défini par `PATH_TO_SECRET_CONFIG`. Ce mécanisme est détaillé dans `conf/etc/secrets/tests/README_SECRETS.md`.
        2. Si non trouvé localement, et si l'environnement n'est pas "tests", tente de lire depuis Google Secret Manager.
    - Fournit des méthodes spécifiques pour récupérer les credentials de chaque type de source (Confluence, SharePoint, Basic, etc.).
    - Utilise `@lru_cache` pour mettre en cache les secrets récupérés et éviter les appels répétés.
    - Pour Confluence, la méthode `get_confluence_credentials` a une logique de fallback interne :
        1. Tente `{perimeter_code}-confluence-credentials` (JSON).
        2. Tente `confluence-credentials` (JSON global).
        3. Tente les anciennes clés séparées (`{perimeter_code}-confluence-pat-token`, etc.).

### 4.5. Configuration et Injection de Dépendances (`src/kbotloadscheduler/dependency/container.py`)

Le système utilise `dependency_injector` pour configurer et injecter tous les composants :

**Configuration des Loaders :**
```python
# Tous les loaders sont configurés avec ConfigWithSecret
sharepoint_loader = providers.Factory(SharepointLoader, config=configWithSecret)
confluence_loader = providers.Factory(ConfluenceLoader, config=configWithSecret)
gcs_loader = providers.Factory(GcsLoader)
basic_loader = providers.Factory(BasicLoader, config=configWithSecret)

# Le LoaderManager reçoit tous les loaders
loader_manager = providers.Factory(
    LoaderManager, 
    gcs=gcs_loader, 
    basic=basic_loader, 
    sharepoint=sharepoint_loader, 
    confluence=confluence_loader
)
```

**Sélection Automatique des Loaders :**
Le `LoaderManager` utilise le `src_type` de la `SourceBean` pour sélectionner le bon loader :
- `src_type: "confluence"` → `ConfluenceLoader`
- `src_type: "sharepoint"` → `SharepointLoader`
- `src_type: "gcs"` → `GcsLoader`
- `src_type: "basic"` → `BasicLoader`

**Configuration Typique des Sources :**
```json
{
  "code": "confluence_space_example",
  "domain_code": "example_domain",
  "perimeter_code": "example_perimeter",
  "src_type": "confluence",
  "configuration": {
    "space_key": "EXAMPLE",
    "confluence_url": "https://example.atlassian.net"
  }
}
```

Cette architecture permet d'ajouter de nouveaux types de loaders sans modifier les endpoints API existants.

### 4.6. Utilitaires GCS

- **`TreatmentFileManager` (`src/kbotloadscheduler/gcs/treatment_file_manager.py`) :**
    - Abstrait la gestion des fichiers d'état sur GCS qui pilotent le workflow.
    - Définit les types de fichiers (`GET_LIST`, `LIST`, `GET_DOC`, `DOCS`, `REMOVE_DOC`) et les conventions de nommage.
    - Gère la création, la lecture et la mise à jour des statuts (`.inprogress`, `.done`, `.error`) de ces fichiers.
    - Construit les chemins de répertoires sur GCS basés sur `perimeter_code`, `load_date`, `domain_code`, `source_code`, `file_type`.
    - Peut lister les répertoires de "load date" à traiter ou à purger.
- **`gcs_utils.py` (`src/kbotloadscheduler/gcs/gcs_utils.py`) :**
    - Fournit des fonctions de bas niveau pour interagir avec GCS (lire, écrire, lister, supprimer des blobs, etc.).

### 4.7. Modèles de Données (`src/kbotloadscheduler/bean/`)

- **`beans.py` :**
    - `SourceBean` : Représente une source de données (configuration, type, etc.).
    - `DocumentBean` : Représente un document unique avec son ID, chemin, nom, date de modification. Utilisé pour la communication entre les étapes et les loaders.
    - `Metadata` : Constantes pour les champs de métadonnées communs.
    - `TreatmentBean` : Représente une tâche/traitement à effectuer (URL à appeler, paramètres).
- **`api_post_beans.py` :** Modèles Pydantic pour les corps de requête POST des API.

### 4.8. Appels API Externes (`src/kbotloadscheduler/apicall/`)

- **`KbotBackApi` :** Client pour interagir avec l'API `kbot-back` (récupérer périmètres, domaines, sources). Gère l'authentification Okapi JWT.
- **`KbotEmbeddingApi` :** Client pour interagir avec l'API `kbot-embedding` (lister documents embeddés, déclencher embedding, suppression).
- **`auth_header.py` :** Construit les headers d'authentification pour les appels vers d'autres services Cloud Run (X-Serverless-Authorization).
- **`orange_jwt.py` :** Gestion spécifique du JWT Orange pour l'authentification Okapi.

## 5. Flux de Données et Processus Typique (Orchestration)

Le processus de chargement est une chorégraphie entre Cloud Scheduler, les endpoints API de `kbot-load-scheduler`, et GCS qui sert de bus de messages/tâches.

```mermaid
sequenceDiagram
    participant CS as "⏰ Cloud Scheduler"
    participant KLS_API as "📍 kbot-load-scheduler API"
    participant SS as "⚙️ ScheduleService"
    participant SrcS as "⚙️ SourcesService"
    participant KBApi as "📡 KbotBackApi"
    participant TFM as "📂 TreatmentFileManager"
    participant LS as "⚙️ LoaderService"
    participant Loader as "📤 AbstractLoader (Confluence, SharePoint, etc.)"
    participant DS as "⚙️ DocumentService"
    participant KEApi as "📡 KbotEmbeddingApi"
    participant GCS as "📦 Google Cloud Storage (Working Bucket)"

    CS ->> KLS_API: "'POST /schedule/treatments/{perimeter}/launch'"
    KLS_API ->> SS: "'launch_treatments_for_perimeter(perimeter)'"
    SS ->> SS: "'__populate_tasks_to_execute__(perimeter)'"
    SS ->> TFM: "'get_load_dates(perimeter)'"
    TFM ->> GCS: "'List date directories'"
    GCS -->> TFM: "'List of load_date'"
    TFM -->> SS: "'List of load_date'"
    loop "'for each load_date'"
        SS ->> TFM: "'get_treatments_files(perimeter, load_date)'"
        TFM ->> GCS: "'List files in load_date dir'"
        GCS -->> TFM: "'Map of files and statuses'"
        TFM -->> SS: "'Map of files'"
    end
    SS ->> SS: "'Sorts tasks by priority (DOCS, REMOVE_DOC, GET_DOC, LIST, GET_LIST)'"

    alt "'No pending tasks AND execution time < MAX'"
        SS ->> SrcS: "'load_sources(perimeter)'"
        SrcS ->> KBApi: "'get_sources(perimeter)'"
        KBApi -->> SrcS: "'List[SourceBean]'"
        loop "'for each SourceBean'"
            SrcS ->> TFM: "'write_get_list(load_date, source)'"
            TFM ->> GCS: "'Create {load_date}/{domain}/{source}/getlist/getlist.json'"
            GCS -->> TFM: "'OK'"
            TFM -->> SrcS: "'OK'"
            SrcS ->> KBApi: "'set_source_done(source.id)'"
            KBApi -->> SrcS: "'OK'"
        end
        SrcS -->> SS: "'List of sources processed'"

        SS ->> TFM: "'purge_old_treatments_files(perimeter, now)'"
        TFM ->> GCS: "'Delete old date directories'"
        GCS -->> TFM: "'OK'"
        TFM -->> SS: "'OK'"
        SS -->> KLS_API: "'{\"status\": \"ok\"}'"
        KLS_API -->> CS: "'Response'"
    else "'For each task type in priority order'"
        SS ->> SS: "'__launch_single_treatment_type__(start_time, \"getlist\", LS.get_document_list, perimeter)'"
        loop "'for each getlist.json file'"
            SS ->> LS: "'get_document_list(perimeter, getlist_file_path)'"
            LS ->> TFM: "'set_in_progress(getlist_file_path)'"
            TFM ->> GCS: "'Create getlist.inprogress'"
            LS ->> TFM: "'read_get_list(getlist_file_path)'"
            TFM ->> GCS: "'Read getlist.json'"
            GCS -->> TFM: "'SourceBean data'"
            TFM -->> LS: "'SourceBean'"
            LS ->> Loader: "'get_document_list(SourceBean)'"
            Loader -->> LS: "'List[DocumentBean]'"
            LS ->> TFM: "'write_document_list(load_date, SourceBean, documents, \"list\")'"
            TFM ->> GCS: "'Create {load_date}/{domain}/{source}/list/list.json'"
            GCS -->> TFM: "'OK'"
            TFM -->> LS: "'Serialized documents'"
            LS ->> TFM: "'set_done(getlist_file_path)'"
            TFM ->> GCS: "'Create getlist.done, delete .inprogress'"
            LS -->> SS: "'List of serialized DocumentBeans'"
        end
        SS ->> SS: "'Log \"getlist\" treatments launched'"

        Note over SS: "Process continues similarly for LIST, GET_DOC, DOCS, REMOVE_DOC steps,\ncalling appropriate services (DS, LS) and APIs (KEApi).\nEach step reads input files from GCS and writes output files to GCS."

        SS -->> KLS_API: "'{\"status\": \"ok\"}'"
        KLS_API -->> CS: "'Response'"
    end
```

[Mermaid Live](https://mermaid.live/view#pako:eNq1WEtv4zYQ_iuELusAihPnuTGKBfJwgkWcB6KgLVoXAiNNbNUyqZLU7noNX3ov9tBTeykKFD332N_TP9D-hA5J62XJTtpug8CWqJlvhsNvPlKeOQEPwek6Er5JgQVwFtGhoJMBI_iXUKGiIEooU-TUI1SSgfPHh9_IaczTkHjBCMI0BjFw6uaXfc8_vn1tff766fvvyPiBq82Y03BTZo4ELZqcvSzWjz_8-fuHPJAH4k0UQKOHCJZ8eCoCkGtcLk-OkyjP72dyifmd0GCMo03m9-dXufG35F4AVRNg6jyK4YoyOmwuQ7-aVR-nD2JNUtYgD_QLOX6QStBALR60Tjl7jM1KucQbUQG3PGLKJaCC9kYT4lk1gzMepDrvdYXp1QvTmzxAGEZsuKI6Fxk90ONXcsH5MIaMJooLLA9pfcbFGBHISRqMQZlkLRD6br56lVGmiygvbm-8e7KVEWVLZeWWW7MERDQBBWK-FdOUBaMXWUIZ5zSY5xkca-EX_v4jF34O0cqvNnIUzysD-H7CkzSmCnxF5Vj6ivvwDoIUB_w17kgX4z8E5WvS-yFCyCYHTSztgSU0Hv1IKqKtSRgJCLB6EcjcWhd6sxzAmPNHkgepApemstoy5jzRFlgbAjQYNZismFq5sNgJpQm6BchGBaVxvsaZRKxw0rOv-NVmfkUTPR3rSllIpKIqlaViNVWh7JUbAgubFt_jQkli1p08TEkiIlwMNSWts5tTzyV3vaubT3s-3rjkondvL_qvvXt7q6_s1C02jZUGveYkwXi6Eyzy8fUZsZSKOCMKy0c-IVfHnzfVXuucJbauk7Qi18Qq46NF0XSWlrp8zZ7wsrq4WYmm1-hLK6knQNlXFYcaewrDil0lpWwR32JBwTddgjFa-fK7xKa5UYNY5s-ppiCQWe4635qFfEIjhhcWZL6FATR-9t3-WvJ6cjWG3Vw2R6_UpsGoofAyL7wfcgYte92OwvoEmxZgKUjO1yJaQ6MvFhp5y_FLQlgiY0M_J6kYgs_jcG1XM_52fT-fQYyWBHFWq9hzqr3cuEuPvYV3edeYDQYDx6qAvsIx_ORj_TmvcjzbKTTCIu87kAlnspA8iCXoB-cZr3W7EjVNQAtVLgZc4Mbc2KvZDrLYhCS2fAxFcX0NhZsIJizwBgvsmoQXHNWXKCdeWzdHuNi2TZe4ZEXr1jqxTHcjeXWq2lz7Xi4PlVDllV-AGUr4CVWjOnX7VUZp0kfMR_4NBRKw9QyEFd2dTSRiGdhTsdEvLHTlX0S-QwDyz_SiED5NfbpaPPrekv2q2dhj34q1KdwbVsKeF8vRjIhn57-6jDfU0IpzNWhJoYv4LsmMpOVwRuCPJN9Guz-ScGe1R2LTOHoPYZH7c_hs1Pu_E1nDYNWsVj5F6yZtL9IvL6lcs01UVKnPh8tiQwrZJ1ayajvGNdfK_gaJZWFu7c5CAs5UxFLca2Q0iWIq4inRIlQchszZaPnYhCc2SKQ7GLCAxrE-EdEEC4HSqksl7SuKxOOWp4VwwxzyULZxxLyjbLTRs6d1TuMQ3fH6DJmk2XnyUfCJfTVBR0NmSXiqCgPF9eN2fVv8n3cWFjquMwGBNA_x5XumhweOGsEEDLgTUjFG-bfjMZ1i1vYBxGONMkd_miruTVngdJVIwXXSRLfP4gU-G8S3M6c7c9453c2j3YP2_s720cHh9s5OZ-_QdaZOt7P9sn10sLf9cv9gp7Pd6ezuz13nPecIsNPe63Q6O7v7nc4h_u_vug6EEe7kV_YnA_PLgYnwhbG3AQVPhyOn-0hx98xy6hm3fFDg_EGc8pQpTGD3wHWGQtdBA8z_BudaFp0)

## 6. Configuration et Déploiement

### 6.1. Configuration Locale (Développement/Test)

- **Variables d'environnement :** Définies via le `Makefile` (pour `make start` ou `make unit-tests`) ou exportées manuellement.
    - `ENV`: `local` ou `tests`.
    - `GCP_PROJECT_ID`: Un ID de projet (peut être factice pour certains tests).
    - `PATH_TO_SECRET_CONFIG`: Pointe vers `conf/etc/secrets/local` ou `conf/etc/secrets/tests`. Ces répertoires simulent la structure des secrets montés dans Cloud Run.
    - URLs des API dépendantes (KBOT_BACK_API_URL, KBOT_EMBEDDING_API_URL, etc.) pointant vers des mocks ou des instances locales/de test.
    - `KBOT_WORK_BUCKET_PREFIX`: Préfixe du bucket GCS de travail (ex: `gs://mon_bucket_dev-[perimeter_code]`).
- **Secrets locaux :** Les fichiers dans `conf/etc/secrets/tests/` (ou `local/`) contiennent les valeurs des secrets. Par exemple, `conf/etc/secrets/tests/basic-client-id/secret` contient l'ID client pour le BasicLoader.
    - Le script `conf/etc/secrets/tests/setup_confluence_secrets.py` aide à configurer les secrets Confluence pour les tests.
- **Lancement :** `make start` ou `uvicorn kbotloadscheduler.main:app --reload`.

### 6.2. Déploiement Cloud Run (via GitLab CI)

Le fichier `.gitlab-ci.yml` définit le pipeline de CI/CD.

- **Environnements :** `dev`, `ppr`, `prd`, chacun avec ses propres variables GCP (`GCP_PROJECT_ID`, `GCP_PROJECT_NUMBER`, URLs des services, etc.).
- **Images Docker :**
    - L'application est packagée dans une image Docker (`Dockerfile`).
    - Une image sidecar proxy (`SIDECAR_PROXY_IMAGE`) est également déployée pour gérer les appels sortants vers des services internes Orange (Okapi, API Basic).
- **Configuration Cloud Run (`.cloud-run-loadscheduler` dans `.gitlab-ci.yml`) :**
    - Nom du service : `kbot-loadscheduler-${ENV_GCP}-cloud-run-v2`.
    - Ingress : `INGRESS_TRAFFIC_INTERNAL_ONLY`.
    - Compte de service : `GCP_SA_CR_EX` (ex: `sa-load-scheduler-cld-run-ex@...`).
    - Variables d'environnement spécifiques à l'environnement GCP déployé.
    - **Montage des secrets :** Les secrets GCP (ex: `kbot_loadscheduler_client_id`, `sharepoint-client-config`) sont montés en tant que volumes dans `/etc/secrets/`, mimant la structure utilisée pour les tests locaux.
    - Configuration VPC pour l'accès réseau.
    - Scaling : min/max instances (actuellement 1 pour les deux).
- **Cloud Scheduler (`.cloud-scheduler-launch-load-scheduler` dans `.gitlab-ci.yml`) :**
    - Des jobs Cloud Scheduler sont définis pour chaque périmètre (ex: `mktsearch`, `ebotman`) et environnement.
    - Ils sont configurés pour appeler l'endpoint `POST /schedule/treatments/{perimeter}/launch` de l'instance Cloud Run appropriée.
    - Utilisent l'authentification OIDC avec le compte de service `GCP_SA_CR_EX`.

### 6.3. Gestion des Dépendances Python

- `requirements.txt` pour les dépendances de production.
- `tests/test-requirements.txt` pour les dépendances de test.
- `pipenv` est utilisé dans le `Makefile` et la CI pour gérer l'environnement virtuel et l'installation des dépendances.

## 7. Tests

Le projet dispose d'une suite de tests robuste.

- **Configuration :** `pytest.ini` définit les chemins Python et les marqueurs de test.
- **Tests Unitaires :** Couvrent la logique des services, des loaders (avec mocks), et des utilitaires.
    - Utilisation intensive de `unittest.mock` (`MagicMock`, `patch`).
    - Les tests de loader utilisent souvent un `MockGCSClient` et un `MockConfigWithSecret` pour isoler les dépendances externes.
    - Le module Confluence a ses propres tests unitaires très complets dans `src/kbotloadscheduler/loader/confluence/tests/`.
- **Tests d'Intégration (spécifiques à Confluence) :**
    - `tests/loader/test_confluence_end_to_end.py` : Teste le flux complet du `ConfluenceLoader` en interaction avec des mocks de l'API Confluence et de GCS.
    - Ces tests valident l'intégration du `ConfluenceLoader` dans l'architecture `kbot-load-scheduler` (récupération de secrets, interaction avec les beans `SourceBean`/`DocumentBean`).
- **Couverture :** La couverture de code est mesurée et peut être exportée (vers BigQuery dans la CI).
- **Linting & Analyse Statique :**
    - `flake8` pour le linting.
    - `bandit` pour l'analyse de sécurité statique (configuration dans `bandit.yaml`).
- **Exécution :** Via `make unit-tests` ou `pytest`.

Un point notable est la gestion des secrets pour les tests : `conf/etc/secrets/tests/` contient des fichiers secrets *mockés* qui sont lus par `ConfigWithSecret` lorsque `ENV=tests`. Ceci évite des appels réels à GCP Secret Manager pendant les tests unitaires et d'intégration. Le `README_SECRETS.md` dans ce dossier explique bien le système.

## 8. Points d'Attention et Pistes d'Amélioration

### 8.1. Troubleshooting des Loaders

**Problèmes Courants et Solutions :**

**Confluence Loader :**
- **Import Error** : Vérifier que l'import dans `container.py` pointe vers `confluence_loader` et non `confluence_loader_simple`
- **Authentification** : Vérifier la configuration des secrets PAT tokens dans Secret Manager ou variables d'environnement
- **Permissions** : S'assurer que le token a les permissions de lecture sur l'espace Confluence spécifié

**SharePoint Loader :**
- **Certificats** : Vérifier la configuration des certificats client dans Secret Manager
- **Permissions** : S'assurer que l'application a les permissions sur le site SharePoint

**Debugging Général :**
1. Vérifier les logs du `LoaderService` pour les erreurs de sélection de loader
2. Contrôler que le `src_type` dans la `SourceBean` correspond exactement aux loaders configurés
3. Tester l'authentification avec les services externes avant le déploiement

### 8.2. Améliorations Futures

- **Complexité de l'orchestration :** Le système de fichiers d'état sur GCS est puissant mais peut devenir complexe à déboguer. Une visualisation de l'état des tâches pourrait être utile.
- **Gestion des erreurs fines :** Améliorer la granularité du reporting d'erreurs pour identifier précisément quelle étape ou quel document a échoué.
- **Monitoring et Alerting :** Bien que le module Confluence ait des health checks, un monitoring plus global de `kbot-load-scheduler` (taux d'erreurs, latences, files d'attente de tâches sur GCS) serait bénéfique.
- **Reprise sur erreur :** Le système de `.done` / `.error` permet une certaine reprise. Évaluer la robustesse pour différents types de pannes (service externe indisponible, bug interne).
- **Scalabilité :** L'architecture actuelle (1 instance Cloud Run) pourrait être un goulot d'étranglement si de nombreux périmètres ou sources très volumineuses sont traités simultanément. Envisager des patterns de distribution de tâches plus avancés (ex: Cloud Tasks, Pub/Sub) si nécessaire.
- **Dépendance `orange_jwt` :** Composant spécifique à l'infrastructure Orange. S'assurer de sa maintenance.
- **Découplage ConfluenceLoader :** Le `ConfluenceLoader` est très riche et pourrait presque être un service à part entière. Son intégration actuelle où `get_document` déclenche potentiellement une synchronisation complète est une particularité à bien comprendre.
- **Idempotence des étapes :** S'assurer que toutes les étapes du traitement peuvent être ré-exécutées sans effets de bord indésirables.
- **Gestion des configurations de source :** Actuellement, les configurations des sources sont passées via des chemins de fichiers GCS dans les requêtes API. Standardiser et sécuriser ce mécanisme.

## 9. Conclusion

`kbot-load-scheduler` est un système d'orchestration sophistiqué et crucial pour l'alimentation en données du Knowledge Bot. Son architecture modulaire, basée sur des loaders interchangeables et un flux de travail piloté par des fichiers d'état sur GCS, lui confère une grande flexibilité. L'intégration du module Confluence, particulièrement complet, ajoute des capacités avancées de synchronisation pour cette source. Le projet est bien structuré pour le déploiement sur GCP Cloud Run et intègre les bonnes pratiques en matière de gestion des secrets, de tests et de CI/CD. Les défis futurs se situeront probablement autour de la scalabilité, du monitoring avancé et de la simplification de la complexité inhérente à un tel orchestrateur.
