#!/bin/bash

# Test script for real Confluence API with local secrets
# This script tests real Confluence integration with:
# - Local secrets from conf/etc/secrets/local
# - Real GCS access (with gsutil fallback if needed)
# - Real Confluence API calls (no mocking)

echo "🔧 Starting server for REAL Confluence testing"
echo "================================================"
echo "Configuration:"
echo "  ENV=local (use local secrets)"
echo "  USE_MOCKS=false (no mocking - use real APIs)"
echo "  SKIP_EXTERNAL_AUTH=false (use real authentication)"
echo "  GCS: Real access with gsutil fallback support"
echo "  Confluence: Real API calls to configured instance"
echo

# Environment variables for local testing with real Confluence
export ENV=local
export SKIP_EXTERNAL_AUTH=true  # Skip OKAPI auth for BasicLoader
export USE_MOCKS=false
export MOCK_CONFLUENCE=false  # Explicitly disable Confluence mocking
export FORCE_REAL_CONFLUENCE=true  # Force ConfluenceLoader to use real client
# export GCS_FORCE_GSUTIL_FALLBACK=true # Force to use gsutil as fallback

# GCS and project configuration
export GCP_PROJECT_ID=ofr-ekb-knowledgebot-dev
export KBOT_WORK_BUCKET_PREFIX="gs://ofr-ekb-knowledgebot-work-dev-[perimeter_code]-dev/test/"

# Path to local secrets
export PATH_TO_SECRET_CONFIG=/Users/<USER>/IdeaProjects/kbot-load-scheduler/conf/etc/secrets/local

# Required variables for BasicLoader (even though we won't use it)
export KBOT_BACK_API_CLOUD_RUN_URL="http://localhost:8091"
export KBOT_BACK_API_URL="http://localhost:8091"
export KBOT_EMBEDDING_API_URL="http://localhost:8093"
export URL_SERVICE_BASIC="https://newbasicqualif-m2m.int.api.hbx.geo.infra.ftgroup/api"
export URL_SERVICE_SHAREPOINT="https://orange0.sharepoint.com/"
export URL_SERVICE_CONFLUENCE="https://espace.agir.orange.com/"
export SHAREPOINT_IN_DOC_ID_USE_PROPERTY="UniqueId"
export OKAPI_URL_BASIC="https://okapi-v2.api.hbx.geo.infra.ftgroup/v2/token"
export SERVICE_SCOPE_BASIC="api-newbasicqualif-v1-int:readonly"
export TIMEOUT_BASIC=120

echo "Environment variables set:"
echo "  ENV=${ENV}"
echo "  USE_MOCKS=${USE_MOCKS}"
echo "  SKIP_EXTERNAL_AUTH=${SKIP_EXTERNAL_AUTH}"
echo "  GCP_PROJECT_ID=${GCP_PROJECT_ID}"
echo "  PATH_TO_SECRET_CONFIG=${PATH_TO_SECRET_CONFIG}"
echo

# Change to source directory
cd /Users/<USER>/IdeaProjects/kbot-load-scheduler/src

echo "Starting server..."
echo "Access logs will show real Confluence API calls"
echo "Press Ctrl+C to stop"
echo

# Start the server
pipenv run uvicorn kbotloadscheduler.main:app --host 0.0.0.0 --port 8092 --reload
