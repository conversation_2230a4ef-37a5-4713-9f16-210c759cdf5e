PYTHON_VERSION=3.12
ROOT_DIR:=$(realpath $(shell dirname $(realpath $(firstword $(MAKEFILE_LIST)))))

.PHONY: all-tests bandit black black-fix debug-tests fix-all fix-fstrings flake init \
        integration-tests integration-tests-confluence lint-check ruff ruff-check ruff-fix \
        setup-mock-data start start-mock start-mock-full start-no-auth start-real-confluence start-test start-test-hybrid \
        test-bearer-auth test-confluence-all test-confluence-full test-confluence-functional \
        test-confluence-quick test-confluence-simple test-confluence-workflow test-credentials \
        test-direct-bearer test-intermediate-bearer test-mock-endpoint test-ultra-minimal \
        test-vodcastv-ravenne-integration trim-whitespace unit-tests

BACK_PORT=8091
LOAD_SCHEDULER_PORT=8092
EMBEDDING_PORT=8093
RETRIEVAL_PORT=8094


init:
	python -V
	pip -V
	pip install pipenv --upgrade
	python -m pip install --upgrade pip
	pipenv --python ${PYTHON_VERSION}
	pipenv run pip install -r requirements.txt
	pipenv run pip install -r tests/test-requirements.txt


start:
	pipenv run pip freeze | grep storage
	export ENV="local" && \
	export SKIP_EXTERNAL_AUTH=true && \
	export GCP_PROJECT_ID="ofr-ekb-knowledgebot-dev" && \
	export PATH_TO_SECRET_CONFIG=${ROOT_DIR}/conf/etc/secrets/local && \
	export KBOT_BACK_API_CLOUD_RUN_URL="http://localhost:${BACK_PORT}" && \
	export KBOT_BACK_API_URL="http://localhost:${BACK_PORT}" && \
	export KBOT_EMBEDDING_API_URL="http://localhost:${EMBEDDING_PORT}" && \
	export KBOT_WORK_BUCKET_PREFIX="gs://ofr-ekb-knowledgebot-work-dev-[perimeter_code]-dev" && \
	export URL_SERVICE_BASIC="https://newbasicqualif-m2m.int.api.hbx.geo.infra.ftgroup/api" && \
	export URL_SERVICE_SHAREPOINT="https://orange0.sharepoint.com/" && \
	export URL_SERVICE_CONFLUENCE="https://espace.agir.orange.com/" && \
	export SHAREPOINT_IN_DOC_ID_USE_PROPERTY="UniqueId" && \
	export OKAPI_URL_BASIC="https://okapi-v2.api.hbx.geo.infra.ftgroup/v2/token" && \
	export SERVICE_SCOPE_BASIC="api-newbasicqualif-v1-int:readonly" && \
	export TIMEOUT_BASIC=120 && \
	cd src && pwd && \
	pipenv run uvicorn kbotloadscheduler.main:app --reload --port ${LOAD_SCHEDULER_PORT}


# Start server in test mode using local secrets (avoids Google Cloud authentication)
start-test:
	pipenv run pip freeze | grep storage
	export ENV="tests" && \
	export GCP_PROJECT_ID="ofr-ekb-knowledgebot-dev" && \
	export PATH_TO_SECRET_CONFIG=${ROOT_DIR}/conf/etc/secrets/tests && \
	export KBOT_BACK_API_CLOUD_RUN_URL="http://localhost:${BACK_PORT}" && \
	export KBOT_BACK_API_URL="http://localhost:${BACK_PORT}" && \
	export KBOT_EMBEDDING_API_URL="http://localhost:${EMBEDDING_PORT}" && \
	export KBOT_WORK_BUCKET_PREFIX="gs://ofr-ekb-knowledgebot-work-dev-[perimeter_code]-dev" && \
	export URL_SERVICE_BASIC="https://newbasicqualif-m2m.int.api.hbx.geo.infra.ftgroup/api" && \
	export URL_SERVICE_SHAREPOINT="https://orange0.sharepoint.com/" && \
	export SHAREPOINT_IN_DOC_ID_USE_PROPERTY="UniqueId" && \
	export OKAPI_URL_BASIC="https://okapi-v2.api.hbx.geo.infra.ftgroup/v2/token" && \
	export SERVICE_SCOPE_BASIC="api-newbasicqualif-v1-int:readonly" && \
	export TIMEOUT_BASIC=120 && \
	cd src && pwd && \
	pipenv run uvicorn kbotloadscheduler.main:app --reload --port ${LOAD_SCHEDULER_PORT}

# Start server with GCS mocking for local testing (no real GCP credentials needed)
start-mock:
	@echo "Starting server with GCS mocking enabled for local testing..."
	pipenv run pip freeze | grep storage
	export ENV="tests" && \
	export GCP_PROJECT_ID="mock-project" && \
	export PATH_TO_SECRET_CONFIG=${ROOT_DIR}/conf/etc/secrets/tests && \
	export KBOT_BACK_API_CLOUD_RUN_URL="http://localhost:${BACK_PORT}" && \
	export KBOT_BACK_API_URL="http://localhost:${BACK_PORT}" && \
	export KBOT_EMBEDDING_API_URL="http://localhost:${EMBEDDING_PORT}" && \
	export KBOT_WORK_BUCKET_PREFIX="gs://mock-bucket-[perimeter_code]" && \
	export URL_SERVICE_BASIC="https://mock-basic-api.example.com/api" && \
	export URL_SERVICE_SHAREPOINT="https://mock.sharepoint.com/" && \
	export SHAREPOINT_IN_DOC_ID_USE_PROPERTY="UniqueId" && \
	export OKAPI_URL_BASIC="https://mock-okapi.example.com/v2/token" && \
	export SERVICE_SCOPE_BASIC="mock-scope:readonly" && \
	export TIMEOUT_BASIC=120 && \
	export USE_MOCKS=true && \
	export SKIP_EXTERNAL_AUTH=true && \
	cd src && pwd && \
	pipenv run uvicorn kbotloadscheduler.main:app --reload --port ${LOAD_SCHEDULER_PORT}


# Start server with full mocks for testing (no external calls)
start-mock-full:
	@echo "Starting server with full mocking (no external calls)..."
	pipenv run pip freeze | grep storage
	export ENV="local" && \
	export GCP_PROJECT_ID="mock-project" && \
	export PATH_TO_SECRET_CONFIG=${ROOT_DIR}/conf/etc/secrets/local && \
	export KBOT_BACK_API_CLOUD_RUN_URL="http://localhost:${BACK_PORT}" && \
	export KBOT_BACK_API_URL="http://localhost:${BACK_PORT}" && \
	export KBOT_EMBEDDING_API_URL="http://localhost:${EMBEDDING_PORT}" && \
	export KBOT_WORK_BUCKET_PREFIX="gs://mock-bucket-[perimeter_code]" && \
	export URL_SERVICE_BASIC="https://mock-basic-api.example.com/api" && \
	export URL_SERVICE_SHAREPOINT="https://mock.sharepoint.com/" && \
	export URL_SERVICE_CONFLUENCE="https://espace.agir.orange.com/" && \
	export SHAREPOINT_IN_DOC_ID_USE_PROPERTY="UniqueId" && \
	export OKAPI_URL_BASIC="https://mock-okapi.example.com/v2/token" && \
	export SERVICE_SCOPE_BASIC="mock-scope:readonly" && \
	export TIMEOUT_BASIC=120 && \
	export USE_MOCKS=true && \
	export MOCK_CONFLUENCE=true && \
	export SKIP_EXTERNAL_AUTH=true && \
	cd src && pwd && \
	pipenv run uvicorn kbotloadscheduler.main:app --reload --port ${LOAD_SCHEDULER_PORT}

# Start server in hybrid mode: mock GCS/Auth + real Confluence using local secrets (requires PAT token)
# MOCK_CONFLUENCE=false: "Use real Confluence API instead of mock documents"
# REAL_CONFLUENCE_MODE=true: "Provide realistic test configuration for Confluence integration tests"
start-test-hybrid:
	@echo "Starting server in test-hybrid mode (matching start_server.py config)..."
	@echo "Using secrets from: ${ROOT_DIR}/conf/etc/secrets/tests"
	pipenv run pip freeze | grep storage
	export ENV="tests" && \
	export GCP_PROJECT_ID="mock-project" && \
	export PATH_TO_SECRET_CONFIG=${ROOT_DIR}/conf/etc/secrets/tests && \
	export KBOT_BACK_API_CLOUD_RUN_URL="http://localhost:${BACK_PORT}" && \
	export KBOT_BACK_API_URL="http://localhost:${BACK_PORT}" && \
	export KBOT_EMBEDDING_API_URL="http://localhost:${EMBEDDING_PORT}" && \
	export KBOT_WORK_BUCKET_PREFIX="gs://mock-bucket-[perimeter_code]" && \
	export URL_SERVICE_BASIC="https://mock-basic-api.example.com/api" && \
	export URL_SERVICE_SHAREPOINT="https://mock.sharepoint.com/" && \
	export URL_SERVICE_CONFLUENCE="https://espace.agir.orange.com/" && \
	export SHAREPOINT_IN_DOC_ID_USE_PROPERTY="UniqueId" && \
	export OKAPI_URL_BASIC="https://mock-okapi.example.com/v2/token" && \
	export SERVICE_SCOPE_BASIC="mock-scope:readonly" && \
	export TIMEOUT_BASIC=120 && \
	export USE_MOCKS=true && \
	export MOCK_CONFLUENCE=false && \
	export REAL_CONFLUENCE_MODE=true && \
	export SKIP_EXTERNAL_AUTH=true && \
	cd src && pwd && \
	pipenv run uvicorn kbotloadscheduler.main:app --reload --port ${LOAD_SCHEDULER_PORT} --host 0.0.0.0

# Start server with real Confluence API using local secrets (no mocking)
start-real-confluence:
	@echo "🔧 Starting server for REAL Confluence testing"
	@echo "================================================"
	@echo "Configuration:"
	@echo "  ENV=local (use local secrets)"
	@echo "  USE_MOCKS=false (no mocking - use real APIs)"
	@echo "  SKIP_EXTERNAL_AUTH=true (skip auth for BasicLoader)"
	@echo "  GCS: Real access with gsutil fallback support"
	@echo "  Confluence: Real API calls to configured instance"
	@echo ""
	pipenv run pip freeze | grep storage
	export ENV="local" && \
	export SKIP_EXTERNAL_AUTH=true && \
	export USE_MOCKS=false && \
	export FORCE_REAL_CONFLUENCE=true && \
	export GCP_PROJECT_ID="ofr-ekb-knowledgebot-dev" && \
	export KBOT_WORK_BUCKET_PREFIX="gs://ofr-ekb-knowledgebot-work-dev-[perimeter_code]-dev/test/" && \
	export PATH_TO_SECRET_CONFIG=${ROOT_DIR}/conf/etc/secrets/local && \
	export KBOT_BACK_API_CLOUD_RUN_URL="http://localhost:${BACK_PORT}" && \
	export KBOT_BACK_API_URL="http://localhost:${BACK_PORT}" && \
	export KBOT_EMBEDDING_API_URL="http://localhost:${EMBEDDING_PORT}" && \
	export URL_SERVICE_BASIC="https://newbasicqualif-m2m.int.api.hbx.geo.infra.ftgroup/api" && \
	export URL_SERVICE_SHAREPOINT="https://orange0.sharepoint.com/" && \
	export URL_SERVICE_CONFLUENCE="https://espace.agir.orange.com/" && \
	export SHAREPOINT_IN_DOC_ID_USE_PROPERTY="UniqueId" && \
	export OKAPI_URL_BASIC="https://okapi-v2.api.hbx.geo.infra.ftgroup/v2/token" && \
	export SERVICE_SCOPE_BASIC="api-newbasicqualif-v1-int:readonly" && \
	export TIMEOUT_BASIC=120 && \
	cd src && pwd && \
	echo "Environment variables set:" && \
	echo "  ENV=local" && \
	echo "  USE_MOCKS=false" && \
	echo "  SKIP_EXTERNAL_AUTH=true" && \
	echo "  GCP_PROJECT_ID=ofr-ekb-knowledgebot-dev" && \
	echo "  PATH_TO_SECRET_CONFIG=${ROOT_DIR}/conf/etc/secrets/local" && \
	echo "" && \
	echo "Starting server..." && \
	echo "Access logs will show real Confluence API calls" && \
	echo "Press Ctrl+C to stop" && \
	echo "" && \
	pipenv run uvicorn kbotloadscheduler.main:app --host 0.0.0.0 --port ${LOAD_SCHEDULER_PORT} --reload

## Runs unit tests for src (excludes integration tests)
## KBOT_BACK_API_URL : associated to tests/data/kbot_back_api_test_data
## KBOT_WORK_BUCKET_PREFIX : associated to bucket given to mock-gcs
unit-tests:
	export ENV="tests" && \
	export GCP_PROJECT_ID="ofr-ekb-knowledgebot-test" && \
	export ROOT_TEST=${ROOT_DIR}/tests && \
	export PATH_TO_SECRET_CONFIG=${ROOT_DIR}/conf/etc/secrets/tests && \
	export KBOT_BACK_API_URL="https://kbot-back-api:8080" && \
	export KBOT_EMBEDDING_API_URL="https://kbot-embedding-api:8081" && \
	export URL_SERVICE_SHAREPOINT="https://orange0.sharepoint.com/" && \
	export SHAREPOINT_IN_DOC_ID_USE_PROPERTY="UniqueId" && \
	export KBOT_WORK_BUCKET_PREFIX="gs://mon_bucket-[perimeter_code]" && \
	pipenv run python -m pytest -v --cov-config=.coveragerc --cov-report=html --cov-report term --cov-report=xml --cov=src/ tests --ignore=tests/integration

## Integration tests - run manually, require external dependencies
integration-tests:
	@echo "Running integration tests (requires credentials and external services)..."
	export ENV="tests" && \
	export GCP_PROJECT_ID="ofr-ekb-knowledgebot-test" && \
	export ROOT_TEST=${ROOT_DIR}/tests && \
	export PATH_TO_SECRET_CONFIG=${ROOT_DIR}/conf/etc/secrets/tests && \
	export KBOT_BACK_API_URL="https://kbot-back-api:8080" && \
	export KBOT_EMBEDDING_API_URL="https://kbot-embedding-api:8081" && \
	export URL_SERVICE_SHAREPOINT="https://orange0.sharepoint.com/" && \
	export SHAREPOINT_IN_DOC_ID_USE_PROPERTY="UniqueId" && \
	export KBOT_WORK_BUCKET_PREFIX="gs://mon_bucket-[perimeter_code]" && \
	pipenv run python -m pytest -v tests/integration

## Run integration tests for specific services
integration-tests-confluence:
	@echo "Running Confluence integration tests..."
	export ENV="tests" && \
	export PATH_TO_SECRET_CONFIG=${ROOT_DIR}/conf/etc/secrets/tests && \
	pipenv run python -m pytest -v tests/integration/confluence

## Test credentials setup (safe to run without external dependencies)
test-credentials:
	@echo "Testing credential configuration..."
	export ENV="tests" && \
	export PATH_TO_SECRET_CONFIG=${ROOT_DIR}/conf/etc/secrets/tests && \
	pipenv run python -m pytest -v tests/integration/confluence/test_credentials.py

## Run all tests (unit + integration)
all-tests:
	@echo "Running ALL tests (unit + integration)..."
	export ENV="tests" && \
	export GCP_PROJECT_ID="ofr-ekb-knowledgebot-test" && \
	export ROOT_TEST=${ROOT_DIR}/tests && \
	export PATH_TO_SECRET_CONFIG=${ROOT_DIR}/conf/etc/secrets/tests && \
	export KBOT_BACK_API_URL="https://kbot-back-api:8080" && \
	export KBOT_EMBEDDING_API_URL="https://kbot-embedding-api:8081" && \
	export URL_SERVICE_SHAREPOINT="https://orange0.sharepoint.com/" && \
	export SHAREPOINT_IN_DOC_ID_USE_PROPERTY="UniqueId" && \
	export KBOT_WORK_BUCKET_PREFIX="gs://mon_bucket-[perimeter_code]" && \
	pipenv run python -m pytest -v --cov-config=.coveragerc --cov-report=html --cov-report term --cov-report=xml --cov=src/ tests

## Debug tests - development and debugging only (not part of formal test suite)
debug-tests:
	@echo "Running debug tests (development/debugging only)..."
	@for test in debug-tests/test_*.py; do \
		if [ -f "$$test" ]; then \
			echo "Running $$test..."; \
			pipenv run python "$$test" || echo "Debug test $$test failed (continuing...)"; \
			echo ""; \
		fi; \
	done
	@echo "Debug tests completed."

flake:
	rm -f flake8_report_src.txt && \
	pipenv run flake8 --output-file=flake8_report_src.txt --format=pylint --tee --exit-zero --statistics src && \
	if [ ! -s flake8_report_src.txt ]; then echo "SCAN FLAKE8 OK FOR SRC DIRECTORY"; fi && \
	rm -f flake8_report_tests.txt && \
	pipenv run flake8 --output-file=flake8_report_tests.txt --format=pylint --tee --exit-zero --statistics tests && \
	if [ ! -s flake8_report_tests.txt ]; then echo "SCAN FLAKE8 OK FOR TESTS DIRECTORY"; fi

bandit:
	touch bandit_report.csv
	pipenv run bandit -c bandit.yaml -f csv -o bandit_report.csv -r src --exit-zero
	cat bandit_report.csv

## Code formatting with black
black:
	pipenv run black --check --diff src tests
	@echo "Run 'make black-fix' to apply black formatting"

black-fix:
	pipenv run black src tests
	@echo "Black formatting applied to src and tests directories"

## Linting and import sorting with Ruff
ruff:
	pipenv run ruff check src tests
	@echo "Run 'make ruff-fix' to apply automatic fixes"

ruff-check:
	pipenv run ruff check --diff src tests
	@echo "Ruff check completed (diff mode)"

ruff-fix:
	pipenv run ruff check --fix src tests
	@echo "Ruff fixes applied (imports sorting + unused cleanup)"

## Fix all common formatting issues automatically
fix-all: black-fix ruff-fix trim-whitespace
	@echo "All automatic fixes applied"

## Remove trailing whitespace and fix blank lines (cross-platform)
trim-whitespace:
	@echo "Removing trailing whitespace..."
ifeq ($(shell uname),Darwin)
	find src tests -name "*.py" -exec sed -i '' 's/[[:space:]]*$$//' {} \;
else
	find src tests -name "*.py" -exec sed -i 's/[[:space:]]*$$//' {} \;
endif
	@echo "Whitespace trimming completed"

## Fix f-strings without placeholders
fix-fstrings:
	@echo "Checking for f-strings without placeholders..."
	@grep -r "f['\"].*['\"]" src tests --include="*.py" || echo "No obvious f-string issues found"
	@echo "Manual review recommended for f-strings"

## Combined linting check (non-destructive)
lint-check: black ruff-check flake
	@echo "All linting checks completed"

## Confluence Tests
test-confluence-simple:
	@echo "Running simple Confluence test..."
	cd tests/integration/confluence && python test_confluence_simple.py

test-vodcastv-ravenne-integration:
	@echo "Running VODCASTV/ravenne integration test..."
	cd tests/integration/confluence && python test_vodcastv_ravenne_integration.py

test-confluence-quick:
	@echo "Running quick Confluence test..."
	cd tests/integration/confluence && python quick_test_vodcastv.py

test-confluence-functional:
	@echo "Running functional Confluence loader test..."
	cd tests/integration/confluence && python test_confluence_loader_functional.py

test-confluence-workflow:
	@echo "Running complete Confluence workflow test..."
	cd tests/integration/confluence && python test_complete_workflow_vodcastv.py

test-confluence-full:
	@echo "Running full Confluence test suite..."
	cd tests/integration/confluence && python test_vodcastv_ravenne_workflow.py

test-bearer-auth:
	@echo "Testing Bearer authentication with PAT token..."
	cd tests/integration/confluence && python test_bearer_auth.py

test-intermediate-bearer:
	@echo "Testing intermediate Bearer authentication workflow..."
	cd tests/integration/confluence && python test_intermediate_bearer.py

test-direct-bearer:
	@echo "Testing direct Bearer authentication with requests..."
	cd tests/integration/confluence && python test_direct_bearer.py

## Test targets for debugging
test-ultra-minimal:
	@echo "Testing ultra minimal workflow without ConfluenceLoader..."
	cd tests/integration/confluence && python test_ultra_minimal.py

# Summary of all confluence tests
test-confluence-all: test-bearer-auth test-direct-bearer test-confluence-simple test-vodcastv-ravenne-integration
	@echo "All Confluence tests completed"

# GCS Mock Testing Workflow:
# 1. make setup-mock-data  - Check mock configuration (no files created)
# 2. make start-mock       - Start server with GCS mocking enabled
# 3. make test-mock-endpoint - Test all source types with mock data
#
# Note: Mock data is generated in-memory, no temporary files needed.
# Reference examples are committed in tests/fixtures/mock-gcs-data/

# Test mock GCS configuration (no file creation needed - data generated in-memory)
# Reference examples are in tests/fixtures/mock-gcs-data/
setup-mock-data:
	@echo "🔧 Testing mock GCS configuration..."
	@echo "Mock data is generated automatically when USE_MOCKS=true is set."
	@echo "No files need to be created - everything works in-memory."
	@echo ""
	@echo "📋 Available mock buckets when USE_MOCKS=true:"
	@echo "   - gs://mock-bucket-ebotman/test-data/getlist.json (Confluence)"
	@echo "   - gs://mock-bucket-mktsearch/test-data/getlist.json (SharePoint)"
	@echo ""
	@echo "✅ Ready to test! Use 'make start-mock' then 'make test-mock-endpoint'"

# Test the loader endpoint with mocked GCS data
test-mock-endpoint:
	@echo "🧪 Running improved mock endpoint tests..."
	@./scripts/test_mock_endpoints.sh