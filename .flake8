[flake8]
# Set the same line length as Black
max-line-length = 120
max-complexity = 10
per-file-ignores =
  */__init__.py: F401

# CRITICAL: Ignore rules that are incompatible with Black.
# E203: whitespace before ':' -> Black handles this.
# W503: line break before binary operator -> Black prefers this, flake8 disagrees.
# E501: line too long -> Black handles this, so checking it here is redundant.
# ignore = E203, W503, E501

# Exclude directories that should not be checked
exclude =
    .git,
    __pycache__,
    .venv,
    venv,
    build,
    dist,
    .tox,
    .eggs,
    *.egg-info
