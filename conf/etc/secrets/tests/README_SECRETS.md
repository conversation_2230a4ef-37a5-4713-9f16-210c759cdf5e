# 🔐 Système de Secrets pour les Tests

Ce répertoire contient les secrets de test pour simuler Google Cloud Secret Manager en local.

## 🎯 Objectif

- **Tests locaux** : <PERSON><PERSON><PERSON> les appels réels à Google Cloud Secret Manager
- **Développement** : Avoir des credentials de test disponibles
- **CI/CD** : Exécuter les tests sans accès GCP

## 📁 Structure

```
conf/etc/secrets/tests/
├── setup_secrets.sh                          # 🔧 Script de configuration automatique
├── basic-client-id/
│   ├── secret.example                         # ✅ Template (committé)
│   └── secret                                 # ❌ Vrai secret (gitignore)
├── basic-client-secret/
│   ├── secret.example                         # ✅ Template (committé)
│   └── secret                                 # ❌ Vrai secret (gitignore)
├── confluence-credentials/
│   ├── secret.example                         # ✅ Template (committé)
│   └── secret                                 # ❌ Vrai secret (gitignore)
└── {perimeter}-confluence-credentials/
    ├── secret.example                         # ✅ Template (committé)
    └── secret                                 # ❌ Vrai secret (gitignore)
```

## 🔧 Comment ça fonctionne

Le `ConfigWithSecret` utilise une **architecture hybride local/cloud** :

### 🏗️ **Architecture de fallback**

```python
@lru_cache
def retrieve_secret(self, secret_id):
    try:
        secret_value = self.retrieve_secret_from_file(secret_id)  # 1️⃣ Fichier local
    except OSError:
        secret_value = None
    if secret_value is None:
        secret_value = self.retrieve_secret_directly(secret_id)  # 2️⃣ Google Secret Manager
    return secret_value
```

### 🌍 **Comportement par environnement**

| Environnement | Fichiers locaux | Google Secret Manager | Priorité |
|---------------|-----------------|----------------------|----------|
| **`env = "tests"`** | ✅ Activé | ❌ Désactivé (`client = None`) | Fichiers uniquement |
| **`env != "tests"`** | ✅ Fallback | ✅ Principal | GCP puis fichiers |

### 🔄 **Ordre de priorité détaillé**

1. **D'abord** : Cherche dans `{path_to_secret_config}/{secret_id}/secret`
   ```bash
   conf/etc/secrets/tests/confluence-credentials/secret
   ```

2. **Ensuite** : Si fichier absent ET `env != "tests"`, utilise GCP :
   ```bash
   projects/{gcp_project_id}/secrets/{secret_id}/versions/latest
   ```

3. **Enfin** : Variables d'environnement (fallback legacy)
   ```bash
   CONFLUENCE_PAT_TOKEN, CONFLUENCE_USERNAME, CONFLUENCE_API_TOKEN
   ```

## 📝 Configuration Confluence

### Format recommandé (JSON)

```json
{
  "pat_token": "votre_personal_access_token"
}
```

### Clés de secrets supportées

| Clé | Description | Priorité |
|-----|-------------|----------|
| `{perimeter_code}-confluence-credentials` | Credentials spécifiques au périmètre | 1 (haute) |
| `confluence-credentials` | Credentials globaux | 2 (moyenne) |
| `{perimeter_code}-confluence-pat-token` | PAT token legacy | 3 (basse) |

### Exemples de clés

- `test-perimeter-confluence-credentials`
- `main-confluence-credentials`
- `confluence-credentials`

## 🚀 Configuration pour vos tests

### 🎯 **Méthode recommandée (automatique)**

```bash
# 1. Lancer le script de setup amélioré
./conf/etc/secrets/tests/setup_secrets.sh

# 2. Éditer les fichiers créés avec vos vraies valeurs
nano conf/etc/secrets/tests/confluence-credentials/secret

# 3. Vérifier la configuration
./conf/etc/secrets/tests/setup_secrets.sh --verify

# 4. Tester la configuration
python -c "from src.kbotloadscheduler.secret.secret_manager import ConfigWithSecret; print('✅ OK')"
```

### 🔧 **Options du script setup_secrets.sh**

```bash
./setup_secrets.sh           # Configuration normale
./setup_secrets.sh --verify  # Vérifier les secrets existants  
./setup_secrets.sh --list    # Lister les secrets
./setup_secrets.sh --help    # Aide complète
```

### 🔧 **Méthode manuelle**

#### 1. Confluence (global)
```bash
# Copier le template
cp conf/etc/secrets/tests/confluence-credentials/secret.example \
   conf/etc/secrets/tests/confluence-credentials/secret

# Éditer avec vos vraies valeurs
nano conf/etc/secrets/tests/confluence-credentials/secret
```

#### 2. Pour un périmètre spécifique
```bash
# Copier le template
cp conf/etc/secrets/tests/test-perimeter-confluence-credentials/secret.example \
   conf/etc/secrets/tests/test-perimeter-confluence-credentials/secret

# Éditer avec vos vraies valeurs
nano conf/etc/secrets/tests/test-perimeter-confluence-credentials/secret
```

## 🔒 Sécurité et Bonnes Pratiques

### ⚠️ **RÈGLES CRITIQUES**

| ✅ **À FAIRE** | ❌ **À NE JAMAIS FAIRE** |
|----------------|--------------------------|
| Utiliser `secret.example` avec des placeholders | Committer de vrais secrets dans `secret` |
| Copier `secret.example` → `secret` et éditer | Partager des tokens par email/chat |
| Utiliser des tokens de test avec permissions limitées | Utiliser des tokens de production en test |
| Vérifier que `secret` est dans `.gitignore` | Committer des fichiers contenant des credentials |

### 🛡️ **Protection automatique**

```bash
# Dans .gitignore - Ces patterns protègent vos secrets
conf/etc/secrets/tests/*/secret     # Tous les vrais secrets
conf/etc/secrets/prod/*/secret      # Secrets de production
```

### 🔧 **Setup rapide et sécurisé**

```bash
# 1. Utiliser le script de setup automatique
./conf/etc/secrets/tests/setup_secrets.sh

# 2. Éditer chaque fichier 'secret' avec vos vraies valeurs
# 3. Jamais committer les fichiers 'secret' !
```

## 🧪 Utilisation dans les tests

### 🔬 **Tests unitaires (avec mocks)**
```python
# Les tests unitaires utilisent des mocks, pas ces secrets
config_with_secret.get_confluence_credentials = MagicMock(return_value={
    "pat_token": "fake_token"
})
```

### 🔗 **Tests d'intégration (avec vrais secrets)**
```python
# Les tests d'intégration utilisent ces secrets automatiquement
# si env = 'tests' dans ConfigWithSecret
```

### 🏭 **En production**
```python
# env != 'tests' → Google Secret Manager activé
# Fallback sur fichiers locaux si secret GCP absent
```

## 📋 Checklist de configuration

- [ ] ✅ Vérifier que `.gitignore` protège les fichiers `secret`
- [ ] 📝 Copier `secret.example` → `secret` pour chaque service nécessaire
- [ ] 🔑 Éditer les fichiers `secret` avec vos vrais tokens
- [ ] ✋ **NE JAMAIS** committer les fichiers `secret`
- [ ] 🧪 Tester avec le script d'extraction
- [ ] 🔒 Utiliser des tokens de test avec permissions limitées
- [ ] 🏗️ Créer un espace Confluence dédié aux tests

## 🔄 Migration vers GCP Secret Manager

En production, ces secrets doivent être dans Google Cloud Secret Manager :

```bash
# Créer le secret dans GCP
gcloud secrets create confluence-credentials --data-file=secret.json

# Format du secret.json
{
  "pat_token": "votre_token_production"
}
```
