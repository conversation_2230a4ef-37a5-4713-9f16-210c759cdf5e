#!/bin/bash
# 🔐 Script de configuration des secrets pour les tests locaux
# 
# Ce script aide à configurer les secrets de test sans risquer de les committer
# Usage: ./setup_secrets.sh [--help|--verify|--list]

set -e

SECRETS_DIR="conf/etc/secrets/tests"
BOLD='\033[1m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to show help
show_help() {
    echo -e "${BOLD}🔐 Script de configuration des secrets de test${NC}"
    echo "=============================================="
    echo
    echo -e "${BOLD}Usage:${NC}"
    echo "  ./setup_secrets.sh           # Configuration normale"
    echo "  ./setup_secrets.sh --verify  # Vérifier les secrets existants"
    echo "  ./setup_secrets.sh --list    # Lister les secrets"
    echo "  ./setup_secrets.sh --help    # Afficher cette aide"
    echo
    echo -e "${BOLD}Description:${NC}"
    echo "Configure les secrets de test en copiant les templates (.example → secret)"
    echo "et permet ensuite d'éditer les vraies valeurs."
    echo
    echo -e "${BOLD}Exemples:${NC}"
    echo "  ./setup_secrets.sh                    # Setup initial"
    echo "  ./setup_secrets.sh --verify           # Validation"
    echo "  nano confluence-credentials/secret     # Éditer"
}

# Function to verify secrets
verify_secrets() {
    echo -e "${BOLD}🔍 Vérification des secrets...${NC}"
    echo "=============================="
    
    local all_good=true
    local secrets=("confluence-credentials" "test-perimeter-confluence-credentials" "basic-client-id" "basic-client-secret")
    
    for secret_name in "${secrets[@]}"; do
        local secret_file="${SECRETS_DIR}/${secret_name}/secret"
        
        if [ -f "$secret_file" ]; then
            # Check if it's not just the example content
            if grep -q "YOUR_.*_HERE\|example\|template" "$secret_file" 2>/dev/null; then
                echo -e "  ${YELLOW}⚠️  ${secret_name}: Contient encore des valeurs d'exemple${NC}"
                all_good=false
            else
                echo -e "  ${GREEN}✅ ${secret_name}: OK${NC}"
            fi
        else
            echo -e "  ${RED}❌ ${secret_name}: Manquant${NC}"
            all_good=false
        fi
    done
    
    if [ "$all_good" = true ]; then
        echo -e "\n${GREEN}✅ Tous les secrets semblent correctement configurés${NC}"
    else
        echo -e "\n${YELLOW}⚠️  Certains secrets nécessitent une configuration${NC}"
    fi
    
    return 0
}

# Function to list secrets
list_secrets() {
    echo -e "${BOLD}📋 Secrets existants:${NC}"
    echo "===================="
    
    find "${SECRETS_DIR}" -name "secret" -type f | while read -r secret_file; do
        local dir_name=$(basename "$(dirname "$secret_file")")
        local size=$(stat -f%z "$secret_file" 2>/dev/null || stat -c%s "$secret_file" 2>/dev/null || echo "?")
        echo -e "  ${GREEN}✅${NC} ${dir_name} (${size} bytes)"
    done
    
    echo
    find "${SECRETS_DIR}" -name "secret.example" -type f | while read -r example_file; do
        local dir_name=$(basename "$(dirname "$example_file")")
        local secret_file="${example_file%%.example}"
        if [ ! -f "$secret_file" ]; then
            echo -e "  ${YELLOW}⚠️${NC}  ${dir_name} (template disponible, secret manquant)"
        fi
    done
}

# Parse arguments
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    --verify|-v)
        verify_secrets
        exit 0
        ;;
    --list|-l)
        list_secrets
        exit 0
        ;;
    "")
        # Continue with normal setup
        ;;
    *)
        echo -e "${RED}❌ Option inconnue: $1${NC}"
        echo -e "Utilisez ${BOLD}--help${NC} pour voir les options disponibles"
        exit 1
        ;;
esac

echo -e "${BOLD}🔐 Configuration des secrets de test${NC}"
echo "=========================================="

# Function to setup a secret from example
setup_secret() {
    local secret_path=$1
    local secret_name=$2
    
    if [ -f "${secret_path}/secret" ]; then
        echo -e "${YELLOW}⚠️  ${secret_name}: secret existe déjà${NC}"
        read -p "Voulez-vous le remplacer ? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            return
        fi
    fi
    
    if [ -f "${secret_path}/secret.example" ]; then
        echo -e "${GREEN}📝 Configuration de ${secret_name}...${NC}"
        cp "${secret_path}/secret.example" "${secret_path}/secret"
        echo -e "   Fichier créé: ${secret_path}/secret"
        echo -e "   ${YELLOW}⚠️  IMPORTANT: Éditez ce fichier avec vos vraies valeurs!${NC}"
    else
        echo -e "${RED}❌ Exemple manquant: ${secret_path}/secret.example${NC}"
    fi
}

# Setup all secrets
echo -e "\n${BOLD}Configuration des secrets...${NC}"

setup_secret "${SECRETS_DIR}/confluence-credentials" "Confluence (global)"
setup_secret "${SECRETS_DIR}/test-perimeter-confluence-credentials" "Confluence (test-perimeter)"
setup_secret "${SECRETS_DIR}/basic-client-id" "Basic Client ID"
setup_secret "${SECRETS_DIR}/basic-client-secret" "Basic Client Secret"

# Add SharePoint if directories exist
if [ -d "${SECRETS_DIR}/sharepoint-client-config" ]; then
    setup_secret "${SECRETS_DIR}/sharepoint-client-config" "SharePoint Client Config"
fi
if [ -d "${SECRETS_DIR}/sharepoint-client-private-key" ]; then
    setup_secret "${SECRETS_DIR}/sharepoint-client-private-key" "SharePoint Private Key"
fi

echo -e "\n${BOLD}✅ Configuration terminée!${NC}"
echo -e "\n${YELLOW}📋 Prochaines étapes:${NC}"
echo "1. Éditez les fichiers 'secret' avec vos vraies valeurs:"
echo -e "   ${BLUE}nano ${SECRETS_DIR}/confluence-credentials/secret${NC}"
echo -e "   ${BLUE}nano ${SECRETS_DIR}/test-perimeter-confluence-credentials/secret${NC}"
echo
echo "2. Vérifiez la configuration:"
echo -e "   ${BLUE}./setup_secrets.sh --verify${NC}"
echo
echo "3. Testez avec:"
echo -e "   ${BLUE}python -c \"from src.kbotloadscheduler.secret.secret_manager import *; print('✅ OK')\"${NC}"
echo
echo -e "4. ${RED}⚠️  NE COMMITEZ JAMAIS les fichiers 'secret' (ils sont dans .gitignore)${NC}"

echo -e "\n${BOLD}📁 Fichiers créés:${NC}"
find "${SECRETS_DIR}" -name "secret" -type f | sort

echo -e "\n${BOLD}💡 Aide:${NC}"
echo -e "  ${BLUE}./setup_secrets.sh --help${NC}     # Afficher l'aide complète"
echo -e "  ${BLUE}./setup_secrets.sh --list${NC}     # Lister tous les secrets"
echo -e "  ${BLUE}./setup_secrets.sh --verify${NC}   # Vérifier la configuration"
