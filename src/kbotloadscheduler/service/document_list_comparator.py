from ..bean.beans import DocumentBean


class DocumentListComparator:
    """Classe de comparaison de deux listes de documents"""

    def __init__(self):
        self._documents_to_get: list[DocumentBean] = []
        self._documents_to_remove: list[DocumentBean] = []

    def compare(
        self, embedded_documents: list[DocumentBean], repo_documents: list[DocumentBean], force_embedding: bool
    ):
        """Comparaison de deux listes de documents

        :param embedded_documents: la liste des documents présents dans la base de vecteur
        :param repo_documents: la liste des documents dans le repo
        :param force_embedding: doit-on forcer l'embedding
        :return: void
        """
        embedded_documents_dict = dict([(document.id, document) for document in embedded_documents])
        repo_documents_dict = dict([(document.id, document) for document in repo_documents])

        embedded_keys = set(embedded_documents_dict.keys())
        repo_keys = set(repo_documents_dict.keys())
        all_keys = set([*embedded_keys, *repo_keys])
        for key in all_keys:
            if key not in repo_keys:
                self._documents_to_remove.append(embedded_documents_dict.get(key))
            elif force_embedding is True or key not in embedded_keys:
                self._documents_to_get.append(repo_documents_dict.get(key))
            else:
                embedded_document = embedded_documents_dict.get(key)
                repo_document = repo_documents_dict.get(key)
                if repo_document.modification_time > embedded_document.modification_time:
                    self._documents_to_get.append(repo_documents_dict.get(key))

    def get_documents_to_get(self) -> list[DocumentBean]:
        """Récupération des documents du repo qui ne sont pas en base vecteur

        :return: documents à récupérer sur le repo
        """
        return self._documents_to_get

    def get_documents_to_remove(self) -> list[DocumentBean]:
        """Récupération des documents de la base vecteur qui ne sont plus dans le repo

        :return: documents à supprimer de la base vecteur
        """
        return self._documents_to_remove
