from datetime import datetime, timezone
from typing import List

from ..apicall.kbot_back_api import KbotBackApi
from ..bean.beans import LOAD_DATE_FORMAT, SourceBean
from ..gcs.treatment_file_manager import TreatmentFileManager


class SourcesService:
    """Service permettant de lister les sources pur lesquelles récupérer les documents

    Ce service va appeler l'api kbot-back pour récupérer les sources
    et écrit ensuite à l'aide du TreatmentFileManager un fichier 'get_list' par source à récupérer
    """

    def __init__(self, kbot_back_api: KbotBackApi, treatment_file_manager: TreatmentFileManager) -> None:
        """Constructeur

        :param kbot_back_api: classe d'appel de l'api kbot_back
        :param treatment_file_manager: classe permettant d'écrire le fichier get_list
        """
        self._kbot_back_api: KbotBackApi = kbot_back_api
        self._treatment_file_manager: TreatmentFileManager = treatment_file_manager

    def load_all_sources(self) -> List[SourceBean]:
        """Liste les sources pour tous les périmètres

        :return: liste des sources à récupérer
        """
        load_date = self.get_load_date()
        perimeters = self._kbot_back_api.get_perimeters()
        sources = []
        for perimeter in perimeters:
            perimeter_sources = self.load_sources(perimeter.get("code"), load_date)
            sources.extend(perimeter_sources)
        return sources

    def load_sources(self, perimeter_code: str, load_date: str = "") -> List[SourceBean]:
        """Liste les sources pour un périmetre

        :param perimeter_code: le périmètre pour lequel lister les sources à récupérer
        :param load_date: date référence pour le chargement de la source
        :return: liste des sources à récupérer
        """
        if load_date == "":
            load_date = self.get_load_date()
        sources = self._kbot_back_api.get_sources(perimeter_code)
        for source in sources:
            self._treatment_file_manager.write_get_list(load_date, source)
            self._kbot_back_api.set_source_done(source.id)
        return sources

    @staticmethod
    def get_load_date() -> str:
        utc_now = datetime.now(timezone.utc)
        return utc_now.strftime(LOAD_DATE_FORMAT)
