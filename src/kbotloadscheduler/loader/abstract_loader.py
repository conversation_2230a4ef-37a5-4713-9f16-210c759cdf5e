from abc import ABC, abstractmethod

from ..bean.beans import DocumentBean, SourceBean


class AbstractLoader(ABC):
    """Loader abstrait dont doivent hériter tous les loaders"""

    def __init__(self, loader_type: str):
        self._loader_type = loader_type

    def check_type(self, source_type: str):
        return self._loader_type == source_type

    @abstractmethod
    def get_document_list(self, source: SourceBean) -> list[DocumentBean]:
        """Récupération de la liste des documents correspondant à une source
        \f
        :param source: définition de la source
        :type source: SourceBean
        :return: la liste des documents
        :rtype: List[DocumentBean]
        """
        pass

    @abstractmethod
    def get_document(self, source: SourceBean, document: DocumentBean, output_path: str):
        """Récupération d'un document d'une source dans un répertoire de sortie
        \f
        :param source: définition de la source
        :type source: SourceBean
        :param document: informations sur le document à récupérer
        :type document: DocumentBean
        :param output_path: répertoire sous lequel exporter le document
        :type output_path: str
        """
        pass


class LoaderException(Exception):
    pass
