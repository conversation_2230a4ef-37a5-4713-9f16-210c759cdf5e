import requests
from functools import wraps
import time
from http import HTTPStatus


class EmptyJSONResponse(requests.Response):
    def __init__(self, *args, status_code=0, error="", **kwargs):
        super().__init__(*args, **kwargs)
        self.status_code = status_code
        self.error = error

    def json(self):
        return None


def failed(func):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        # Extract 'url' from positional arguments
        url = args[0] if args else kwargs.get("url", "Unknown URL")

        # Call the function that makes the request
        resp = func(self, *args, **kwargs)

        # Check if the response is not OK
        if resp.status_code != HTTPStatus.OK:
            # Append to self.failed with the URL, status code, and error message if available
            self.failed.append(
                {
                    "url": url,
                    "status_code": resp.status_code,
                    "error": resp.error,
                }
            )
            print(f"Logged failure for URL: {url} with status code: {resp.status_code}")

        return resp

    return wrapper


def call_request_with_token(self, func, args, kwargs):
    resp = func(self, *args, **kwargs)
    if resp.status_code == HTTPStatus.UNAUTHORIZED:
        print("Unauthorized. Refreshing token...")
        self.headers = self.get_new_token_headers()
        resp = func(self, *args, **kwargs)
    return resp


def retry(max_retries=3, backoff_factor=2):
    def decorator_retry(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            retries = 0
            while retries <= max_retries:
                try:
                    # Call the function that makes the request
                    resp = call_request_with_token(self, func, args, kwargs)

                    # Check for the status code
                    if resp.status_code == HTTPStatus.OK:
                        return resp
                    # If status is still not OK or other errors
                    raise ValueError(
                        f"Wrong status ({resp.status_code}) - {resp.text} - URL: {resp.url}"
                    )

                except (requests.exceptions.RequestException, ValueError) as e:
                    cur_status = 0  # Indicates an issue before HTTP response
                    if hasattr(e, "response") and e.response is not None:
                        cur_status = e.response.status_code

                    if retries < max_retries:
                        wait_time = backoff_factor ** retries
                        print(
                            f"Request failed for {func.__name__}: {e}. Retrying in {wait_time} seconds..."
                        )
                        time.sleep(wait_time)
                        retries += 1
                    else:
                        print(f"Max retries reached. Last error: {e}")
                        # Return a response object with the last known status code or 0
                        return EmptyJSONResponse(
                            status_code=cur_status, error=str(e.response.status_code)
                        )

        return wrapper

    return decorator_retry
