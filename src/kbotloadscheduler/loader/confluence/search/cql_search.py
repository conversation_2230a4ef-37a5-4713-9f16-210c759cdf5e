"""
Main search strategy based on Confluence Query Language (CQL),
including query construction, resilient execution, fallback strategies,
and result filtering.
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List

from ..config.confluence_config import ConfluenceConfig
from ..utils.circuit_breaker import CircuitBreaker
from ..utils.retry import retry_operation

class CqlSearch:
    """Executes CQL searches to find Confluence content."""

    def __init__(self, client, circuit_breaker: CircuitBreaker):
        """
        Initialize the CQL searcher.

        Args:
            client: The Confluence client to execute queries.
            circuit_breaker: An instance of the circuit breaker for resilience.
        """
        self.client = client
        self.circuit_breaker = circuit_breaker

    def search_pages_in_space(self, space_key: str, config: ConfluenceConfig) -> List[Dict]:
        """
        Main entry point to search for pages in a space.
        Builds a query, executes it, and attempts fallback strategies if necessary.

        Args:
            space_key: The Confluence space key to query.
            config: The source configuration to guide the search.

        Returns:
            A list of dictionaries, each representing a found page.
        """
        primary_cql = self._build_cql_query(space_key, config)
        results = self._execute_search(primary_cql, config)
        logging.info(f"Recherche principale pour l'espace '{space_key}' a retourné {len(results)} pages.")

        if not results and not config.filtering.custom_cql:
            logging.warning(f"La recherche principale pour '{space_key}' n'a retourné aucun résultat. Tentative de stratégies de repli.")
            results = self._try_fallback_searches(space_key, config)

        return results

    # In cql_search.py


    def search_all_pages_in_space(self, space_key: str, config: ConfluenceConfig) -> List[Dict]:
        """
        Search all pages in a space at once, without filters,
        ensuring ancestors are included for hierarchy construction.
        """
        # Build the CQL query for all pages in the space
        cql = f'space = "{space_key}" AND type = page'
        logging.info(f"CQL for all pages (hierarchy) in space '{space_key}': {cql}")

        # --- KEY MODIFICATION ---
        # We will force the call to the underlying method with the correct parameters,
        # to ensure nothing is lost along the way.

        all_results = []
        start = 0
        page_size = 100
        # The CRUCIAL expand for hierarchy
        expand_for_hierarchy = "version,space,ancestors"

        while True:
            try:
                # Direct call to the library method for more control
                raw_results = self.client.search_content(
                    cql,
                    limit=page_size,
                    start=start,
                    expand=expand_for_hierarchy,
                )
            except Exception as e:
                logging.error(f"Hierarchy search failed (start={start}): {e}")
                raise

            if not raw_results:
                break

            all_results.extend(raw_results)

            # Confluence API may return fewer than the limit even if there are more pages.
            # The safest condition is to stop if we retrieved fewer than the requested page size.
            if len(raw_results) < page_size:
                break

            start += len(raw_results)

        logging.info(f"All pages search for hierarchy in space '{space_key}' returned {len(all_results)} pages.")

        if not all_results:
            logging.warning(f"No pages found in space '{space_key}' for hierarchy construction.")

        # Normalize results if necessary (even if _execute_search already did it)
        page_results = self._filter_and_normalize_pages(all_results)

        return page_results

    def search_child_pages(self, parent_page_id: str, config: ConfluenceConfig) -> List[Dict]:
        """
        Search for direct child pages of a parent page.

        Args:
            parent_page_id: The parent page ID.
            config: The source configuration.

        Returns:
            A list of dictionaries representing the child pages.
        """
        try:
            child_cql = f"parent = {parent_page_id} AND type = page"
            if config.filtering.last_modified_days:
                cutoff_date = datetime.now() - timedelta(days=config.filtering.last_modified_days)
                child_cql += f" AND lastModified >= '{cutoff_date.strftime('%Y-%m-%d')}'"

            # For children, we do not always expect a result, so we ignore warnings if empty.
            return self._execute_search(child_cql, config, ignore_empty_warning=True)
        except Exception as e:
            logging.error(f"Failed to search for child pages for parent {parent_page_id}: {e}")
            return []

    def _execute_search(self, cql: str, config: ConfluenceConfig, ignore_empty_warning: bool = False) -> List[Dict]:
        """Private method to execute a CQL query resiliently and filter results with pagination."""
        if logging.getLogger().isEnabledFor(logging.DEBUG):
            logging.debug("Exécution de la requête CQL : %s", cql)
        expand_params = "version,space,_links.webui,body.storage,metadata.labels,ancestors"
        search_method = getattr(self.client, 'search_content_with_retry', self.client.search_content)

        all_results = []
        start = 0
        page_size = 100  # Confluence API default/recommended page size
        max_results = config.basic.max_results or float('inf')

        while start < max_results:
            current_limit = min(page_size, max_results - start) if max_results != float('inf') else page_size

            try:
                raw_results = self.circuit_breaker.call(
                    retry_operation,
                    search_method,
                    cql,
                    limit=current_limit,
                    start=start,
                    expand=expand_params,
                    max_attempts=config.performance.retry_attempts,
                    delay_seconds=config.performance.retry_delay_seconds,
                )
            except Exception as e:
                logging.error(f"An exception was raised during CQL search execution '{cql}' (start={start}): {e}")
                raise

            if not raw_results:
                # No more results to fetch
                break

            all_results.extend(raw_results)
            if logging.getLogger().isEnabledFor(logging.DEBUG):
                logging.debug(
                    "Page retrieved: %d results (start=%d, total=%d)",
                    len(raw_results), start, len(all_results)
                )

            # If we got fewer results than requested, we've reached the end
            if len(raw_results) < current_limit:
                break

            start += len(raw_results)

        if not all_results and not ignore_empty_warning:
            logging.warning(f"The CQL query '{cql}' returned no results.")
            return []

        page_results = self._filter_and_normalize_pages(all_results)
        logging.info(f"Pagination complete: {len(page_results)} pages kept from {len(all_results)} raw results (retrieved in {(start // page_size) + 1} page(s)).")
        return page_results

    def _build_cql_query(self, space_key: str, config: ConfluenceConfig) -> str:
        """Builds the CQL query string from configuration parameters."""
        if config.filtering.custom_cql:
            logging.info(f"Using custom CQL for space '{space_key}': {config.filtering.custom_cql}")
            return config.filtering.custom_cql

        cql_parts = [f'space = "{space_key}"']

        if config.filtering.last_modified_days is not None:
            cutoff_date = datetime.now() - timedelta(days=config.filtering.last_modified_days)
            cql_parts.append(f"lastModified >= '{cutoff_date.strftime('%Y-%m-%d')}'")

        if config.filtering.labels:
            labels_cql = ", ".join(f"'{label.strip()}'" for label in config.filtering.labels)
            cql_parts.append(f"label in ({labels_cql})")

        if config.filtering.exclude_labels:
            exclude_labels_cql = ", ".join(f"'{label.strip()}'" for label in config.filtering.exclude_labels)
            cql_parts.append(f"label not in ({exclude_labels_cql})")

        cql_parts.append("type = page")
        final_cql = " AND ".join(cql_parts)
        logging.info(f"Auto-generated CQL for space '{space_key}': {final_cql}")
        return final_cql

    def _try_fallback_searches(self, space_key: str, config: ConfluenceConfig) -> List[Dict]:
        """Attempts simpler alternative queries if the main query is empty."""
        logging.info("Fallback strategy 1: Try with a simple CQL (type=page).")
        simple_cql = f'space = "{space_key}" AND type = page'
        results = self._execute_search(simple_cql, config, ignore_empty_warning=True)
        if results:
            logging.info(f"Fallback strategy 1 found {len(results)} pages.")
            return results

        logging.info("Fallback strategy 2: Try with legacy method 'get_space_content'.")
        try:
            # Note: get_space_content may also need pagination, but that depends on the client implementation
            # For now, we'll use it as-is since it's a legacy fallback
            legacy_results = self.client.get_space_content(space_key, expand="version,space,_links.webui")
            if legacy_results:
                filtered = self._filter_and_normalize_pages(legacy_results)
                logging.info(f"Fallback strategy 2 found {len(filtered)} pages.")
                return filtered
        except Exception as e:
            logging.warning(f"Legacy search method failed: {e}")

        return []

    def _filter_and_normalize_pages(self, results: List[Dict]) -> List[Dict]:
        """Filters raw results to keep only pages and normalizes their structure."""
        page_results = []
        for result in results:
            if not isinstance(result, dict):
                continue
            if result.get("type") == "page":
                page_results.append(result)
            elif "content" in result and isinstance(result.get("content"), dict) and result["content"].get("type") == "page":
                page_results.append(self._flatten_search_result(result))
        return page_results

    def _flatten_search_result(self, result: Dict) -> Dict:
        """Flattens a complex search result into a simple page structure."""
        content = result["content"]
        return {
            "id": content.get("id"),
            "type": "page",
            "title": result.get("title") or content.get("title"),
            "status": content.get("status"),
            "_links": content.get("_links", {}),
            "space": content.get("_expandable", {}).get("space", {}),
            "lastModified": result.get("lastModified"),
            "url": result.get("url"),
            "excerpt": result.get("excerpt"),
            "_original_content": content
        }
