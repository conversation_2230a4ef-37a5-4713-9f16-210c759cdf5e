"""Cache utilities for Confluence loader."""

import threading
from datetime import UTC, datetime
from typing import Any


class CacheEntry:
    """Cache entry with TTL support."""

    def __init__(self, value: Any, ttl_minutes: int = 60):
        self.value = value
        self.created_at = datetime.now(UTC)
        self.ttl_minutes = ttl_minutes

    def is_expired(self) -> bool:
        """Check if cache entry is expired."""
        age = datetime.now(UTC) - self.created_at
        return age.total_seconds() > (self.ttl_minutes * 60)


class SimpleCache:
    """Simple in-memory cache with TTL support."""

    def __init__(self):
        self._cache: dict[str, CacheEntry] = {}
        self._lock = threading.RLock()

    def get(self, key: str) -> Any | None:
        """Get value from cache if not expired."""
        with self._lock:
            entry = self._cache.get(key)
            if entry and not entry.is_expired():
                return entry.value
            elif entry:
                # Remove expired entry
                del self._cache[key]
            return None

    def put(self, key: str, value: Any, ttl_minutes: int = 60):
        """Put value in cache with TTL."""
        with self._lock:
            self._cache[key] = CacheEntry(value, ttl_minutes)

    def clear(self):
        """Clear all cache entries."""
        with self._lock:
            self._cache.clear()

    def size(self) -> int:
        """Get cache size."""
        with self._lock:
            return len(self._cache)
