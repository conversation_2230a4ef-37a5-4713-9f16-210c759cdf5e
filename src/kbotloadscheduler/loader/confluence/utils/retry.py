"""Retry utilities for Confluence loader."""

import logging
import time
from typing import Any, Callable, TypeVar

logger = logging.getLogger(__name__)

T = TypeVar('T')


def retry_operation(
    operation: Callable[..., T],
    *args,
    max_attempts: int = 3,
    delay_seconds: int = 2,
    **kwargs
) -> T:
    """Execute operation with retry logic.

    Args:
        operation: The operation to retry
        *args: Positional arguments for operation
        max_attempts: Maximum number of attempts
        delay_seconds: Base delay between attempts
        **kwargs: Keyword arguments for operation

    Returns:
        Result of the operation

    Raises:
        Exception: The last exception if all attempts fail
    """
    last_exception = None
    for attempt in range(max_attempts):
        try:
            return operation(*args, **kwargs)
        except Exception as e:
            last_exception = e
            if attempt < max_attempts - 1:
                delay = delay_seconds * (attempt + 1)  # Exponential backoff
                logger.warning(f"Operation failed (attempt {attempt + 1}/{max_attempts}): {e}")
                logger.info(f"Retrying in {delay} seconds...")
                time.sleep(delay)
            else:
                logger.error(f"Operation failed after {max_attempts} attempts: {e}")

    if last_exception:
        raise last_exception
    else:
        raise RuntimeError("Operation failed with no exception recorded")
