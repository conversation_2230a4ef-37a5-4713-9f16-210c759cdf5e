"""
Utilities for URL parsing and manipulation in Confluence contexts.
"""
import logging
import re
from typing import Optional, Dict, Any

# Constants
HTTP_PREFIX = "http"
DOWNLOAD_ATTACHMENTS_PATH = "/download/attachments/"
DICT_KEY_SPACE = "space"
DICT_KEY_KEY = "key"
DICT_KEY_TITLE = "title"


def extract_page_id_from_attachment_path(path: str, client=None) -> Optional[str]:
    """
    Extract parent page ID from attachment path.

    Args:
        path: Attachment path which can be:
            - Pipe format: "Space|PageTitle|AttachmentId"
            - Confluence URL: "https://domain.com/download/attachments/PAGE_ID/filename"
        client: Confluence client for API calls (optional)

    Returns:
        Parent page ID or None if not found
    """
    try:
        # Case 1: Confluence download URL
        if path.startswith(HTTP_PREFIX) and DOWNLOAD_ATTACHMENTS_PATH in path:
            # Format: https://domain.com/download/attachments/PAGE_ID/filename
            try:
                # Extract page ID from URL
                match = re.search(r'/download/attachments/(\d+)/', path)
                if match:
                    page_id = match.group(1)
                    logging.info(f"Page ID extracted from URL: {page_id}")
                    return page_id
                else:
                    logging.warning(f"Cannot extract page ID from URL: {path}")
                    return None
            except Exception as e:
                logging.error(f"Error extracting ID from URL: {e}")
                return None

        # Case 2: Traditional pipe format "Space|PageTitle|AttachmentId"
        parts = path.split('|')
        if len(parts) >= 2:
            page_title = parts[1]
            space_key = parts[0]

            # Try different methods to get page ID
            page_id = None

            if client:
                # Method 1: Use get_page_by_title if available
                if hasattr(client, 'get_page_by_title'):
                    try:
                        page_data = client.get_page_by_title(space_key, page_title)
                        if page_data:
                            page_id = page_data.get('id')
                    except Exception as e:
                        logging.warning(f"get_page_by_title method failed: {e}")

                # Method 2: Use get_page_content if available
                if not page_id and hasattr(client, 'get_page_content'):
                    try:
                        # Try with title as ID (sometimes title is already an ID)
                        page_data = client.get_page_content(page_title)
                        if page_data:
                            page_id = page_data.get('id', page_title)
                    except Exception as e:
                        logging.warning(f"get_page_content with title failed: {e}")

                # Method 3: Use page search
                if not page_id and hasattr(client, 'search_pages'):
                    try:
                        search_results = client.search_pages(f'title:"{page_title}" AND space:"{space_key}"')
                        if search_results and search_results.get('results'):
                            page_id = search_results['results'][0].get('id')
                    except Exception as e:
                        logging.warning(f"search_pages method failed: {e}")

            # Method 4: Fallback - use title as ID
            if not page_id:
                logging.warning(f"Cannot retrieve page ID via API, using title as ID: {page_title}")
                page_id = page_title

            return page_id

        logging.warning(f"Unrecognized path format (expected: Space|PageTitle|AttachmentId or URL): {path}")
        return None

    except Exception as e:
        logging.error(f"Error extracting page ID: {e}")
        return None


def extract_parent_page_url(attachment_path: str, page_id: str, client=None) -> Optional[str]:
    """
    Extract parent page URL from attachment path.

    Args:
        attachment_path: Attachment path
        page_id: Parent page ID
        client: Confluence client for API calls (optional)

    Returns:
        Parent page URL or None if not found
    """
    try:
        from ..processors.confluence_url_builder import ConfluenceUrlBuilder

        # Case 1: If attachment_path is already a URL, extract base
        if attachment_path.startswith(HTTP_PREFIX) and DOWNLOAD_ATTACHMENTS_PATH in attachment_path:
            # Format: https://domain.com/download/attachments/PAGE_ID/filename
            # Build page URL: https://domain.com/display/SPACE/PAGE_TITLE
            base_url_match = re.match(r'(https?://[^/]+)', attachment_path)
            if base_url_match:
                base_url = base_url_match.group(1)
                url_builder = ConfluenceUrlBuilder(base_url)
                try:
                    if client and hasattr(client, 'get_page_content'):
                        page_data = client.get_page_content(page_id)
                        if page_data:
                            space_key = page_data.get(DICT_KEY_SPACE, {}).get(DICT_KEY_KEY, '')
                            page_title = page_data.get(DICT_KEY_TITLE, '')
                            if space_key and page_title:
                                return url_builder.build_page_url(space_key, page_title)
                except Exception as e:
                    logging.warning(f"Cannot retrieve page details for {page_id}: {e}")
                # Fallback: use page ID
                return url_builder.build_page_url_by_id(page_id)

        # Case 2: Pipe format "Space|PageTitle|AttachmentId"
        elif '|' in attachment_path:
            parts = attachment_path.split('|')
            if len(parts) >= 2:
                space_key = parts[0]
                page_title = parts[1]
                base_url = get_confluence_base_url(client)
                if base_url:
                    url_builder = ConfluenceUrlBuilder(base_url)
                    return url_builder.build_page_url(space_key, page_title)

        # Fallback: try to build basic URL
        base_url = get_confluence_base_url(client)
        if base_url:
            url_builder = ConfluenceUrlBuilder(base_url)
            return url_builder.build_page_url_by_id(page_id)

        logging.warning(f"Cannot build parent page URL for {attachment_path}")
        return None

    except Exception as e:
        logging.error(f"Error extracting parent page URL: {e}")
        return None


def extract_parent_page_url_from_page_data(page_data: Dict, page_id: str, client=None) -> Optional[str]:
    """
    Extract parent page URL from page data.

    Args:
        page_data: Page data retrieved from API
        page_id: Parent page ID
        client: Confluence client for API calls (optional)

    Returns:
        Parent page URL or None if not found
    """
    try:
        from ..processors.confluence_url_builder import ConfluenceUrlBuilder

        space_key = page_data.get(DICT_KEY_SPACE, {}).get(DICT_KEY_KEY, '')
        page_title = page_data.get(DICT_KEY_TITLE, '')
        base_url = get_confluence_base_url(client)

        if base_url:
            url_builder = ConfluenceUrlBuilder(base_url)
            if space_key and page_title:
                return url_builder.build_page_url(space_key, page_title)
            else:
                return url_builder.build_page_url_by_id(page_id)

        logging.warning(f"Cannot build parent page URL for page {page_id}")
        return None

    except Exception as e:
        logging.error(f"Error extracting parent page URL from data: {e}")
        return None


def get_confluence_base_url(client=None) -> Optional[str]:
    """
    Get Confluence base URL from client or configuration.

    Args:
        client: Confluence client (optional)

    Returns:
        Confluence base URL or None if not found
    """
    if not client:
        return None

    try:
        # Try to get URL from client
        if hasattr(client, 'base_url'):
            return client.base_url
        elif hasattr(client, 'url'):
            return client.url
        elif hasattr(client, '_base_url'):
            return client._base_url

        # If client has config attribute
        if hasattr(client, 'config') and hasattr(client.config, 'base_url'):
            return client.config.base_url

        logging.warning("Cannot retrieve Confluence base URL from client")
        return None

    except Exception as e:
        logging.error(f"Error retrieving base URL: {e}")
        return None
