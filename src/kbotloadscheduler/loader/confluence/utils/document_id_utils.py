"""
Utility module for handling Confluence document ID format and parsing.

This module centralizes the document ID format logic to avoid coupling between
ID creation and parsing components.
"""

from typing import Dict
from kbotloadscheduler.bean.beans import SourceBean


class DocumentIdFormatter:
    """
    Handles formatting and parsing of Confluence document IDs.

    Format: {domain_code}|{source_code}|{confluence_id}
    Examples:
    - Page: "engineering|confluence_docs|123456"
    - Attachment: "engineering|confluence_docs|att789012"
    - Draw.io diagram: "engineering|confluence_docs|2392678238_macro_0"
    """

    SEPARATOR = "|"

    @classmethod
    def create_page_id(cls, source: SourceBean, page_id: str) -> str:
        """
        Create a document ID for a Confluence page.

        Args:
            source: Source bean containing domain and source codes
            page_id: Confluence page ID

        Returns:
            Formatted document ID
        """
        return f"{source.domain_code}{cls.SEPARATOR}{source.code}{cls.SEPARATOR}{page_id}"

    @classmethod
    def create_attachment_id(cls, source: SourceBean, attachment_id: str) -> str:
        """
        Create a document ID for a Confluence attachment.

        Args:
            source: Source bean containing domain and source codes
            attachment_id: Confluence attachment ID

        Returns:
            Formatted document ID with 'att' prefix
        """
        att_id_part = f"att{attachment_id}"
        return f"{source.domain_code}{cls.SEPARATOR}{source.code}{cls.SEPARATOR}{att_id_part}"

    @classmethod
    def create_drawio_id(cls, source: SourceBean, diagram_id: str) -> str:
        """
        Create a document ID for a Draw.io diagram.

        Args:
            source: Source bean containing domain and source codes
            diagram_id: Unique diagram ID (e.g., "page123_macro_0", "page123_att_456")

        Returns:
            Formatted document ID
        """
        return f"{source.domain_code}{cls.SEPARATOR}{source.code}{cls.SEPARATOR}{diagram_id}"

    @classmethod
    def parse_document_id(cls, document_id: str) -> Dict[str, str]:
        """
        Parse a document ID to extract its components.

        Args:
            document_id: The document ID to parse

        Returns:
            Dictionary with 'domain_code', 'source_code', and 'confluence_id' keys

        Raises:
            ValueError: If the document ID format is invalid
        """
        try:
            parts = document_id.split(cls.SEPARATOR)
            if len(parts) != 3:
                raise ValueError(f"Invalid document ID format: {document_id}")
            return {
                "domain_code": parts[0],
                "source_code": parts[1],
                "confluence_id": parts[2]
            }
        except (ValueError, IndexError) as e:
            raise ValueError(f"Invalid or corrupted document ID format: '{document_id}'") from e

    @classmethod
    def is_attachment_id(cls, confluence_id: str) -> bool:
        """
        Check if a Confluence ID corresponds to an attachment.

        Args:
            confluence_id: The Confluence ID part (after parsing)

        Returns:
            True if it's an attachment ID
        """
        return confluence_id.startswith("att")

    @classmethod
    def is_drawio_diagram_id(cls, confluence_id: str) -> bool:
        """
        Check if a Confluence ID corresponds to a Draw.io diagram.

        Args:
            confluence_id: The Confluence ID part (after parsing)

        Returns:
            True if it's a Draw.io diagram ID
        """
        return ("_macro_" in confluence_id or
                "_att_" in confluence_id or
                "_embedded_" in confluence_id)
