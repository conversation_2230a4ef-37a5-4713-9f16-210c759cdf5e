"""
Utilities for file handling, filename sanitization, and temporary file management.
"""
import logging
import os
import re
import tempfile
from typing import Any, Callable


def create_safe_filename(filename: str, item_id: str) -> str:
    """
    Create a safe filename by removing invalid characters.

    Args:
        filename: Original filename
        item_id: ID to use as fallback if filename is empty

    Returns:
        Safe filename for filesystem storage
    """
    # Clean the filename
    safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename).strip()

    # If the name is empty, use the ID as name
    if not safe_name:
        safe_name = f"attachment_{item_id}"

    return safe_name


def create_unique_filename(filename: str, item_id: str, output_path: str, strategy: str) -> str:
    """
    Create a unique filename to avoid conflicts.

    Args:
        filename: Base filename
        item_id: ID to append if using append_id strategy
        output_path: Output directory path
        strategy: Conflict resolution strategy

    Returns:
        Unique filename
    """
    if strategy == "append_id":
        # Add ID to the end of filename
        name, ext = os.path.splitext(filename)
        return f"{name}_{item_id}{ext}"
    else:
        # Default strategy: use filename as-is
        return filename


def clean_filename(filename: str) -> str:
    """
    Clean a filename for safe filesystem storage.

    Args:
        filename: Filename to clean

    Returns:
        Cleaned filename
    """
    return re.sub(r'[<>:"/\\|?*]', '_', filename).strip()


def download_with_temp_file(download_func: Callable, *args, **kwargs) -> str:
    """
    Helper method for temporary file management using a context manager.

    Args:
        download_func: Function to call for downloading
        *args: Arguments to pass to download_func
        **kwargs: Keyword arguments to pass to download_func

    Returns:
        Path to the temporary file

    Raises:
        Exception: If download fails, temporary file is cleaned up
    """
    temp_file = tempfile.NamedTemporaryFile(delete=False)
    temp_path = temp_file.name
    temp_file.close()  # We'll open it as needed in download_func

    try:
        download_func(temp_path, *args, **kwargs)
        return temp_path
    except Exception:
        if os.path.exists(temp_path):
            os.remove(temp_path)
        raise
