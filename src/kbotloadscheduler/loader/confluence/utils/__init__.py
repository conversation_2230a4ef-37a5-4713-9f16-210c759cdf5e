"""Confluence loader utility classes."""

from .cache import <PERSON>ache<PERSON>ntry, SimpleCache
from .circuit_breaker import Circuit<PERSON>reaker
from .metrics import ConfluenceMetrics
from .retry import retry_operation

# File handling utilities
from .file_utils import (
    create_safe_filename,
    create_unique_filename,
    clean_filename,
    download_with_temp_file
)

# URL parsing utilities
from .url_utils import (
    extract_page_id_from_attachment_path,
    extract_parent_page_url,
    extract_parent_page_url_from_page_data,
    get_confluence_base_url
)

# Draw.io utilities
from .drawio_utils import (
    DrawioIdComponents,
    parse_drawio_id,
    generate_basic_drawio_markdown
)

# Metadata building utilities
from .metadata_utils import (
    build_attachment_metadata,
    build_page_metadata,
    build_drawio_metadata
)

__all__ = [
    "CacheEntry",
    "SimpleCache",
    "CircuitBreaker",
    "ConfluenceMetrics",
    "retry_operation",
    # File utilities
    "create_safe_filename",
    "create_unique_filename",
    "clean_filename",
    "download_with_temp_file",
    # URL utilities
    "extract_page_id_from_attachment_path",
    "extract_parent_page_url",
    "extract_parent_page_url_from_page_data",
    "get_confluence_base_url",
    # Draw.io utilities
    "DrawioIdComponents",
    "parse_drawio_id",
    "generate_basic_drawio_markdown",
    # Metadata utilities
    "build_attachment_metadata",
    "build_page_metadata",
    "build_drawio_metadata",
]
