"""
Utilities for handling Draw.io diagrams in Confluence.
"""
import logging
from dataclasses import dataclass
from typing import Dict, Any
from kbotloadscheduler.bean.beans import DocumentBean
from kbotloadscheduler.exceptions.confluence_exceptions import ConfluenceException

# Draw.io types
DRAWIO_TYPE_MACRO = "macro"
DRAWIO_TYPE_ATTACHMENT = "attachment"
DRAWIO_TYPE_EMBEDDED = "embedded"


@dataclass
class DrawioIdComponents:
    """Components of a Draw.io diagram ID."""
    page_id: str
    diagram_type: str
    diagram_index: int


def parse_drawio_id(drawio_id: str) -> DrawioIdComponents:
    """
    Parse a Draw.io diagram ID to extract its components.

    Args:
        drawio_id: Diagram ID (e.g., "2392678238_macro_0")

    Returns:
        DrawioIdComponents: Structured components of the Draw.io ID

    Raises:
        ConfluenceException: If ID format is not recognized
    """
    try:
        if "_macro_" in drawio_id:
            parts = drawio_id.split("_macro_")
            return DrawioIdComponents(parts[0], DRAWIO_TYPE_MACRO, int(parts[1]))
        elif "_att_" in drawio_id:
            parts = drawio_id.split("_att_")
            return DrawioIdComponents(parts[0], DRAWIO_TYPE_ATTACHMENT, int(parts[1]))
        elif "_embedded_" in drawio_id:
            parts = drawio_id.split("_embedded_")
            return DrawioIdComponents(parts[0], DRAWIO_TYPE_EMBEDDED, int(parts[1]))
        else:
            raise ValueError(f"Unrecognized Draw.io ID format: {drawio_id}")
    except (ValueError, IndexError) as e:
        raise ConfluenceException(f"Cannot parse Draw.io ID '{drawio_id}': {e}") from e


def generate_basic_drawio_markdown(diagram, document: DocumentBean, page_data: Dict) -> str:
    """
    Generate basic markdown for a Draw.io diagram without XML metadata.

    Args:
        diagram: Diagram object with type, title, and context
        document: Document bean
        page_data: Page data from API

    Returns:
        Basic markdown content for the diagram
    """
    lines = [
        f"# {document.name}",
        "",
        "## Basic Information",
        f"- **Type** : Draw.io Diagram ({diagram.diagram_type})",
        f"- **Source** : [Confluence Page]({document.path})",
        f"- **Parent Page** : {page_data.get('title', 'Confluence Page')}",
        ""
    ]

    if diagram.title:
        lines.extend([
            "## Diagram Title",
            diagram.title,
            ""
        ])

    if diagram.page_context:
        lines.extend([
            "## Context",
            diagram.page_context,
            ""
        ])

    lines.extend([
        "## Searchable Content",
        f"draw.io diagram {diagram.diagram_type} {diagram.title or ''} {page_data.get('title', '')}"
    ])

    return "\n".join(lines)
