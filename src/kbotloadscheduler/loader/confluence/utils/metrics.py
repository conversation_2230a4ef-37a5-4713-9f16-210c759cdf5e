"""Metrics utilities for Confluence loader."""

import threading
from datetime import UTC, datetime
from typing import Any


class ConfluenceMetrics:
    """Metrics collector for Confluence operations."""

    def __init__(self):
        self.start_time = datetime.now(UTC)
        self.pages_processed = 0
        self.attachments_processed = 0
        self.errors_count = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.download_times: list[float] = []
        self.processing_times: list[float] = []
        self._lock = threading.RLock()

    def record_page_processed(self):
        """Record a page processed."""
        with self._lock:
            self.pages_processed += 1

    def record_attachment_processed(self):
        """Record an attachment processed."""
        with self._lock:
            self.attachments_processed += 1

    def record_error(self):
        """Record an error."""
        with self._lock:
            self.errors_count += 1

    def record_cache_hit(self):
        """Record a cache hit."""
        with self._lock:
            self.cache_hits += 1

    def record_cache_miss(self):
        """Record a cache miss."""
        with self._lock:
            self.cache_misses += 1

    def record_download_time(self, duration: float):
        """Record download time."""
        with self._lock:
            self.download_times.append(duration)

    def record_processing_time(self, duration: float):
        """Record processing time."""
        with self._lock:
            self.processing_times.append(duration)

    def get_summary(self) -> dict[str, Any]:
        """Get metrics summary."""
        with self._lock:
            total_time = (datetime.now(UTC) - self.start_time).total_seconds()

            avg_download_time = sum(self.download_times) / len(self.download_times) if self.download_times else 0
            avg_processing_time = (
                sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0
            )

            cache_hit_rate = 0
            if self.cache_hits + self.cache_misses > 0:
                cache_hit_rate = self.cache_hits / (self.cache_hits + self.cache_misses)

            return {
                "total_execution_time": total_time,
                "pages_processed": self.pages_processed,
                "attachments_processed": self.attachments_processed,
                "total_documents": self.pages_processed + self.attachments_processed,
                "errors_count": self.errors_count,
                "cache_hits": self.cache_hits,
                "cache_misses": self.cache_misses,
                "cache_hit_rate": cache_hit_rate,
                "average_download_time": avg_download_time,
                "average_processing_time": avg_processing_time,
                "documents_per_second": (
                    (self.pages_processed + self.attachments_processed) / total_time if total_time > 0 else 0
                ),
            }
