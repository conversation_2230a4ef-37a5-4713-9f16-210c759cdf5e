"""
Configurable markdown converter interface for content processing.

This module provides an abstraction layer for markdown conversion,
allowing different markdown libraries to be used without tight coupling.
It is also responsible for pre-processing Confluence-specific HTML,
such as macros, before the final conversion.
"""
import logging
import re
from typing import Protocol, Optional, Any

# Assuming ConfluenceConfig is accessible from this path
from ..config.confluence_config import ConfluenceConfig

logger = logging.getLogger(__name__)


class MarkdownConverter(Protocol):
    """Protocol for markdown conversion functionality."""

    def __call__(self, html_content: str, page_id: str, config: ConfluenceConfig) -> str:
        """
        Convert HTML content to markdown, including pre-processing of macros.

        Args:
            html_content: HTML content to convert.
            page_id: The ID of the Confluence page, used to resolve macros.
            config: The Confluence configuration object.

        Returns:
            Converted markdown content.
        """
        ...


class DefaultMarkdownConverter:
    """
    Default markdown converter using a custom markdownify implementation.
    This class is responsible for:
    1. Pre-processing Confluence HTML to handle macros (Draw.io, view-file, etc.).
    2. Converting the processed HTML to clean markdown.
    """

    def __init__(self, client: Any):
        """
        Initialize the default converter.

        Args:
            client: The Confluence API client, required for resolving macros.
        """
        self.client = client
        self.base_url = getattr(self.client, 'url', '')
        try:
            from kbotloadscheduler.loader.basic.custom_markdownify import md
            self._converter = md
        except ImportError as e:
            logger.error(f"Failed to import custom markdownify: {e}")
            raise

    def __call__(self, html_content: str, page_id: str, config: ConfluenceConfig) -> str:
        """
        Convert HTML to markdown, including pre-processing of macros.

        Args:
            html_content: HTML content to convert.
            page_id: The ID of the Confluence page, used to resolve macros.
            config: The Confluence configuration object.

        Returns:
            Converted markdown content.
        """
        if not html_content:
            return ""

        try:
            # Step 1: Pre-process HTML to handle macros
            processed_html = self._preprocess_confluence_macros(html_content, page_id, config)
            # Step 2: Convert the cleaned HTML to Markdown
            return self._converter(processed_html)
        except Exception as e:
            logger.error(f"Error converting HTML to markdown for page {page_id}: {e}", exc_info=True)
            return f"*Error converting content to markdown: {str(e)}*"

    def _get_attachment_absolute_url(self, att: dict) -> Optional[str]:
        """Helper to get the absolute download URL for a Confluence attachment dict."""
        download_path = att.get('_links', {}).get('download')
        if download_path and self.base_url:
            return self.base_url.rstrip('/') + download_path
        return None

    def _preprocess_confluence_macros(self, html_content: str, page_id: str, config: ConfluenceConfig) -> str:
        """
        Replaces all known Confluence macros with a meaningful representation or removes them.
        This is the logic moved from ContentDownloader.
        """
        try:
            attachments = self.client.get_page_attachments_all_current(page_id)
            attachments_by_title = {att.get('title'): att for att in attachments}
        except Exception as e:
            logger.warning(f"Could not fetch attachments for page {page_id} to resolve macros: {e}")
            attachments_by_title, attachments = {}, []

        macro_pattern = re.compile(r'(<ac:structured-macro[\s\S]*?</ac:structured-macro>)', re.IGNORECASE)

        def macro_replacer(match):
            macro_xml = match.group(1)
            macro_name_match = re.search(r'ac:name="([^"]+)"', macro_xml)
            if not macro_name_match:
                return ""  # Remove unknown macro

            macro_name = macro_name_match.group(1).lower()

            # --- Handler for Draw.io ---
            if macro_name == "drawio":
                filename_match = re.search(r'<ac:parameter ac:name="filename">(.*?)</ac:parameter>', macro_xml)
                diagram_name_match = re.search(r'<ac:parameter ac:name="diagramName">(.*?)</ac:parameter>', macro_xml)

                filename = filename_match.group(1) if filename_match else None
                diagram_name = diagram_name_match.group(1) if diagram_name_match else None
                display_name = filename or diagram_name or "Draw.io Diagram"

                if config.attachments.extract_drawio_as_documents:
                    # When extraction is enabled, the diagram is a separate document.
                    # Remove the macro from the page content.
                    logger.debug(f"Draw.io extraction enabled - removing macro for '{display_name}' from page {page_id}")
                    return ""
                else:
                    # When extraction is disabled, create a link to the PNG attachment.
                    logger.debug(f"Draw.io extraction disabled - creating link to PNG attachment for '{display_name}'")
                    found_att = attachments_by_title.get(f"{diagram_name}.png") or attachments_by_title.get(filename)
                    if found_att:
                        download_url = self._get_attachment_absolute_url(found_att)
                        # This markdown matches the example you provided
                        return f"\n\n![{display_name}]({download_url})\n\n*[Download {display_name}]({download_url})*\n\n"
                    else:
                        return f"\n\n**Draw.io Diagram: {display_name}** *(attachment not found)*\n\n"

            # --- Handler for 'view-file' ---
            elif macro_name == "view-file":
                filename_match = re.search(r'<ri:attachment ri:filename="([^"]+)"', macro_xml)
                if not filename_match:
                    return ""

                filename = filename_match.group(1)
                found_att = attachments_by_title.get(filename)

                if found_att:
                    download_url = self._get_attachment_absolute_url(found_att)
                    # This markdown matches the example you provided
                    return f"\n\n[View File: {filename}]({download_url})\n\n"
                else:
                    return f"\n\n[File: {filename} (Attachment not found)]\n\n"

            # --- Default: Remove any other unhandled macros ---
            logger.debug(f"Removing unhandled macro: {macro_name}")
            return ""

        # Apply the replacement
        processed_html = macro_pattern.sub(macro_replacer, html_content)
        return processed_html


class AlternativeMarkdownConverter:
    """
    Alternative markdown converter (EXAMPLE).
    This would need the same modifications as DefaultMarkdownConverter
    to handle macro pre-processing if it were to be used.
    """
    def __init__(self, client: Any):
        logger.warning("AlternativeMarkdownConverter is an example and does not process macros.")
        self.client = client
        # ... initialization logic ...

    def __call__(self, html_content: str, page_id: str, config: ConfluenceConfig) -> str:
        # NOTE: This implementation is incomplete as it doesn't do the preprocessing.
        # You would add a call to a preprocessing method here as well.
        if not html_content:
            return ""
        # ... conversion logic ...
        return "Not implemented with macro processing."


def create_markdown_converter(client: Any, converter_type: str = "default") -> MarkdownConverter:
    """
    Factory function to create markdown converters.

    Args:
        client: The Confluence API client instance.
        converter_type: Type of converter to create ("default" or "alternative")

    Returns:
        A markdown converter instance

    Raises:
        ValueError: If converter_type is not recognized
    """
    if converter_type == "default":
        return DefaultMarkdownConverter(client)
    elif converter_type == "alternative":
        return AlternativeMarkdownConverter(client)
    else:
        raise ValueError(f"Unknown converter type: {converter_type}")
