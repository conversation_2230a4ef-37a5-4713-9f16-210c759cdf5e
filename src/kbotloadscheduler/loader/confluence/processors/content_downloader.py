import logging
import os
from typing import Any, Dict, Optional

from kbotloadscheduler.gcs import gcs_utils
from kbotloadscheduler.bean.beans import DocumentBean, Metadata
from kbotloadscheduler.exceptions.confluence_exceptions import ConfluenceException
from ..config.confluence_config import ConfluenceConfig
from ..utils.retry import retry_operation

# Import utility functions
from ..utils.file_utils import (
    create_safe_filename,
    create_unique_filename,
    clean_filename,
    download_with_temp_file
)
from ..utils.url_utils import (
    extract_page_id_from_attachment_path,
    extract_parent_page_url,
    extract_parent_page_url_from_page_data
)
from ..utils.drawio_utils import parse_drawio_id
from ..utils.metadata_utils import (
    build_attachment_metadata,
    build_page_metadata,
    build_drawio_metadata,
    EXPORT_FORMAT_PDF,
    EXPORT_FORMAT_MARKDOWN,
    EXPORT_FORMAT_HTML,
    FILE_EXT_PDF,
    FILE_EXT_MARKDOWN,
    FILE_EXT_HTML
)

# Import the new processors and converters
from .drawio_processor import DrawioProcessor
from .markdown_converter import MarkdownConverter, create_markdown_converter

# Constants
FILENAME_STRATEGY_APPEND_ID = "append_id"


class ContentDownloader:
    """Downloads Confluence document content with configurable processors."""

    def __init__(self, client, markdown_converter: Optional[MarkdownConverter] = None):
        """
        Initialize the content downloader.

        Args:
            client: Confluence client for API calls
            markdown_converter: Optional markdown converter for dependency injection
        """
        self.client = client
        self.drawio_processor = DrawioProcessor(client)

        # Use dependency injection for markdown converter
        self.markdown_converter = markdown_converter or create_markdown_converter(client, "default")

    # --- Public Methods ---

    def download_page(self, item_id: str, document: DocumentBean, output_path: str, config: ConfluenceConfig) -> Dict[str, Any]:
        """
        Download page content respecting the export format specified in configuration.
        Acts as a router to format-specific download methods.
        """
        export_format = config.basic.export_format.lower()
        logging.info("Export format configured: '%s'", config.basic.export_format)

        if export_format in ["markdown", "md"]:
            return self._download_page_as_markdown(item_id, document, output_path, config)
        elif export_format == "pdf":
            return self._download_page_as_pdf(item_id, document, output_path, config)
        else:  # Default or "html"
            return self._download_page_as_html(item_id, document, output_path, config)

    def download_attachment(self, item_id: str, document: DocumentBean, output_path: str, config: ConfluenceConfig) -> Dict[str, Any]:
        """
        Download a Confluence attachment.
        """
        try:
            logging.info(f"Starting attachment download {item_id}, path: {document.path}, name: {document.name}")

            attachment_info = self._validate_and_extract_attachment_info(item_id, document)
            filename_info = self._process_attachment_filename(
                document.name, item_id, output_path, config.file_processing.duplicate_filename_strategy
            )
            temp_path, file_size = self._download_attachment_content(
                item_id, document, attachment_info['page_id']
            )

            try:
                destination_path = self._store_attachment_to_gcs(
                    temp_path, output_path, filename_info['unique_filename']
                )
                metadata = build_attachment_metadata(
                    destination_path, file_size, document.name,
                    filename_info, attachment_info, Metadata.LOCATION
                )
                if filename_info['conflict_resolved']:
                    logging.info(f"Filename conflict resolved: '{filename_info['safe_filename']}' -> '{filename_info['unique_filename']}' (strategy: {config.file_processing.duplicate_filename_strategy})")
                return metadata
            finally:
                if temp_path and os.path.exists(temp_path):
                    os.remove(temp_path)
                    logging.debug("Temporary file removed: %s", temp_path)
        except ConfluenceException:
            raise
        except Exception as e:
            logging.error(f"Unexpected error downloading attachment {item_id}: {e}", exc_info=True)
            raise ConfluenceException(f"Unexpected error downloading attachment {item_id}") from e

    def download_drawio_diagram(self, item_id: str, document: DocumentBean, output_path: str, config: ConfluenceConfig) -> Dict[str, Any]:
        """
        Download a Draw.io diagram by generating its markdown content.
        """
        try:
            drawio_id_components = parse_drawio_id(item_id)
            page_id = drawio_id_components.page_id
            logging.info(f"Processing Draw.io diagram {item_id} from page {page_id}")

            page_data = self.drawio_processor.get_page_content_with_retry(page_id, config)
            markdown_content = self.drawio_processor.generate_drawio_markdown(
                diagram_id=item_id, document=document, page_data=page_data, config=config
            )

            base_filename = f"{clean_filename(document.name)}.{FILE_EXT_MARKDOWN}"
            strategy = config.file_processing.duplicate_filename_strategy
            unique_filename = create_unique_filename(base_filename, item_id, output_path, strategy)
            destination_path = os.path.join(output_path, unique_filename)
            content_bytes = markdown_content.encode('utf-8')
            gcs_utils.create_file_with_bytes_content(destination_path, content_bytes)

            if unique_filename != base_filename:
                logging.info(f"Draw.io filename conflict resolved: '{base_filename}' -> '{unique_filename}' (strategy: {strategy})")

            parent_page_url = extract_parent_page_url_from_page_data(page_data, page_id, self.client)
            metadata = build_drawio_metadata(
                destination_path, len(content_bytes), f"{document.name}.{FILE_EXT_MARKDOWN}",
                unique_filename, parent_page_url, page_id, Metadata.LOCATION,
                markdown_content if config.basic.include_content_in_metadata else None,
                config.basic.include_content_in_metadata
            )
            logging.info(f"Draw.io diagram downloaded successfully: {destination_path}")
            return metadata
        except Exception as e:
            logging.error(f"Draw.io diagram download failed {item_id}: {e}", exc_info=True)
            raise ConfluenceException(f"Draw.io diagram download failed {item_id}") from e

    # --- Private/Internal Methods ---

    def _process_and_save_page(self, item_id: str, document: DocumentBean, output_path: str, config: ConfluenceConfig, export_format: str) -> Dict[str, Any]:
        """Helper to fetch, process, and save page content for HTML or Markdown."""
        page_data = self._get_page_content_with_retry(item_id, config)
        html_content = page_data.get('body', {}).get('storage', {}).get('value', '')

        if not html_content:
            logging.warning(f"HTML content is empty for page {item_id}. An empty file will be created.")
            final_content = ""
            file_ext = export_format # or .txt
        else:
            if export_format == EXPORT_FORMAT_MARKDOWN:
                # The markdown_converter now handles everything.
                final_content = self.markdown_converter(
                    html_content, page_id=item_id, config=config
                )
                file_ext = FILE_EXT_MARKDOWN
            else:  # HTML
                # For HTML export, we save the raw storage format.
                # You could also have the converter clean it if needed.
                final_content = html_content
                file_ext = FILE_EXT_HTML

        clean_filename_str = f"{clean_filename(document.name)}.{file_ext}"
        destination_path = os.path.join(output_path, clean_filename_str)
        content_bytes = final_content.encode('utf-8')
        gcs_utils.create_file_with_bytes_content(destination_path, content_bytes)

        return build_page_metadata(
            destination_path,
            len(content_bytes),
            export_format,
            Metadata.LOCATION,
            final_content if config.basic.include_content_in_metadata else None,
            config.basic.include_content_in_metadata
        )
    def _download_page_as_html(self, item_id: str, document: DocumentBean, output_path: str, config: ConfluenceConfig) -> Dict[str, Any]:
        """Private method to download page as HTML."""
        try:
            return self._process_and_save_page(item_id, document, output_path, config, EXPORT_FORMAT_HTML)
        except Exception as e:
            logging.error(f"Page download as HTML failed {item_id}: {e}", exc_info=True)
            raise ConfluenceException(f"Page download as HTML failed {item_id}") from e

    def _download_page_as_markdown(self, item_id: str, document: DocumentBean, output_path: str, config: ConfluenceConfig) -> Dict[str, Any]:
        """Private method to download and convert page to Markdown."""
        try:
            return self._process_and_save_page(item_id, document, output_path, config, EXPORT_FORMAT_MARKDOWN)
        except Exception as e:
            logging.error(f"Page download or Markdown conversion failed {item_id}: {e}", exc_info=True)
            raise ConfluenceException(f"Page download to Markdown failed {item_id}") from e

    def _download_page_as_pdf(self, item_id: str, document: DocumentBean, output_path: str, config: ConfluenceConfig) -> Dict[str, Any]:
        """Private method to download page as PDF."""
        try:
            pdf_content = self.client.export_page_as_pdf(item_id)
            clean_filename_str = f"{clean_filename(document.name)}.{FILE_EXT_PDF}"
            destination_path = os.path.join(output_path, clean_filename_str)
            gcs_utils.create_file_with_bytes_content(destination_path, pdf_content)
            return build_page_metadata(
                destination_path, len(pdf_content), EXPORT_FORMAT_PDF,
                Metadata.LOCATION, None, config.basic.include_content_in_metadata
            )
        except Exception as e:
            logging.error(f"Page download as PDF failed {item_id}: {e}", exc_info=True)
            raise ConfluenceException(f"Page download as PDF failed {item_id}") from e

    def _validate_and_extract_attachment_info(self, item_id: str, document: DocumentBean) -> Dict[str, Any]:
        """Validate and extract basic attachment information."""
        page_id = extract_page_id_from_attachment_path(document.path, self.client)
        if not page_id:
            raise ConfluenceException(f"Cannot extract page_id from attachment path: {document.path}")
        logging.info(f"Extracted page ID: {page_id} for attachment {item_id}")
        parent_page_url = extract_parent_page_url(document.path, page_id, self.client)
        return {'page_id': page_id, 'parent_page_url': parent_page_url}

    def _process_attachment_filename(self, filename: str, item_id: str, output_path: str, strategy: str) -> Dict[str, Any]:
        """Process filename to create safe and unique name."""
        try:
            safe_filename = create_safe_filename(filename, item_id)
            unique_filename = create_unique_filename(safe_filename, item_id, output_path, strategy)
            logging.info(f"Safe filename created: {unique_filename}")
            return {
                'safe_filename': safe_filename,
                'unique_filename': unique_filename,
                'conflict_resolved': unique_filename != safe_filename
            }
        except Exception as e:
            raise ConfluenceException(f"Error creating safe filename: {e}") from e

    def _download_attachment_content(self, item_id: str, document: DocumentBean, page_id: str) -> tuple[str, int]:
        """Download attachment content to a temporary file."""
        attachment_api_id = item_id.replace("att", "")
        logging.info(f"Downloading attachment content for ID: {attachment_api_id}")
        try:
            content_bytes = self.client.get_attachment_content(attachment_id=attachment_api_id)
            if not content_bytes:
                logging.warning(f"Attachment {attachment_api_id} downloaded with empty content.")

            def write_to_temp(temp_path):
                with open(temp_path, 'wb') as f:
                    f.write(content_bytes)
            temp_path = download_with_temp_file(write_to_temp)

            if not os.path.exists(temp_path):
                raise ConfluenceException("Failed to write downloaded content to temporary file.")
            file_size = os.path.getsize(temp_path)
            logging.info(f"Attachment {attachment_api_id} downloaded successfully to temp file. Size: {file_size} bytes")
            return temp_path, file_size
        except Exception as e:
            logging.error(f"Failed to download attachment content for ID {attachment_api_id}: {e}", exc_info=True)
            raise ConfluenceException(f"API call to download attachment {attachment_api_id} failed.") from e

    def _store_attachment_to_gcs(self, temp_path: str, output_path: str, unique_filename: str) -> str:
        """Store temporary file to GCS."""
        destination_path = os.path.join(output_path, unique_filename)
        logging.info(f"Writing to GCS: {destination_path}")
        with open(temp_path, "rb") as f:
            content_bytes = f.read()
        gcs_utils.create_file_with_bytes_content(destination_path, content_bytes)
        if not gcs_utils.exists_file_gcs(destination_path):
            raise ConfluenceException(f"File {destination_path} was not created in GCS.")
        logging.info(f"File created successfully in GCS: {destination_path}")
        return destination_path

    def _get_page_content_with_retry(self, item_id: str, config: ConfluenceConfig) -> Dict:
        """Helper method to get page content with retry logic."""
        page_data = retry_operation(
            self.client.get_page_content,
            page_id=item_id,
            expand="body.storage,version",
            max_attempts=config.performance.retry_attempts,
            delay_seconds=config.performance.retry_delay_seconds
        )
        if not page_data:
            raise ConfluenceException(f"Cannot retrieve page content {item_id} after multiple attempts.")
        return page_data
