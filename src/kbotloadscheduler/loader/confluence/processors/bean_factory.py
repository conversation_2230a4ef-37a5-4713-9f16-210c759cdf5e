"""
Factory for creating DocumentBean objects from raw Confluence data.
"""
import urllib.parse
from datetime import datetime, UTC
from typing import Dict

from kbotloadscheduler.bean.beans import DocumentBean, SourceBean
from ..utils.document_id_utils import DocumentIdFormatter


def create_page_bean(page_data: Dict, source: SourceBean, base_url: str) -> DocumentBean:
    """Create a DocumentBean for a Confluence page."""
    page_id = page_data.get("id", "unknown")
    page_title = page_data.get("title", f"Page {page_id}")

    # Build the URL
    web_ui_link = page_data.get("_links", {}).get("webui")
    page_url = f"{base_url}{web_ui_link}" if web_ui_link else f"{base_url}/pages/viewpage.action?pageId={page_id}"

    doc_id = DocumentIdFormatter.create_page_id(source, page_id)
    return DocumentBean(
        id=doc_id,
        name=page_title,
        path=page_url,
        modification_time=datetime.now(UTC)  # Could be extracted from page_data['version']['when']
    )


def create_attachment_bean(att_data: Dict, source: SourceBean, parent_page_id: str, base_url: str) -> DocumentBean:
    """Create a DocumentBean for a Confluence attachment."""
    att_id = att_data.get("id", "unknown")
    att_title = att_data.get("title", f"attachment_{att_id}")

    # Build the download URL
    download_link = att_data.get("_links", {}).get("download")
    if download_link:
        att_url = f"{base_url}{download_link}"
    else:
        # Encode the file name to avoid issues with spaces and special characters
        encoded_att_title = urllib.parse.quote(att_title)
        att_url = f"{base_url}/download/attachments/{parent_page_id}/{encoded_att_title}"

    doc_id = DocumentIdFormatter.create_attachment_id(source, att_id)

    return DocumentBean(
        id=doc_id,
        name=att_title,
        path=att_url,
        modification_time=datetime.now(UTC)
    )


def create_drawio_bean(diagram_data: Dict, source: SourceBean, page_data: Dict, base_url: str) -> DocumentBean:
    """
    Create a DocumentBean for a Draw.io diagram.

    Args:
        diagram_data: Dictionary containing diagram information with diagram_id from detector
        source: SourceBean containing domain and source codes
        page_data: Dictionary containing page information
        base_url: Base URL for Confluence instance

    Returns:
        DocumentBean with unique ID based on detector's diagram_id
    """
    # Use the unique diagram_id from the detector (e.g., "page123_macro_0" or "page123_att_456")
    diagram_id = diagram_data.get("diagram_id", "unknown")
    diagram_title = diagram_data.get("title") or f"Diagram {diagram_data.get('diagram_type', 'Draw.io')}"

    # Build the URL (points to the page containing the diagram)
    page_web_ui_link = page_data.get("_links", {}).get("webui")
    diagram_url = f"{base_url}{page_web_ui_link}" if page_web_ui_link else f"{base_url}/pages/viewpage.action?pageId={page_data.get('id')}"

    # If it's an attachment, point to the attachment
    if diagram_data.get("attachment_id"):
        # Encode the diagram name to avoid issues with spaces and special characters
        encoded_diagram_title = urllib.parse.quote(diagram_title)
        diagram_url = f"{base_url}/download/attachments/{page_data.get('id')}/{encoded_diagram_title}"

    # Use the detector's unique diagram_id directly in the DocumentBean ID
    doc_id = DocumentIdFormatter.create_drawio_id(source, diagram_id)
    return DocumentBean(
        id=doc_id,
        name=diagram_title,
        path=diagram_url,
        modification_time=datetime.now(UTC)
    )
