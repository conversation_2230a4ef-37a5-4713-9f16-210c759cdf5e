"""
Generates markdown content from Draw.io metadata.
"""
import logging
import re
from typing import Dict, List

from .drawio_extractor import DrawioMetadata

logger = logging.getLogger(__name__)


class DrawioMarkdownGenerator:
    """
    Creates searchable and readable markdown from Draw.io metadata.
    """

    def generate_markdown(self, metadata: DrawioMetadata, source_info: Dict, page_context: str) -> str:
        """
        Generates the full markdown document for a Draw.io diagram.

        Args:
            metadata: Extracted metadata from the diagram.
            source_info: Dictionary with source page title, URL, etc.
            page_context: Surrounding text from the Confluence page.

        Returns:
            A string containing the generated markdown.
        """
        md_parts = []

        # --- Header ---
        title = source_info.get('title', 'Draw.io Diagram')
        md_parts.append(f"# {title}\n")
        md_parts.append(
            f"**Source Page**: [{source_info.get('page_title', 'N/A')}]({source_info.get('page_url', '#')})")
        if source_info.get('last_modified'):
            md_parts.append(f"**Last Modified**: {source_info.get('last_modified')}")
        md_parts.append("\n---\n")

        # --- Diagram Type ---
        md_parts.append(f"## Diagram Overview\n")
        md_parts.append(f"**Inferred Diagram Type**: `{metadata.diagram_type.capitalize()}`\n")

        # --- Handle SVG-only vs Full XML ---
        if not metadata.shapes and not metadata.connections and metadata.all_text:
            md_parts.append("### Extracted Text Elements\n")
            md_parts.append(
                "_Note: This diagram was processed from a rendered SVG, so detailed shape and connection information is not available. The following text elements were found:_")
            for text in sorted(list(set(metadata.all_text))):
                if text:
                    md_parts.append(f"- `{text}`")
        else:
            # Full metadata processing
            self._generate_semantic_summary(metadata, md_parts)
            self._generate_detailed_elements(metadata, md_parts)

        return "\n".join(md_parts)

    def _generate_semantic_summary(self, metadata: DrawioMetadata, md_parts: List[str]):
        """Generates the semantic summary section."""
        md_parts.append("### Semantic Summary\n")
        has_content = False
        for category, items in metadata.semantic_elements.items():
            # Clean items before adding
            cleaned_items = []
            for item in items:
                if item:
                    # Remove HTML tags and normalize whitespace
                    cleaned_text = ' '.join(re.sub('<[^<]+?>', ' ', item).split())
                    if cleaned_text:
                        cleaned_items.append(cleaned_text)

            if cleaned_items:
                has_content = True
                md_parts.append(f"**{category.replace('_', ' ').title()}:**\n")
                for item in sorted(list(set(cleaned_items))):
                    md_parts.append(f"- {item}")
                md_parts.append("\n")

        if not has_content:
            md_parts.append("No distinct semantic elements were identified.\n")

    def _generate_detailed_elements(self, metadata: DrawioMetadata, md_parts: List[str]):
        """Generates the detailed elements section with an enriched connections table."""
        md_parts.append("\n---\n")
        md_parts.append("## Detailed Elements\n")

        # Shapes table remains the same - it's our "dictionary"
        if metadata.shapes:
            md_parts.append("### Shapes\n")
            md_parts.append("| ID | Type | Text |")
            md_parts.append("|---|---|---|")
            # Clean text before adding to table
            for shape in sorted(metadata.shapes, key=lambda s: (s.id or "")):
                text_md = re.sub('<[^<]+?>', ' ', shape.text).strip()
                text_md = ' '.join(text_md.split())
                if text_md:
                    md_parts.append(f"| `{shape.id}` | `{shape.shape_type}` | {text_md} |")
            md_parts.append("\n")

        # Connections table is now enriched
        if metadata.connections:
            md_parts.append("### Connections\n")
            md_parts.append("| Source | Target | Label |")
            md_parts.append("|---|---|---|")

            # Create a quick lookup map for shape text
            shape_text_map = {shape.id: ' '.join(re.sub('<[^<]+?>', ' ', shape.text).split()) for shape in
                              metadata.shapes}

            # Use a stable sort order for connections
            for conn in sorted(metadata.connections, key=lambda c: (c.source or "", c.target or "")):
                # Handle source
                source_text = shape_text_map.get(conn.source, "*Unlabeled Shape*")
                source_id_str = f"`{conn.source}`" if conn.source else "*N/A*"
                source_cell = f"{source_text} ({source_id_str})"

                # Handle target
                target_text = shape_text_map.get(conn.target, "*Unlabeled Shape*")
                target_id_str = f"`{conn.target}`" if conn.target else "*N/A*"
                target_cell = f"{target_text} ({target_id_str})"

                # Handle label
                label_text = ' '.join(re.sub('<[^<]+?>', ' ', conn.text).split())

                if conn.source and conn.target:
                    md_parts.append(f"| {source_cell} | {target_cell} | {label_text} |")
