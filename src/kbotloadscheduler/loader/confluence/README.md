# Chargeur de Données Confluence

Ce document décrit l'architecture et le fonctionnement du chargeur de données pour Confluence. Ce module est conçu pour découvrir, filtrer et télécharger de manière fiable et performante le contenu d'une ou plusieurs instances Confluence.

## Table des Matières

- [Chargeur de Données Confluence](#chargeur-de-données-confluence)
  - [Table des Matières](#table-des-matières)
  - [1. Vue d'Ensemble](#1-vue-densemble)
  - [2. Fonctionnalités Clés](#2-fonctionnalités-clés)
  - [3. Architecture](#3-architecture)
  - [4. Flux de Travail Détaillé](#4-flux-de-travail-détaillé)
    - [Phase 1 : Découverte (`get_document_list`)](#phase-1--découverte-get_document_list)
    - [Phase 2 : Téléchargement (`get_document`)](#phase-2--téléchargement-get_document)
  - [5. Configuration (JSON)](#5-configuration-json)
    - [Exemple Complet](#exemple-complet)
    - [Référence des Paramètres](#référence-des-paramètres)
  - [6. Authentification](#6-authentification)
    - [Stratégies d'Authentification](#stratégies-dauthentification)
    - [Structure du Secret](#structure-du-secret)
  - [7. Gestion des Erreurs et Résilience](#7-gestion-des-erreurs-et-résilience)
  - [8. Mode Mock pour le Développement](#8-mode-mock-pour-le-développement)
  - [9. Exemples d'Utilisation via API](#9-exemples-dutilisation-via-api)
    - [9.1 Lister les documents d'une source (`/loader/list`)](#91-lister-les-documents-dune-source-loaderlist)
      - [Commande `curl`](#commande-curl)
      - [Fichier d'entrée (`getlist.json`)](#fichier-dentrée-getlistjson)
      - [Fichier de sortie (Exemple)](#fichier-de-sortie-exemple)
    - [9.2 Télécharger un document spécifique (`/loader/document`)](#92-télécharger-un-document-spécifique-loaderdocument)
      - [Commande `curl`](#commande-curl-1)
      - [Fichier d'entrée (`test_png.getdoc.json`)](#fichier-dentrée-test_pnggetdocjson)
      - [Fichier de sortie (Exemple)](#fichier-de-sortie-exemple-1)

---

## 1. Vue d'Ensemble

Le chargeur Confluence est un composant robuste qui orchestre la connexion à Confluence, la recherche de contenu (pages, pièces jointes) selon des règles de filtrage complexes, et le téléchargement de ce contenu pour un traitement ultérieur.

Il est conçu pour être :
-   **Performant** : grâce au traitement parallèle des espaces et à une gestion optimisée des appels API.
-   **Résilient** : avec des mécanismes de nouvelles tentatives (retry) et de disjoncteur (circuit breaker) pour gérer les instabilités réseau ou API.
-   **Flexible** : via un système de configuration JSON riche permettant d'adapter finement le comportement du chargeur.
-   **Économe en mémoire** : grâce à un mode de traitement en streaming pour les très grands espaces.

## 2. Fonctionnalités Clés

-   **Traitement Parallèle** : Capacité à traiter plusieurs espaces Confluence simultanément pour accélérer la découverte.
-   **Découverte Intelligente** :
    -   Navigation récursive dans les arborescences de pages avec une profondeur configurable.
    -   Détection et traitement des diagrammes **Draw.io** comme des documents de premier ordre.
    -   Filtrage avancé des pièces jointes, y compris un mode "utilisé dans le contenu" pour ne récupérer que les fichiers réellement référencés.
-   **Filtrage Puissant** :
    -   Filtrage par date de dernière modification, labels (inclus/exclus), et extensions de fichiers.
    -   Support pour des requêtes **CQL (Confluence Query Language)** personnalisées pour des scénarios de recherche complexes.
-   **Authentification Flexible** : Supporte une authentification centralisée (`global`) ou par périmètre (`perimeter`), en utilisant des jetons PAT ou des paires utilisateur/jeton API.
-   **Mode Streaming** : Un mode optionnel (`use_memory_efficient_processing`) qui traite les documents un par un pour éviter la saturation de la mémoire sur les espaces contenant des dizaines de milliers de pages.
-   **Mode Mock Intégré** : Permet de tester le flux de travail sans se connecter réellement à Confluence, idéal pour le développement et les tests CI/CD.

## 3. Architecture

Le chargeur est composé de plusieurs classes spécialisées qui collaborent pour accomplir la tâche.

-   `ConfluenceLoader` (`confluence_loader.py`)
    -   **Rôle** : **L'Orchestrateur**. C'est le point d'entrée principal. Il initialise tous les autres composants et pilote les deux phases principales : la découverte (`get_document_list`) et le téléchargement (`get_document`).

-   `SpaceProcessor` (`space/space_processor.py`)
    -   **Rôle** : **Le Découvreur**. Il est responsable de l'exploration d'un *unique* espace Confluence. Il identifie toutes les pages, pièces jointes et diagrammes Draw.io et crée des "bons de travail" (`DocumentBean`) sans télécharger leur contenu.

-   `CqlSearch` (`search/cql_search.py`)
    -   **Rôle** : **Le Chercheur**. Spécialisé dans la construction et l'exécution de requêtes CQL. Il gère la pagination, les stratégies de repli (fallback) et la résilience des recherches.

-   `ContentDownloader` (`processors/content_downloader.py`)
    -   **Rôle** : **Le Téléchargeur**. Responsable du téléchargement effectif du contenu d'un `DocumentBean` (page, pièce jointe ou diagramme) et de sa sauvegarde dans le système de fichiers.

-   `ConfluenceClient` (`client/confluence_client.py`)
    -   **Rôle** : **Le Communicateur**. Un client API robuste qui encapsule les appels de bas niveau vers l'API Confluence. Il gère l'authentification, la gestion des sessions et la traduction des erreurs HTTP en exceptions sémantiques.

-   `ConfluenceConfigFactory` et `ConfluenceConfig` (`config/*.py`)
    -   **Rôle** : **Le Configurateur**. Ces classes gèrent la configuration. Elles transforment le JSON brut de la `SourceBean` en un objet de configuration (`ConfluenceConfig`) fortement typé, en assurant la rétrocompatibilité.

## 4. Flux de Travail Détaillé

### Phase 1 : Découverte (`get_document_list`)

1.  `ConfluenceLoader` reçoit une `SourceBean`.
2.  Il initialise ses composants : `ConfluenceConfig` est créé à partir du JSON de la source, puis le `ConfluenceClient` est instancié avec les informations d'authentification. Enfin, `CqlSearch` et `SpaceProcessor` sont créés.
3.  Pour chaque `space` défini dans la configuration (en parallèle si activé) :
    a. `ConfluenceLoader` délègue le travail au `SpaceProcessor`.
    b. `SpaceProcessor` utilise `CqlSearch` pour trouver les pages racines de l'espace.
    c. Pour chaque page, `SpaceProcessor` opère récursivement :
        i.  Crée un `DocumentBean` pour la page.
        ii. Analyse la page pour trouver des diagrammes **Draw.io** et crée des `DocumentBean`s spécifiques.
        iii. Utilise le `ConfluenceClient` pour lister les pièces jointes, les filtre (par extension, par usage), et crée des `DocumentBean`s pour celles qui sont valides.
        iv. Utilise `CqlSearch` pour trouver les pages enfants et recommence le processus jusqu'à la profondeur définie.
4.  `ConfluenceLoader` agrège les listes de `DocumentBean` de tous les espaces et retourne la liste complète.

### Phase 2 : Téléchargement (`get_document`)

1.  `ConfluenceLoader` reçoit un `DocumentBean` spécifique à télécharger.
2.  Il analyse l'ID du document pour déterminer son type (page, pièce jointe, drawio).
3.  Il délègue le téléchargement au `ContentDownloader`.
4.  `ContentDownloader` utilise le `ConfluenceClient` pour effectuer l'appel API approprié (`get_page_content`, `get_attachment_content`, etc.).
5.  Il sauvegarde le contenu reçu (HTML, PDF, fichier binaire...) dans le chemin de sortie (`output_path`).
6.  Il retourne les métadonnées du téléchargement (chemin du fichier, taille, etc.).

## 5. Configuration (JSON)

La configuration du chargeur est fournie via un objet JSON dans la `SourceBean`.

### Exemple Complet

```json
{
  "basic": {
    "spaces": ["PROJETX", "SUPPORT"],
    "max_results": 5000,
    "export_format": "markdown",
    "include_child_pages": true,
    "child_page_depth": 10
  },
  "filtering": {
    "labels": ["featured", "public-release"],
    "exclude_labels": ["archive", "draft"],
    "last_modified_days": 180,
    "custom_cql": null
  },
  "attachments": {
    "include_attachments": true,
    "attachment_filter_mode": "content_used",
    "file_extensions": ["pdf", "docx", "png", "jpg", "drawio"],
    "extract_drawio_as_documents": true,
    "include_drawio_png_exports": false
  },
  "performance": {
    "parallel_downloads": true,
    "max_parallel_workers": 4,
    "retry_attempts": 3,
    "use_memory_efficient_processing": true
  },
  "auth": {
    "confluence_auth_mode": "perimeter"
  },
  "file_processing": {
    "duplicate_filename_strategy": "append_id",
    "max_filename_length": 255
  }
}
```

### Référence des Paramètres

<details>
<summary><strong><code>basic</code>: Configuration de base</strong></summary>

| Clé | Description | Type | Défaut |
| --- | --- | --- | --- |
| `spaces` | Liste des clés des espaces Confluence à traiter. | `list[str]` | `[]` |
| `max_results` | Nombre maximum de pages à récupérer par espace. | `int` \| `null` | `null` (illimité) |
| `export_format` | Format d'exportation pour le contenu des pages. | `str` | `"markdown"` |
| `include_child_pages` | Inclure les pages enfants dans la découverte. | `bool` | `true` |
| `child_page_depth`| Profondeur de récursion maximale pour les pages enfants. | `int` | `15` |

</details>

<details>
<summary><strong><code>filtering</code>: Filtrage du contenu</strong></summary>

| Clé | Description | Type | Défaut |
| --- | --- | --- | --- |
| `labels` | Ne garder que les pages ayant au moins un de ces labels. | `list[str]` \| `null` | `null` |
| `exclude_labels`| Exclure les pages ayant au moins un de ces labels. | `list[str]` \| `null` | `null` |
| `last_modified_days` | Ne garder que les pages modifiées dans les N derniers jours. | `int` \| `null` | `null` |
| `custom_cql`| Utiliser une requête CQL personnalisée. **Remplace** les autres filtres. | `str` \| `null` | `null` |

</details>

<details>
<summary><strong><code>attachments</code>: Gestion des pièces jointes</strong></summary>

| Clé | Description | Type | Défaut |
| --- | --- | --- | --- |
| `include_attachments` | Activer le traitement des pièces jointes. | `bool` | `true` |
| `attachment_filter_mode` | Mode de filtrage : `"all_current"` (toutes) ou `"content_used"` (seulement celles liées dans la page). | `str` | `"content_used"` |
| `file_extensions` | Liste blanche des extensions de fichiers à inclure. | `list[str]` | (Liste par défaut) |
| `extract_drawio_as_documents`| Traiter les diagrammes Draw.io comme des documents à part entière. | `bool` | `true` |
| `include_drawio_png_exports`| Inclure les exports PNG redondants des diagrammes Draw.io. | `bool` | `false` |

</details>

<details>
<summary><strong><code>performance</code>: Performance et résilience</strong></summary>

| Clé | Description | Type | Défaut |
| --- | --- | --- | --- |
| `parallel_downloads` | Traiter les espaces en parallèle. | `bool` | `false` |
| `max_parallel_workers` | Nombre maximum de workers pour le traitement parallèle. | `int` | `1` |
| `retry_attempts`| Nombre de tentatives en cas d'échec d'un appel API. | `int` | `3` |
| `retry_delay_seconds`| Délai en secondes entre chaque tentative. | `int` | `2` |
| `circuit_breaker_threshold`| Nombre d'échecs consécutifs avant d'ouvrir le disjoncteur. | `int` | `5` |
| `circuit_breaker_timeout_seconds`| Temps en secondes avant que le disjoncteur ne tente de se refermer. | `int` | `60` |
| `use_memory_efficient_processing` | Activer le mode streaming pour les grands espaces. | `bool` | `true` |

</details>

<details>
<summary><strong><code>auth</code> et <code>file_processing</code>: Avancé</strong></summary>

| Clé | Description | Type | Défaut |
| --- | --- | --- | --- |
| `auth.confluence_auth_mode`| Stratégie d'authentification : `"global"` ou `"perimeter"`. | `str` | `"global"` |
| `file_processing.duplicate_filename_strategy` | Stratégie en cas de noms de fichiers dupliqués : `"append_id"`, `"append_counter"`, ou `"overwrite"`. | `str` | `"append_id"` |
| `file_processing.max_filename_length`| Longueur maximale autorisée pour les noms de fichiers. | `int` | `255` |

</details>

## 6. Authentification

Le client supporte deux stratégies d'authentification et deux types de jetons.

### Stratégies d'Authentification

Ceci est contrôlé par `auth.confluence_auth_mode`.

-   **`"global"` (par défaut)** : Le chargeur utilise un seul secret global, dont le nom est défini dans la configuration principale de l'application (par ex., `confluence_secret_id` dans un `config.yml`).
-   **`"perimeter"`** : Le chargeur recherche un secret spécifique au périmètre de la source. Le nom du secret doit suivre le format `{perimeter_code}-confluence-credentials` (ex: `sandbox-confluence-credentials`). S'il n'est pas trouvé, il se rabat sur le mode `global`.

### Structure du Secret

Le secret (qu'il soit global ou par périmètre) doit être un JSON avec la structure suivante :

```json
{
  "confluence_url": "https://votre-instance.atlassian.net",
  "cloud": true,
  "username": "<EMAIL>",
  "api_token": "VOTRE_JETON_API",
  "pat_token": null
}
```

**Champs importants :**

-   `confluence_url` (obligatoire) : L'URL de base de l'instance Confluence.
-   `cloud` (obligatoire) : `true` pour Confluence Cloud, `false` pour Server/Data Center.
-   **Méthode 1 : Jeton API**
    -   `username` : L'email de l'utilisateur.
    -   `api_token` : Le jeton API généré depuis les paramètres du compte Atlassian.
-   **Méthode 2 : PAT (Personal Access Token)**
    -   `pat_token` : Le jeton d'accès personnel. S'il est fourni, il a **priorité** sur la méthode `username`/`api_token`.

## 7. Gestion des Erreurs et Résilience

Le chargeur est conçu pour être robuste face aux problèmes temporaires.

-   **Nouvelles Tentatives (Retry)** : Les appels API critiques sont automatiquement réessayés en cas d'échec (configurable via `retry_attempts` et `retry_delay_seconds`).
-   **Disjoncteur (Circuit Breaker)** : Si un grand nombre d'appels échouent consécutivement, le disjoncteur s'ouvre. Il interrompt les appels API pendant une période définie (`circuit_breaker_timeout_seconds`) pour éviter de surcharger un service instable et pour échouer rapidement.

## 8. Mode Mock pour le Développement

Pour faciliter le développement et les tests sans dépendre d'une instance Confluence réelle, un mode "mock" est disponible. Il peut être activé via des variables d'environnement (`SKIP_EXTERNAL_AUTH=true` ou `MOCKING_ENABLED=true`).

Lorsque ce mode est actif, le `ConfluenceLoader` ne contacte pas l'API. À la place, il appelle des fonctions de simulation (`generate_mock_confluence_documents`) qui retournent une liste de `DocumentBean`s et du contenu de test.

## 9. Exemples d'Utilisation via API

Cette section illustre comment interagir avec le service du chargeur via des appels API HTTP, en montrant les deux étapes principales : lister les documents et télécharger un document spécifique.

### 9.1 Lister les documents d'une source (`/loader/list`)

Cette étape lance le processus de découverte. Le service reçoit les informations sur la source à traiter, et le chargeur Confluence est invoqué pour scanner les espaces configurés et générer la liste des `DocumentBean`.

#### Commande `curl`

```bash
❯ curl -sX 'POST' \
  'http://127.0.0.1:8092/loader/list/sandbox' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "get_list_file":"gs://ofr-ekb-knowledgebot-work-dev-mktsearch-dev/test/202506211610/ConfluenceProduction14542/CiblesRurales/getlist.json"
}'
```

#### Fichier d'entrée (`getlist.json`)

Le fichier `get_list_file` contient l'objet `SourceBean` qui décrit la source à traiter. Le champ `configuration` est une chaîne de caractères JSON contenant les paramètres spécifiques au chargeur Confluence.

```json
{
    "id": 1079611284,
    "code": "testconfluence27284",
    "label": "test confluence",
    "src_type": "confluence",
    "perimeter_code": "mktsearch",
    "configuration": "{\"sourceCurrentLabel\":\"test confluence\",\"sourceType\":\"confluence\",\"confluence_url\":\"https://espace.agir.orange.com/\",\"space_key\":\"VODCASTV\",\"labels\":\"ravenne, rag\",\"child_page_depth\":\"15\",\"include_attachments\":\"true\"}",
    "last_load_time": 1750069757,
    "next_load_time": 1750156157,
    "load_interval": 24,
    "force_embedding": false,
    "domain_id": 1,
    "domain_code": "CiblesRurales"
}
```
*Note : Le chargeur va parser la chaîne `configuration` pour extraire des paramètres comme `space_key: "VODCASTV"` et `labels: "ravenne, rag"`.*

#### Fichier de sortie (Exemple)

Le service retourne un JSON contenant la liste des `DocumentBean` découverts. Chaque objet représente un "bon de travail" pour l'étape de téléchargement.

```json
{
  "documents": [
    {
      "id": "CiblesRurales|testconfluence27284|1994899263",
      "path": "https://espace.agir.orange.com/display/VODCASTV/Accueil+Vodcast",
      "name": "Accueil Vodcast",
      "modification_time": "20250627101530"
    },
    {
      "id": "CiblesRurales|testconfluence27284|att2006357553",
      "path": "https://espace.agir.orange.com/download/attachments/1994899263/image-2024-11-18_11-53-14.png",
      "name": "image-2024-11-18_11-53-14.png",
      "modification_time": "20250628122104"
    },
    {
      "id": "CiblesRurales|testconfluence27284|drawio_Diagramme_Flux_att2006357554",
      "path": "https://espace.agir.orange.com/display/VODCASTV/Flux+de+donn%C3%A9es",
      "name": "Diagramme de Flux.drawio",
      "modification_time": "20250628140500"
    }
  ]
}
```

### 9.2 Télécharger un document spécifique (`/loader/document`)

Cette étape prend en entrée un `DocumentBean` (provenant de l'étape précédente) et demande au chargeur de télécharger son contenu.

#### Commande `curl`

```bash
curl -X 'POST' \
  'http://127.0.0.1:8092/loader/document/mktsearch' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "document_get_file": "gs://ofr-ekb-knowledgebot-work-dev-mktsearch-dev/test/202506211610/CiblesRurales/testconfluence27284/getdoc/test_png.getdoc.json"
}'
```

#### Fichier d'entrée (`test_png.getdoc.json`)

Le fichier `document_get_file` contient la `SourceBean` complète (pour la configuration et l'authentification) ainsi que le `DocumentBean` spécifique à télécharger.

```json
{
    "source": {
      "id": 1079611284,
      "code": "testconfluence27284",
      "label": "test confluence",
      "src_type": "confluence",
      "perimeter_code": "mktsearch",
      "configuration": "{\"sourceCurrentLabel\":\"test confluence\",\"sourceType\":\"confluence\",\"confluence_url\":\"https://espace.agir.orange.com/\",\"space_key\":\"VODCASTV\",\"labels\":\"ravenne, rag\",\"child_page_depth\":\"15\",\"include_attachments\":\"true\"}",
      "last_load_time": 1750069757,
      "next_load_time": 1750156157,
      "load_interval": 24,
      "force_embedding": false,
      "domain_id": 1,
      "domain_code": "CiblesRurales"
    },
    "document": {
      "id": "CiblesRurales|testconfluence27284|att2006357553",
      "path": "https://espace.agir.orange.com/download/attachments/1994899263/image-2024-11-18_11-53-14.png?version=1&modificationDate=1731927195778&api=v2",
      "name": "image-2024-11-18_11-53-14.png",
      "modification_time": "20250628122104"
    },
    "output_path": "gs://ofr-ekb-knowledgebot-work-dev-mktsearch-dev/test/202506211610/CiblesRurales/testconfluence27284/doc"
}
```

#### Fichier de sortie (Exemple)

Le service retourne les métadonnées enrichies du document qui vient d'être téléchargé. Le contenu binaire du fichier a été sauvegardé à l'emplacement spécifié par le champ `location`.

```json
{
  "document": {
    "id": "CiblesRurales|testconfluence27284|att2006357553",
    "path": "https://espace.agir.orange.com/download/attachments/1994899263/image-2024-11-18_11-53-14.png?version=1&modificationDate=1731927195778&api=v2",
    "name": "image-2024-11-18_11-53-14.png",
    "modification_time": "20250628122104"
  },
  "metadata": {
    "domain": "CiblesRurales",
    "source": "testconfluence27284",
    "source_type": "confluence",
    "source_conf": "{\"sourceCurrentLabel\":\"test confluence\",\"sourceType\":\"confluence\",\"confluence_url\":\"https://espace.agir.orange.com/\",\"space_key\":\"VODCASTV\",\"labels\":\"ravenne, rag\",\"child_page_depth\":\"15\",\"include_attachments\":\"true\"}",
    "document_id": "CiblesRurales|testconfluence27284|att2006357553",
    "document_name": "image-2024-11-18_11-53-14.png",
    "source_path": "https://espace.agir.orange.com/download/attachments/1994899263/image-2024-11-18_11-53-14.png?version=1&modificationDate=1731927195778&api=v2",
    "modificationDate": "2025-06-28T12:21:04",
    "location": "gs://ofr-ekb-knowledgebot-work-dev-mktsearch-dev/test/202506211610/CiblesRurales/testconfluence27284/docs/image-2024-11-18_11-53-14_att2006357553.png",
    "file_size": 166505,
    "export_format": "binary",
    "original_filename": "image-2024-11-18_11-53-14.png",
    "safe_filename": "image-2024-11-18_11-53-14_att2006357553.png",
    "parent_page_url": "https://espace.agir.orange.com/pages/viewpage.action?pageId=1994899263",
    "parent_page_id": "1994899263"
  }
}
```