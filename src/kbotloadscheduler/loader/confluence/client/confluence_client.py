import logging
import os
import re
import urllib.parse
from contextlib import contextmanager
from typing import Optional, List, Dict

import requests
from atlassian import Confluence

from kbotloadscheduler.exceptions import (
    ConfluenceClientException,
    ConfluenceAuthenticationError, # noqa: F401
    ConfluenceNotFoundError, # noqa: F401
    ConfluenceTimeoutError,
)
from ..config.confluence_config import ConfluenceConfig
from .confluence_credentials import ConfluenceCredentials

logger = logging.getLogger(__name__)

# Pre-compiled regex patterns for better performance
_DRAWIO_MACRO_PATTERN = re.compile(
    r'<ac:structured-macro[^>]+ac:name="drawio"[\s\S]*?</ac:structured-macro>',
    re.IGNORECASE
)
_FILENAME_PARAM_PATTERN = re.compile(
    r'<ac:parameter ac:name="filename">(.*?)</ac:parameter>',
    re.IGNORECASE
)
_DIAGRAM_NAME_PARAM_PATTERN = re.compile(
    r'<ac:parameter ac:name="diagramName">(.*?)</ac:parameter>',
    re.IGNORECASE
)


class ConfluenceClient:
    """
    Robust client for interacting with the Confluence API, with semantic error handling and proper resource management.
    """

    def __init__(self, credentials: ConfluenceCredentials, config: ConfluenceConfig):
        """
        Initializes the client with validated credentials and configuration.

        Args:
            credentials: An immutable ConfluenceCredentials object.
            config: A ConfluenceConfig object defining loader behavior.
        """
        if not isinstance(credentials, ConfluenceCredentials) or not credentials.is_valid():
            raise ConfluenceAuthenticationError("A valid ConfluenceCredentials object is required.")

        self.credentials = credentials
        self.config = config

        self.url = self.credentials.url.rstrip("/")
        self.verify_ssl = True # Or get from config if needed
        self.timeout = self.config.file_processing.default_timeout
        self.attachment_filter_mode = self.config.attachments.attachment_filter_mode
        self._session: Optional[requests.Session] = None

        # Add simple cache for attachment metadata to avoid repeated API calls
        self._attachment_cache: Dict[str, Dict] = {}

        self._initialize_confluence_client()
        self._configure_session()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def close(self):
        if self._session:
            self._session.close()
            self._session = None
            logger.debug("Custom requests session closed.")
        # noinspection PyProtectedMember
        if hasattr(self.confluence, '_session') and self.confluence._session:
            # noinspection PyProtectedMember
            self.confluence._session.close()
            logger.debug("Atlassian library session closed.")

        # Clear cache when closing
        self._attachment_cache.clear()

    @contextmanager
    def _safe_request_context(self, method: str, url: str, **kwargs):
        response = None
        try:
            kwargs.setdefault('timeout', self.timeout)
            response = self._make_authenticated_request(method, url, **kwargs)
            response.raise_for_status()
            yield response
        except requests.exceptions.HTTPError as e:
            raise ConfluenceClientException.from_http_error(e.response, message=f"Erreur API Confluence pour {url}",
                                                            original_exception=e) from e
        except requests.exceptions.Timeout as e:
            raise ConfluenceTimeoutError(f"La requête vers {url} a expiré", original_exception=e, resource=url) from e
        except requests.exceptions.RequestException as e:
            raise ConfluenceClientException(f"Erreur réseau durant la requête vers {url}: {e}", original_exception=e,
                                            resource=url, is_retryable=True) from e
        finally:
            if response:
                response.close()

    def _initialize_confluence_client(self):
        """Initializes the underlying atlassian-python-api client."""
        # This logic is now much cleaner, directly using properties from the credentials object.
        if self.credentials.pat_token:
            self.auth_method = "bearer"
            self.confluence = Confluence(
                url=self.url,
                token=self.credentials.pat_token,
                cloud=self.credentials.cloud,
                timeout=self.timeout,
                verify_ssl=self.verify_ssl
            )
        elif self.credentials.username and self.credentials.api_token:
            self.auth_method = "basic"
            self.confluence = Confluence(
                url=self.url,
                username=self.credentials.username,
                password=self.credentials.api_token,
                timeout=self.timeout,
                verify_ssl=self.verify_ssl
            )
        else:
            # This case should be caught by credentials.is_valid() in __init__, but it's good defense.
            raise ConfluenceAuthenticationError("No valid authentication method (PAT or user/token) found in credentials.")

    def _configure_session(self):
        """Configures a custom requests.Session for direct API calls."""
        if self._session is None:
            self._session = requests.Session()
        self._session.verify = self.verify_ssl
        headers = {
            "Accept": "application/json",
            "User-Agent": "Mozilla/5.0 (compatible; KbotConfluenceClient/1.2)"
        }
        # Use the get_auth_headers method from the credentials object for clean encapsulation
        headers.update(self.credentials.get_auth_headers())
        self._session.headers.update(headers)

    def _is_valid_attachment(self, attachment: dict) -> bool:
        from ..config.confluence_helpers import is_temp_file
        title = attachment.get("title", "")
        if self.config and is_temp_file(self.config, title):
            return False
        if title.startswith((".", "~")):
            return False
        return True

    def _filter_current_attachments(self, all_attachments: List[dict]) -> List[dict]:
        return [att for att in all_attachments if att.get("status") == "current" and self._is_valid_attachment(att)]

    def _extract_drawio_references(self, content: str) -> set[str]:
        """
        Extract Draw.io diagram references from content.

        Args:
            content: HTML content to search for Draw.io references

        Returns:
            Set of referenced diagram names (lowercased)
        """
        drawio_referenced_names = set()
        for macro_match in _DRAWIO_MACRO_PATTERN.finditer(content):
            macro_xml = macro_match.group(0)

            filename_match = _FILENAME_PARAM_PATTERN.search(macro_xml)
            if filename_match:
                drawio_referenced_names.add(filename_match.group(1).lower())

            diagram_name_match = _DIAGRAM_NAME_PARAM_PATTERN.search(macro_xml)
            if diagram_name_match:
                drawio_referenced_names.add(diagram_name_match.group(1).lower())

        return drawio_referenced_names

    def _is_attachment_referenced(self, attachment: dict, content: str, drawio_refs: set[str]) -> bool:
        """
        Check if attachment is referenced in content.

        Args:
            attachment: Attachment metadata dict
            content: HTML content (already lowercased)
            drawio_refs: Set of Draw.io diagram references (lowercased)

        Returns:
            True if attachment is referenced in content
        """
        att_id = attachment.get('id', '')
        att_title = attachment.get('title', '')
        att_title_lower = att_title.lower()

        # Optimized: Use more efficient search patterns
        # Check direct ID references first (most specific)
        if att_id and (att_id in content or f"att{att_id}" in content):
            return True

        # Check title references
        if att_title_lower and att_title_lower in content:
            return True

        # Check URL-encoded title (only if needed)
        try:
            encoded_title = urllib.parse.quote(att_title).lower()
            if encoded_title != att_title_lower and encoded_title in content:
                return True
        except Exception:
            # Skip encoding errors
            pass

        # Check if referenced in Draw.io macros by full title or name without extension
        if (att_title_lower in drawio_refs or
            os.path.splitext(att_title)[0].lower() in drawio_refs):
            return True

        return False

    def _filter_attachments_used_in_content(self, attachments: List[dict], page_content_html: str) -> List[dict]:
        """
        Filter attachments based on whether they are referenced in the page content.

        Args:
            attachments: List of attachment metadata dicts
            page_content_html: HTML content of the page

        Returns:
            List of attachments that are referenced in the content
        """
        if not page_content_html or not attachments:
            return attachments

        # Optimize: Process content once and reuse
        content_lower = page_content_html.lower()
        drawio_refs = self._extract_drawio_references(page_content_html)

        # Use list comprehension for better performance
        return [
            att for att in attachments
            if self._is_attachment_referenced(att, content_lower, drawio_refs)
        ]

    # --- ATTACHMENT RETRIEVAL LOGIC BLOCK ---

    def get_page_attachments(self, page_id: str, start: int = 0, limit: int = 50) -> list[dict]:
        """This method is an alias for the more complex filtering logic."""
        return self.get_page_attachments_filtered(page_id, self.attachment_filter_mode, start, limit)

    def get_page_attachments_all_current(self, page_id: str, start: int = 0, limit: int = 50) -> list[dict]:
        """Retrieve all valid attachments via a dedicated API call."""
        try:
            # Original behavior: direct API call for attachments.
            response = self.confluence.get_attachments_from_content(page_id=page_id, start=start, limit=limit)
            return self._filter_current_attachments(response.get("results", []))
        except Exception as e:
            raise ConfluenceClientException(f"Failed to retrieve attachments for page {page_id}",
                                            original_exception=e, resource=page_id) from e

    def get_page_attachments_used_in_content(self, page_id: str, start: int = 0, limit: int = 50) -> list[dict]:
        """Filter attachments based on the page content."""
        try:
            current_attachments = self.get_page_attachments_all_current(page_id, start, limit)
            if not current_attachments:
                return []

            # API call for page content, only if needed for filtering.
            page_data = self.get_page_content(page_id, expand="body.storage,children.attachment")
            content_html = page_data.get('body', {}).get('storage', {}).get('value', '')

            # Performance optimization: For very large content, skip complex filtering
            # and return all attachments to avoid memory/CPU issues
            max_content_size = getattr(self.config, 'max_content_size_for_filtering', 1024 * 1024)  # 1MB default
            if len(content_html.encode('utf-8')) > max_content_size:
                logger.warning(f"Page {page_id} content too large ({len(content_html)} chars), "
                             f"returning all attachments without filtering")
                return current_attachments

            return self._filter_attachments_used_in_content(current_attachments, content_html)
        except ConfluenceClientException as e:
            # Do not hide the error if get_page_content fails
            logger.error(
                f"Failed to filter attachments for page {page_id} due to an underlying error: {e}")
            raise

    def get_page_attachments_filtered(self, page_id: str, filter_mode: str, start: int = 0, limit: int = 50) -> list[dict]:
        """Dispatch to the correct filtering method, as in the original."""
        logger.debug(f"Using filter mode '{filter_mode}' for page {page_id}.")
        if filter_mode == "content_used":
            return self.get_page_attachments_used_in_content(page_id, start, limit)
        if filter_mode == "all_current":
            return self.get_page_attachments_all_current(page_id, start, limit)

        logger.warning(f"Unknown filter mode '{filter_mode}', using 'content_used' by default.")
        return self.get_page_attachments_used_in_content(page_id, start, limit)

    # --- END OF ATTACHMENT RETRIEVAL LOGIC BLOCK ---

    def get_attachment_by_id(self, attachment_id: str) -> Dict:
        """
        Get attachment metadata by ID with caching for better performance.

        Args:
            attachment_id: The attachment ID

        Returns:
            Dict: Attachment metadata
        """
        # Check cache first
        if attachment_id in self._attachment_cache:
            return self._attachment_cache[attachment_id]

        # NOTE: The base atlassian-python-api get_attachment_by_id uses a different endpoint.
        # This custom implementation ensures we get the necessary '_links' field.
        api_url = f"{self.url}/rest/api/content/{attachment_id}"
        with self._safe_request_context('GET', api_url) as response:
            attachment_data = response.json()
            # Cache the result
            self._attachment_cache[attachment_id] = attachment_data
            return attachment_data

    def get_attachment_content(self, attachment_id: str) -> bytes:
        """
        Downloads the raw content of a specific attachment.
        This method is essential for fetching the source XML of Draw.io diagrams.
        """
        try:
            # Optimized: Get download URL directly from API without separate metadata call
            api_url = f"{self.url}/rest/api/content/{attachment_id}"
            with self._safe_request_context('GET', api_url) as response:
                attachment_data = response.json()
                download_link = attachment_data.get('_links', {}).get('download')

                if not download_link:
                    raise ConfluenceClientException(
                        f"No download link found for attachment {attachment_id}",
                        resource=attachment_id
                    )

                download_url = self.url + download_link if download_link.startswith('/') else download_link

            # Use the robust context manager to handle the download request
            with self._safe_request_context('GET', download_url) as response:
                return response.content  # Return the raw bytes

        except ConfluenceNotFoundError:
            raise  # Re-raise not found errors
        except ConfluenceClientException as e:
            logger.error(f"Confluence error while retrieving content for {attachment_id}: {e}")
            raise # Re-raise other client errors
        except Exception as e:
            logger.error(f"Unexpected error while retrieving content for {attachment_id}: {e}")
            raise ConfluenceClientException(
                f"Unexpected error while retrieving content for {attachment_id}",
                original_exception=e, resource=attachment_id
            ) from e

    def get_page_content(self, page_id: str,
                         expand: str = "body.storage,version,ancestors,children.attachment") -> Dict:
        try:
            page = self.confluence.get_page_by_id(page_id, expand=expand)
            if page is None:
                raise ConfluenceNotFoundError(f"Confluence page with ID '{page_id}' was not found.",
                                              resource=page_id)
            return page
        except Exception as e:
            raise ConfluenceClientException(f"Failed to retrieve page {page_id}", original_exception=e,
                                            resource=page_id) from e

    def search_content(self, cql: str, limit: int = 100, start: int = 0,
                       expand: str = "version,ancestors,children.attachment") -> List[dict]:
        """Execute a CQL search and return the results."""
        # 'children.attachment' is also added to expand by default here, which may be relevant.
        try:
            response = self.confluence.cql(cql=cql, start=start, limit=limit, expand=expand)
            return response.get("results", [])
        except Exception as e:
            raise ConfluenceClientException(f"CQL search failed: '{cql}'", original_exception=e) from e

    def _make_authenticated_request(self, method: str, url: str, **kwargs) -> requests.Response:
        if self._session is None:
            self._configure_session()
        return self._session.request(method, url, **kwargs)

    def convert_html_to_pdf(self, html_content: str) -> bytes:
        """
        Converts HTML content to PDF using the Confluence API.

        Note: This method requires a page_id rather than raw HTML content,
        as Confluence PDF export works with existing pages, not arbitrary HTML.
        """
        raise ConfluenceClientException(
            "convert_html_to_pdf is not supported. Use export_page_as_pdf(page_id) instead. "
            "Confluence PDF export requires an existing page ID, not raw HTML content.",
            operation="convert_html_to_pdf",
            is_critical=False,
            is_retryable=False
        )

    def export_page_as_pdf(self, page_id: str) -> bytes:
        """
        Export a Confluence page as PDF using the built-in FlyingPDF functionality.

        Args:
            page_id: The ID of the Confluence page to export

        Returns:
            The PDF content as bytes

        Raises:
            ConfluenceClientException: If the PDF export fails
        """
        try:
            logger.info(f"Exporting page {page_id} as PDF using FlyingPDF")

            # Use the built-in get_page_as_pdf method from atlassian library
            pdf_content = self.confluence.get_page_as_pdf(page_id)

            if pdf_content is None:
                raise ConfluenceClientException(
                    f"PDF export returned None for page {page_id}",
                    resource=page_id,
                    operation="export_page_as_pdf"
                )

            logger.info(f"PDF export successful for page {page_id}")
            return pdf_content

        except Exception as e:
            if isinstance(e, ConfluenceClientException):
                raise
            logger.error(f"Failed to export page {page_id} as PDF: {e}", exc_info=True)
            raise ConfluenceClientException(
                f"Failed to export page {page_id} as PDF: {e}",
                original_exception=e,
                resource=page_id,
                operation="export_page_as_pdf"
            ) from e