"""
Confluence Credentials Management

This module manages authentication credentials for the Confluence API.
"""

import base64
import os
from dataclasses import dataclass


@dataclass(frozen=True)
class ConfluenceCredentials:
    """
    Credentials for Confluence API authentication.

    This data class stores the information required to connect to a Confluence instance, whether Cloud or Server/Data Center.

    Attributes:
        url (str): The URL of the Confluence instance.
        username (Optional[str]): The username (usually an email address for Confluence Cloud).
        api_token (Optional[str]): The API token to use with the username.
        pat_token (Optional[str]): The Personal Access Token (PAT). Takes precedence over username/API token authentication.
        cloud (bool): True for Confluence Cloud, False for Confluence Server/Data Center.
    """

    url: str
    username: str | None = None
    api_token: str | None = None
    pat_token: str | None = None
    cloud: bool = True  # True for Confluence Cloud, False for Server

    @classmethod
    def from_env(cls) -> "ConfluenceCredentials":
        """
        Create a credentials object from environment variables.

        Expected environment variables:
        - CONFLUENCE_URL
        - CONFLUENCE_USERNAME
        - CONFLUENCE_API_TOKEN
        - CONFLUENCE_PAT_TOKEN
        - CONFLUENCE_CLOUD: The value "true" (case-insensitive) is interpreted as True.
          Any other value or absence of the variable is interpreted as False, unless the default value is used (which is True by default here).

        Returns:
            ConfluenceCredentials: A new instance of `ConfluenceCredentials`.
        """
        return cls(
            url=os.getenv("CONFLUENCE_URL", ""),
            username=os.getenv("CONFLUENCE_USERNAME", ""),
            api_token=os.getenv("CONFLUENCE_API_TOKEN", ""),
            pat_token=os.getenv("CONFLUENCE_PAT_TOKEN", ""),
            cloud=os.getenv("CONFLUENCE_CLOUD", "true").lower() == "true",
        )

    @classmethod
    def from_secret_dict(cls, secret_dict: dict) -> "ConfluenceCredentials":
        """
        Create a credentials object from a dictionary, typically from a secret manager.
        The dictionary is expected to contain all necessary fields.

        Args:
            secret_dict (dict): A dictionary containing 'confluence_url' and authentication secrets.

        Returns:
            ConfluenceCredentials: A new instance of `ConfluenceCredentials`.
        """
        if not secret_dict.get("confluence_url"):
            raise ValueError("The 'confluence_url' key is missing from the secret dictionary.")

        # Check for the 'cloud' key specifically.
        cloud_status = secret_dict.get("cloud")
        if not isinstance(cloud_status, bool): # Checks for None and wrong types
            raise ValueError("The 'cloud' key is mandatory and must be a boolean (true/false) in the secret dictionary.")

        if cloud_status is None: # Use 'is None' to correctly handle a value of False
            raise ValueError("The 'cloud' key (true/false) is a mandatory field and is missing from the secret dictionary.")

        return cls(
            # The URL now comes directly from the secret, making it self-contained
            url=secret_dict.get("confluence_url"),
            username=secret_dict.get("username"),
            api_token=secret_dict.get("api_token"),
            pat_token=secret_dict.get("pat_token"),
            # Default to False (on-premise) if the key is not in the secret
            cloud=cloud_status
        )

    def is_valid(self) -> bool:
        """
        Check if the credentials are sufficient for authentication.

        Credentials are considered valid if the URL is present and at least one authentication method is complete (either PAT token or username/API token pair).

        Returns:
            bool: True if the credentials are valid, otherwise False.
        """
        has_url = bool(self.url)
        has_pat = bool(self.pat_token)
        has_user_api = bool(self.username and self.api_token)

        return has_url and (has_pat or has_user_api)

    def get_auth_headers(self) -> dict:
        """
        Generate authentication headers for API requests.

        Gives priority to the PAT token (Bearer) if available, otherwise uses Basic authentication with username and API token.

        Returns:
            dict: A dictionary containing the `Authorization` header, or an empty dictionary if no authentication information is available.
        """
        if self.pat_token:
            return {"Authorization": f"Bearer {self.pat_token}"}
        elif self.username and self.api_token:
            credentials = f"{self.username}:{self.api_token}"
            encoded_credentials = base64.b64encode(credentials.encode()).decode()
            return {"Authorization": f"Basic {encoded_credentials}"}
        else:
            return {}

    def __repr__(self) -> str:
        """
        Return a string representation of the object, masking sensitive information.

        This is important to avoid leaking tokens in logs or console output.

        Returns:
            str: A string representing the `ConfluenceCredentials` object.
        """
        masked_pat_token = "***" if self.pat_token else None
        masked_api_token = "***" if self.api_token else None

        return (
            f"ConfluenceCredentials("
            f"url='{self.url}', "
            f"username={self.username!r}, "
            f"api_token={masked_api_token!r}, "
            f"pat_token={masked_pat_token!r}, "
            f"cloud={self.cloud})"
        )
