"""
Orchestrates the discovery of content in a single Confluence space,
creating DocumentBeans that serve as "work orders" for the ContentDownloader.
"""
import logging
import os
import re
from typing import Dict, List, Optional, Any

from kbotloadscheduler.bean.beans import DocumentBean, SourceBean
from ..config.confluence_config import ConfluenceConfig, DEFAULT_ATTACHMENT_LIMIT
from ..processors import bean_factory
from ..processors.drawio_detector import DrawioDetector
from ..search.cql_search import CqlSearch

logger = logging.getLogger(__name__)


class SpaceProcessor:
    """
    Discovers and creates DocumentBeans for all content in a Confluence space.

    This processor focuses solely on content discovery and DocumentBean creation.
    It finds pages, attachments, and Draw.io diagrams, then creates DocumentBeans
    that serve as "work orders" for the ContentDownloader.

    Responsibilities:
    - Search for pages in a space (including child pages recursively)
    - Detect Draw.io diagrams using DrawioDetector
    - Create DocumentBeans for all discovered content via bean_factory
    - Apply configuration filters (extensions, depth limits, etc.)

    NOT responsible for:
    - Content processing or transformation
    - Markdown generation
    - File download or storage
    """

    # Precompiled regex for Draw.io PNG export filenames (e.g., "DiagramName-1234567890123.png")
    DRAWIO_PNG_EXPORT_REGEX = re.compile(r'-\d{10,13}\.png$', re.IGNORECASE)

    def __init__(self, client, cql_search: CqlSearch, base_url: str):
        """
        Initialize the space processor for content discovery.

        Args:
            client: Confluence client for API operations (used for attachment retrieval).
            cql_search: Search manager for finding content in Confluence.
            base_url: Base URL of the Confluence instance for building document URLs.
        """
        self.client = client
        self.cql_search = cql_search
        self.base_url = base_url

        # Initialize only the Draw.io detector for discovery
        self.drawio_detector = DrawioDetector()

    def process(self, space_key: str, source: SourceBean, config: ConfluenceConfig) -> List[DocumentBean]:
        """
        Discover all content in a Confluence space and create DocumentBeans.

        This method focuses on content discovery only. Each DocumentBean created
        acts as a "work order" that describes what content needs to be processed
        by the ContentDownloader.

        Args:
            space_key: The Confluence space key to process
            source: SourceBean for document ID generation
            config: Configuration object with filtering rules

        Returns:
            List of DocumentBeans representing all discovered content
        """
        logging.info(f"Start processing space: {space_key}")

        # Validate configuration
        self._validate_config(config)

        # Use memory-efficient processing if configured
        if getattr(config, 'use_memory_efficient_processing', False):
            logger.info(f"Using memory-efficient processing for space '{space_key}'")
            all_documents = list(self.process_streaming(space_key, source, config))
        else:
            # 1. Search for top-level pages
            root_pages = self.cql_search.search_pages_in_space(space_key, config)

            all_documents = []
            for page_data in root_pages:
                # 2. Recursively process each page and its content
                all_documents.extend(
                    self._process_page_recursively(page_data, source, config, current_depth=0)
                )

        logger.info(f"Space {space_key} processed. {len(all_documents)} documents found in total.")
        return all_documents

    def process_streaming(self, space_key: str, source: SourceBean, config: ConfluenceConfig):
        """
        Generator version that processes a space and yields documents one by one.
        Maintains constant memory usage regardless of space size.

        Args:
            space_key: The Confluence space key to process
            source: The SourceBean object for the current source
            config: The Confluence configuration

        Yields:
            DocumentBean: Each processed document individually
        """
        logging.info(f"Start streaming processing of space: {space_key}")

        # Validate configuration
        self._validate_config(config)

        # 1. Search for top-level pages
        root_pages = self.cql_search.search_pages_in_space(space_key, config)

        documents_count = 0
        for page_data in root_pages:
            # 2. Recursively process each page and its content with yield
            for document in self._process_page_recursively_streaming(page_data, source, config, current_depth=0):
                documents_count += 1
                yield document

        logger.info(f"Space {space_key} processed in streaming mode. {documents_count} documents processed in total.")

    def _validate_config(self, config: ConfluenceConfig) -> None:
        """
        Validates the configuration before processing.

        Args:
            config: Configuration to validate

        Raises:
            ValueError: If the configuration is invalid
        """
        if not isinstance(config.basic.child_page_depth, int) or config.basic.child_page_depth < 0:
            raise ValueError("child_page_depth must be >= 0")

        if config.basic.child_page_depth > 50:
            raise ValueError("child_page_depth must be <= 50")

        if config.attachments.file_extensions:
            for ext in config.attachments.file_extensions:
                if not isinstance(ext, str):
                    raise ValueError(f"Invalid file extension type: {type(ext)}")
                if not ext.strip():
                    raise ValueError("File extension cannot be empty")

        # Validate filename handling settings
        if hasattr(config.file_processing, 'max_filename_length') and config.file_processing.max_filename_length < 20:
            raise ValueError("max_filename_length must be at least 20 characters")

        if hasattr(config.file_processing, 'duplicate_filename_strategy'):
            valid_strategies = ["append_id", "append_counter", "overwrite"]
            if config.file_processing.duplicate_filename_strategy not in valid_strategies:
                raise ValueError(f"duplicate_filename_strategy must be one of {valid_strategies}")

    def _process_page_recursively(self, page_data: Dict[str, Any], source: SourceBean,
                                  config: ConfluenceConfig, current_depth: int) -> List[DocumentBean]:
        """
        Processes a page, its attachments, and its children recursively,
        applying all configured filters.

        Args:
            page_data: The data dictionary for the current page.
            source: The SourceBean object for the current source.
            config: The configuration object with filtering rules.
            current_depth: The current depth in the recursion tree.

        Returns:
            A list of DocumentBean objects for the page, its valid attachments,
            and its entire descendant tree.
        """
        return list(self._process_page_recursively_streaming(page_data, source, config, current_depth))

    def _process_page_recursively_streaming(self, page_data: Dict[str, Any], source: SourceBean,
                                          config: ConfluenceConfig, current_depth: int):
        """
        Generator version that processes a page, its attachments, and its children recursively,
        applying all configured filters and yielding documents one by one.

        Args:
            page_data: The data dictionary for the current page.
            source: The SourceBean object for the current source.
            config: The configuration object with filtering rules.
            current_depth: The current depth in the recursion tree.

        Yields:
            DocumentBean: Each document found individually
        """
        page_id = page_data.get("id")
        if not page_id:
            logger.warning(f"Unable to process a page without ID. Data: {page_data}")
            return

    # 1. Create the bean for the current page
        page_bean = self._create_page_bean(page_data, source)
        if not page_bean:
            # If page creation fails, do not process its attachments or children.
            return

        yield page_bean

    # 2. Process content associated with the page (yield documents one by one)
    # Draw.io diagrams
        for document in self._process_drawio_diagrams_streaming(page_data, source, config):
            yield document

    # Attachments
        for document in self._process_page_attachments_streaming(page_data, source, config):
            yield document

    # Child pages (recursive)
        for document in self._process_child_pages_streaming(page_data, source, config, current_depth):
            yield document

    def _create_page_bean(self, page_data: Dict[str, Any], source: SourceBean) -> Optional[DocumentBean]:
        """
        Creates the DocumentBean for a page.

        Args:
            page_data: Page data
            source: Source bean

        Returns:
            Created DocumentBean or None in case of error
        """
        page_id = page_data.get("id")
        try:
            return bean_factory.create_page_bean(page_data, source, self.base_url)
        except Exception as e:
            logger.error(f"Error while creating bean for page {page_id}: {e}")
            return None

    def _process_page_drawio_diagrams(self, page_data: Dict[str, Any], source: SourceBean,
                                      config: ConfluenceConfig) -> List[DocumentBean]:
        """
        Processes Draw.io diagrams of a page.

        Args:
            page_data: Page data
            source: Source bean
            config: Configuration

        Returns:
            List of DocumentBeans for Draw.io diagrams
        """
        return list(self._process_drawio_diagrams_streaming(page_data, source, config))

    def _process_page_attachments(self, page_data: Dict[str, Any], source: SourceBean,
                                  config: ConfluenceConfig) -> List[DocumentBean]:
        """
        Processes the attachments of a page.

        Args:
            page_data: Page data
            source: Source bean
            config: Configuration

        Return
            List of DocumentBeans for attachments
        """
        return list(self._process_page_attachments_streaming(page_data, source, config))

    def _create_attachment_bean(self, att_data: Dict[str, Any], source: SourceBean,
                                page_id: str, config: ConfluenceConfig) -> Optional[DocumentBean]:
        """
        Creates a DocumentBean for an attachment.

        Args:
            att_data: Attachment data
            source: Source bean
            page_id: Parent page ID
            config: Configuration

        Returns:
            Created DocumentBean or None if filtered/error
        """
        attachment_title = att_data.get('title', '')        # Check if this is a Draw.io PNG export and if it should be excluded
        if self._should_exclude_drawio_png_export(attachment_title, config):
            logger.info(f"📎➡️🎨 Draw.io PNG attachment ignored (redundant with Draw.io document): {attachment_title}")
            return None

        # Apply file extension filter
        if not self._should_include_attachment(attachment_title, config.attachments.file_extensions):
            logger.debug(
                f"Attachment '{attachment_title}' (page {page_id}) ignored because its extension is not in the allowed list.")
            return None

        try:
            return bean_factory.create_attachment_bean(att_data, source, page_id, self.base_url)
        except Exception as e:
            logger.error(
                f"Error while creating bean for attachment '{attachment_title}' (page {page_id}): {e}")
            return None

    def _process_child_pages(self, page_data: Dict[str, Any], source: SourceBean,
                             config: ConfluenceConfig, current_depth: int) -> List[DocumentBean]:
        """
        Processes child pages recursively.

        Args:
            page_data: Parent page data
            source: Source bean
            config: Configuration
            current_depth: Current depth

        Returns:
            List of DocumentBeans for child pages
        """
        return list(self._process_child_pages_streaming(page_data, source, config, current_depth))

    def _should_include_attachment(self, filename: str, allowed_extensions: List[str]) -> bool:
        """
        Checks if an attachment should be included based on its file extension.

        Args:
            filename: The filename of the attachment.
            allowed_extensions: A list of allowed extensions (e.g., ['pdf', 'docx']).

        Returns:
            True if the attachment should be included, False otherwise.
        """
        # If the list of allowed extensions is empty, include nothing.
        if not allowed_extensions:
            return False

        if not filename:
            return False

    # Extract the extension and normalize (lowercase, without the dot)
        _, ext = os.path.splitext(filename.lower())
        file_ext = ext[1:]  # Retire le '.' initial

    # Also normalize the list of allowed extensions for comparison
        normalized_allowed_extensions = [e.lower().lstrip(".") for e in allowed_extensions]

        return file_ext in normalized_allowed_extensions

    def _should_exclude_drawio_png_export(self, filename: str, config: ConfluenceConfig) -> bool:
        """
        Checks if a PNG attachment should be excluded because it is a redundant Draw.io export.

        Args:
            filename: The filename of the attachment
            config: Configuration

        Returns:
            True if the attachment should be excluded (it's a Draw.io PNG and config forbids it), False otherwise
        """
        # If extract_drawio_as_documents is disabled, do not filter PNGs
        if not config.attachments.extract_drawio_as_documents:
            return False

    # If include_drawio_png_exports is enabled, do not filter Draw.io PNGs
        if config.attachments.include_drawio_png_exports:
            return False

    # Check if this is a PNG file with Draw.io indicators
        if not filename or not filename.lower().endswith('.png'):
            return False

    # Typical indicators of a Draw.io PNG export
        drawio_indicators = [
            'diagram',
            'drawio',
            'draw.io',
        # Confluence often generates names like "DiagramName-123456789.png"
        # where the number is a timestamp
        ]

        filename_lower = filename.lower()

    # Check explicit indicators
        for indicator in drawio_indicators:
            if indicator in filename_lower:
                return True

    # Check the typical Confluence timestamp pattern for Draw.io exports
    # Format: "DiagramName-1234567890123.png" (name followed by a dash and a long timestamp)

        if self.DRAWIO_PNG_EXPORT_REGEX.search(filename):
            return True

        return False

    def _should_include_drawio_diagram(self, diagram, config: ConfluenceConfig) -> bool:
        """
        Checks if a Draw.io diagram should be included according to the extension filter.

        For macro/inline (embedded) diagrams: checks if 'drawio' is in the extensions
        For attachment diagrams: checks the actual file extension

        Args:
            diagram: DrawioDiagram object
            config: Configuration

        Returns:
            True if the diagram should be included, False otherwise
        """
    # If file_extensions is empty, do not include
        if not config.attachments.file_extensions:
            return False

        # For inline diagrams (macros and embedded), always include if 'drawio' is in the allowed extensions
        if diagram.diagram_type in ["inline", "macro", "embedded"]:
            normalized_extensions = [ext.lower().lstrip(".") for ext in config.attachments.file_extensions]
            return "drawio" in normalized_extensions

        # For attachment diagrams, use the normal extension filter
        if diagram.title:
            return self._should_include_attachment(diagram.title, config.attachments.file_extensions)

        # If no title, consider as generic Draw.io
        normalized_extensions = [ext.lower().lstrip(".") for ext in config.attachments.file_extensions]
        return "drawio" in normalized_extensions

    def _get_page_content_safe(self, page_id: str) -> Optional[Dict[str, Any]]:
        """
        Safely retrieves the content of a page.

        Args:
            page_id: Page ID

        Returns:
            Page content data or None in case of error
        """
        try:
            return self.client.get_page_content(page_id, expand="body.storage")
        except Exception as e:
            logger.debug(f"Unable to retrieve page content {page_id} for Draw.io detection: {e}")
            return None

    def _get_attachments_for_drawio(self, page_id: str, config: ConfluenceConfig) -> List[Dict[str, Any]]:
        """
        Retrieves the attachments needed for Draw.io detection.

        Args:
            page_id: Page ID
            config: Configuration

        Returns:
            List of attachments
        """
        attachments = []
        if config.attachments.include_attachments:
            try:
                # 1. Retrieve normally filtered attachments (visible/used)
                filtered_attachments = self.client.get_page_attachments_filtered(
                    page_id=page_id,
                    filter_mode=config.attachments.attachment_filter_mode,
                    start=0,
                    limit=DEFAULT_ATTACHMENT_LIMIT
                )

                # 2. Retrieve all attachments to detect Draw.io without extension
                all_attachments = self.client.get_page_attachments_all_current(
                    page_id=page_id,
                    start=0,
                    limit=DEFAULT_ATTACHMENT_LIMIT
                )

                # 3. Intelligently merge: filtered + potential Draw.io
                attachments = self._merge_attachments_for_drawio(filtered_attachments, all_attachments, config)

            except Exception as e:
                logger.warning(f"Error retrieving attachments for page {page_id}: {e}")

        return attachments

    def _create_drawio_document_safe(self, diagram, page_data: Dict[str, Any], source: SourceBean) -> Optional[DocumentBean]:
        """
        Safely creates a DocumentBean for a detected Draw.io diagram.

        Args:
            diagram: Detected DrawioDiagram object
            page_data: Confluence page data containing the diagram
            source: Source bean for document creation

        Returns:
            DocumentBean for the Draw.io diagram or None in case of error
        """
        try:
            return self._create_drawio_document(diagram, page_data, source)
        except Exception as e:
            logger.error(f"Error creating document for diagram {diagram.diagram_id}: {e}")
            return None

    def _create_drawio_document(self, diagram, page_data: Dict[str, Any], source: SourceBean) -> Optional[DocumentBean]:
        """
        Creates a DocumentBean for a Draw.io diagram.
        Focus on discovery only - no content processing.

        Args:
            diagram: Detected DrawioDiagram object
            page_data: Confluence page data containing the diagram
            source: Source bean for document creation

        Returns:
            DocumentBean for the Draw.io diagram
        """
    # Prepare diagram data for the bean_factory
        diagram_data = {
            "diagram_id": diagram.diagram_id,
            "title": diagram.title,
            "diagram_type": diagram.diagram_type,
            "attachment_id": diagram.attachment_id
        }        # Create the DocumentBean via the bean_factory
        document = bean_factory.create_drawio_bean(diagram_data, source, page_data, self.base_url)

        logger.debug(f"Draw.io document created: {document.id} - {document.name}")
        return document

    def _merge_attachments_for_drawio(self, filtered_attachments: List[Dict[str, Any]],
                                      all_attachments: List[Dict[str, Any]], config: ConfluenceConfig) -> List[Dict[str, Any]]:
        """
        Intelligently merges filtered attachments with potential Draw.io attachments.

        Strategy:
        1. Include all filtered attachments (visible/used)
        2. Add potential Draw.io attachments not already included, respecting the extension filter

        Args:
            filtered_attachments: Attachments filtered by visibility/extension
            all_attachments: All attachments of the page
            config: Configuration containing extension filters

        Returns:
            Merged list of attachments
        """
        # Create a set of IDs for already filtered attachments
        filtered_ids = {att.get('id') for att in filtered_attachments if att.get('id')}

        # Start with filtered attachments
        merged_attachments = list(filtered_attachments)

        # Add potential Draw.io attachments not already included
        for attachment in all_attachments:
            attachment_id = attachment.get('id')
            attachment_title = attachment.get('title', '')

            # If already included in filtered ones, ignore
            if attachment_id in filtered_ids:
                continue

            # Check if it's a potential Draw.io attachment
            if self.drawio_detector._is_potential_drawio_file(attachment_title):
                # Apply the extension filter even for Draw.io
                if self._should_include_attachment(attachment_title, config.attachments.file_extensions):
                    merged_attachments.append(attachment)
                    logger.debug(f"Adding potential Draw.io attachment: {attachment_title}")
                else:
                    logger.debug(f"Potential Draw.io attachment ignored (extension not allowed): {attachment_title}")

        logger.debug(f"Attachment merge: {len(filtered_attachments)} filtered + "
                      f"{len(merged_attachments) - len(filtered_attachments)} Draw.io = "
                      f"{len(merged_attachments)} total")

        return merged_attachments

    # === STREAMING METHODS (Memory-efficient generators) ===

    def _process_drawio_diagrams_streaming(self, page_data: Dict[str, Any], source: SourceBean,
                                         config: ConfluenceConfig):
        """
        Generator version to process Draw.io diagrams present in a Confluence page.

        Args:
            page_data: Confluence page data
            source: Source bean for document creation
            config: Confluence configuration

        Yields:
            DocumentBean: Each Draw.io diagram individually
        """
        page_id = page_data.get('id')
        logger.info(f"🎨 Page {page_id}: Checking Draw.io diagrams (extract_drawio_as_documents={config.attachments.extract_drawio_as_documents}, include_drawio_png_exports={config.attachments.include_drawio_png_exports})")

        # Only process Draw.io diagrams if attachments are enabled AND extract_drawio_as_documents is enabled
        if not config.attachments.include_attachments:
            logger.info(f"Page {page_id}: Draw.io diagrams ignored because include_attachments=False")
            return

        if not config.attachments.extract_drawio_as_documents:
            logger.info(f"Page {page_id}: Draw.io diagrams ignored because extract_drawio_as_documents=False")
            return

        # If file_extensions is empty, do not process Draw.io diagrams
        if not config.attachments.file_extensions:
            logger.debug(f"Page {page_id}: Draw.io diagrams ignored because file_extensions is empty")
            return

        if not page_id:
            return

        try:
            # Retrieve the page content to detect diagrams
            page_content_data = self._get_page_content_safe(page_id)
            if not page_content_data:
                logger.debug(f"Page {page_id}: Unable to retrieve content for Draw.io detection")
                return

            page_content = page_content_data.get('body', {}).get('storage', {}).get('value', '')
            attachments = self._get_attachments_for_drawio(page_id, config)
            logger.debug(f"Page {page_id}: {len(attachments)} attachments retrieved for Draw.io detection")

            # Detect Draw.io diagrams
            diagrams = self.drawio_detector.detect_drawio_diagrams(
                page_content=page_content,
                page_id=page_id,
                attachments=attachments
            )

            logger.info(f"🔍 Page {page_id}: {len(diagrams)} Draw.io diagrams detected")

            # Process each detected diagram, applying the extension filter
            diagrams_count = 0
            for diagram in diagrams:
                # Check if the diagram should be included according to the extension filter
                if self._should_include_drawio_diagram(diagram, config):
                    diagram_document = self._create_drawio_document_safe(diagram, page_data, source)
                    if diagram_document is not None:
                        diagrams_count += 1
                        logger.info(f"✅ Draw.io diagram created: {diagram_document.id} - {diagram_document.name}")
                        yield diagram_document
                else:
                    logger.debug(f"Draw.io diagram ignored (extension filter): {diagram.title or diagram.diagram_id}")

            if diagrams_count > 0:
                logger.info(f"✅ Page {page_id}: {diagrams_count} Draw.io diagrams extracted as separate documents")
            else:
                logger.debug(f"Page {page_id}: No Draw.io diagram found or processed")

        except Exception as e:
            logger.error(f"Error while processing Draw.io diagrams for page {page_id}: {e}")

    def _process_page_attachments_streaming(self, page_data: Dict[str, Any], source: SourceBean,
                                          config: ConfluenceConfig):
        """
        Generator version to process the attachments of a page.

        Args:
            page_data: Page data
            source: Source bean
            config: Configuration

        Yields:
            DocumentBean: Each attachment individually
        """
        if not config.attachments.include_attachments:
            return

        page_id = page_data.get("id")

        try:
            # Uses the client's routing method that respects the config's filter_mode.
            attachments = self.client.get_page_attachments_filtered(
                page_id=page_id,
                filter_mode=config.attachments.attachment_filter_mode,
                limit=DEFAULT_ATTACHMENT_LIMIT
            )

            logger.debug(
                f"📎 Page {page_id}: {len(attachments)} attachments found with mode '{config.attachments.attachment_filter_mode}'.")

            attachments_count = 0
            for att_data in attachments:
                attachment_bean = self._create_attachment_bean(att_data, source, page_id, config)
                if attachment_bean:
                    attachments_count += 1
                    attachment_title = att_data.get('title', '')
                    # Less verbose now that Draw.io filtering is handled in _create_attachment_bean
                    logger.debug(f"📎 Attachment created: {attachment_bean.id} - {attachment_bean.name}")
                    yield attachment_bean

            if attachments_count > 0:
                logger.debug(f"Page {page_id}: {attachments_count} attachments processed")

        except Exception as e:
            logger.error(f"Unable to retrieve or process attachments for page {page_id}: {e}")

    def _process_child_pages_streaming(self, page_data: Dict[str, Any], source: SourceBean,
                                     config: ConfluenceConfig, current_depth: int):
        """
        Generator version to process child pages recursively.

        Args:
            page_data: Parent page data
            source: Source bean
            config: Configuration
            current_depth: Current depth

        Yields:
            DocumentBean: Each child page and its descendants individually
        """
        if not config.basic.include_child_pages or current_depth >= config.basic.child_page_depth:
            return

        page_id = page_data.get("id")
        if not page_id:
            logger.warning("No page ID found in page_data; skipping child processing.")
            return

        try:
            child_pages = self.cql_search.search_child_pages(page_id, config)
            logger.debug(f"Page {page_id}: {len(child_pages)} children found at depth {current_depth}.")

            children_count = 0
            for child_page_data in child_pages:
                # Recursive call for each child
                for document in self._process_page_recursively_streaming(child_page_data, source, config, current_depth + 1):
                    children_count += 1
                    yield document

            if children_count > 0:
                logger.debug(f"Page {page_id}: {children_count} descendants processed at depth {current_depth}")

        except Exception as e:
            logger.error(f"Error while recursively processing children of page {page_id}: {type(e).__name__}: {e}")
