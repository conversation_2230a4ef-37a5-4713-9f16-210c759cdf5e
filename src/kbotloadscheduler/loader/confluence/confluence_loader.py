"""
Main loader for Confluence.

This class is the entry point for loading documents from Confluence.
It orchestrates operations by delegating to specialized classes for
configuration, search, and processing of documents and spaces.
"""

import logging
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Any, Dict, List, Optional

from kbotloadscheduler.bean.beans import DocumentBean, SourceBean, Metadata
from kbotloadscheduler.mock import is_mocking_enabled
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret

from ..abstract_loader import AbstractLoader
from kbotloadscheduler.exceptions.confluence_exceptions import (
    ConfluenceClientException,
    ConfluenceAuthenticationError,
    ConfluenceConfigurationError
)
from kbotloadscheduler.exceptions.confluence_exceptions import ConfluenceClientException
from .config.confluence_config import ConfluenceConfig
from .config.config_factory import ConfluenceConfigFactory
from .processors.content_downloader import ContentDownloader
from .search.cql_search import CqlSearch
from .space.space_processor import SpaceProcessor
from .utils.circuit_breaker import CircuitBreaker
from .utils.document_id_utils import DocumentIdFormatter
from .client.confluence_credentials import ConfluenceCredentials
from .client.confluence_client import ConfluenceClient

class ConfluenceLoader(AbstractLoader):
    """
    Loads documents from a Confluence instance by processing configured
    spaces, their pages, and their attachments.
    """

    def __init__(self, config: ConfigWithSecret):
        """Initialize the Confluence loader."""
        super().__init__("confluence")
        self.global_config = config
        # CircuitBreaker will be initialized in _initialize_components with config values
        self._circuit_breaker = None

        # Components will be initialized "on demand" (lazy initialization)
        # because they depend on the client, which itself depends on the source.
        self.client: Optional[ConfluenceClient] = None
        self.downloader: Optional[ContentDownloader] = None
        self.space_processor: Optional[SpaceProcessor] = None

    def _initialize_components(self, source: SourceBean):
        """
        Initialize components that depend on a specific source.
        This method is now the only place where configuration is created.
        """
        if self.client is None:
            logging.info(f"Initializing Confluence components for source '{source.code}'...")

            # 1. Create config first - this is the key change
            config = ConfluenceConfigFactory.create_from_source(source)

            # 2. Initialize CircuitBreaker with correct config values
            self._circuit_breaker = CircuitBreaker(
                threshold=config.performance.circuit_breaker_threshold,
                timeout_seconds=config.performance.circuit_breaker_timeout_seconds
            )

            # 3. Create client and other components
            self.client = self._create_confluence_client(source, config)
            base_url = self.client.url

            # 4. Inject dependencies into specialized components
            cql_search = CqlSearch(self.client, self._circuit_breaker)
            self.downloader = ContentDownloader(self.client)
            self.space_processor = SpaceProcessor(self.client, cql_search, base_url)
            logging.info("Confluence components successfully initialized.")

    def get_document_list(self, source: SourceBean) -> List[DocumentBean]:
        """Retrieve the list of all documents for a given source."""
        if self._is_in_mock_mode(source):
            return self._get_mock_document_list(source)

        try:
            # Just initialize, no need to pass config anymore
            self._initialize_components(source)
            # The config must be retrieved after initialization for use here
            config = ConfluenceConfigFactory.create_from_source(source)

            all_documents = []
            spaces_to_process = config.basic.spaces

            if not spaces_to_process:
                logging.warning(f"No space is configured for source '{source.code}'. Returning empty list.")
                return []

            logging.info(f"Starting processing of {len(spaces_to_process)} spaces for source '{source.code}'.")

            # Parallel or sequential execution based on configuration
            if config.performance.parallel_downloads and len(spaces_to_process) > 1:
                with ThreadPoolExecutor(max_workers=min(config.performance.max_parallel_workers, len(spaces_to_process))) as executor:
                    future_to_space = {executor.submit(self.space_processor.process, space, source, config): space for space in spaces_to_process}
                    for future in as_completed(future_to_space):
                        space_key = future_to_space[future]
                        try:
                            all_documents.extend(future.result())
                        except Exception as e:
                            logging.error(f"Failed to process space '{space_key}' in parallel: {e}", exc_info=True)
            else:
                for space_key in spaces_to_process:
                    try:
                        all_documents.extend(self.space_processor.process(space_key, source, config))
                    except Exception as e:
                        logging.error(f"Failed to process space '{space_key}' sequentially: {e}", exc_info=True)

            logging.info(f"Processing completed. {len(all_documents)} documents found in total for source '{source.code}'.")
            return all_documents

        except Exception as e:
            logging.error(f"Critical error while retrieving document list for source '{source.code}': {e}", exc_info=True)
            raise ConfluenceClientException(f"Unable to get Confluence document list: {e}", original_exception=e, resource=source.code)

    def get_document(self, source: SourceBean, document: DocumentBean, output_path: str) -> Dict[str, Any]:
        """Download the content of a specific document."""
        if self._is_in_mock_mode(source):
            return self._get_mock_document_content(source, document, output_path)

        try:
            self._initialize_components(source)
            config = ConfluenceConfigFactory.create_from_source(source)

            item_id = DocumentIdFormatter.parse_document_id(document.id)["confluence_id"]
            is_attachment = DocumentIdFormatter.is_attachment_id(item_id)
            is_drawio_diagram = DocumentIdFormatter.is_drawio_diagram_id(item_id)

            # Create basic metadata
            metadata = {
                Metadata.DOCUMENT_ID: document.id,
                Metadata.DOCUMENT_NAME: document.name,
                "source_path": document.path,
                "modificationDate": document.modification_time.isoformat(),
            }

            if is_attachment:
                logging.info(f"Downloading attachment: {document.name} (ID: {item_id})")
                download_metadata = self.downloader.download_attachment(item_id, document, output_path, config)
            elif is_drawio_diagram:
                logging.info(f"Downloading Draw.io diagram: {document.name} (ID: {item_id})")
                download_metadata = self.downloader.download_drawio_diagram(item_id, document, output_path, config)
            else:
                logging.info(f"Downloading page: {document.name} (ID: {item_id})")
                download_metadata = self.downloader.download_page(item_id, document, output_path, config)

            metadata.update(download_metadata)
            logging.info(f"Successful download for document {document.id}. Location: {metadata.get(Metadata.LOCATION)}")
            return metadata

        except Exception as e:
            logging.error(f"Failed to download document {document.id}: {e}", exc_info=True)
            raise ConfluenceClientException(f"Failed to download Confluence document {document.id}", original_exception=e, resource=document.id)

    def _create_confluence_client(self, source: SourceBean, config: ConfluenceConfig) -> ConfluenceClient:
        """
        Create and return a resilient Confluence client instance.

        This method implements a cascading authentication strategy based on the
        'confluence_auth_mode' setting in the ConfluenceConfig:

        1.  **If `auth_mode` is 'perimeter'**:
            - It first attempts to find a perimeter-specific secret. The secret name
              is constructed as `{perimeter_code}-confluence-credentials`.
              (e.g., 'sandbox-confluence-credentials').
            - If this secret is not found, it logs a warning and falls back to
              the global mode below.

        2.  **If `auth_mode` is 'global' (or as a fallback)**:
            - It looks for a single, global secret. The name of this secret is
              defined by the 'confluence_secret_id' key in the main application
              configuration (e.g., config.yml).
            - If that secret is not found, it makes a final attempt to use
              CONFLUENCE_* environment variables.
        """
        logging.info(f"Creating Confluence client for source: {source.code}")

        auth_mode = config.auth.confluence_auth_mode
        creds_dict = None

        if auth_mode == "perimeter":
            logging.info("Using 'perimeter' authentication mode for Confluence.")
            creds_dict = self.global_config.get_perimeter_confluence_credentials(source.perimeter_code)
            if not creds_dict:
                logging.warning(
                    f"Perimeter mode was configured, but no secret found for perimeter '{source.perimeter_code}'. "
                    f"Falling back to global credentials."
                )

        # 1. Fetch the raw credential dictionary using the global method.
        # If we are in 'global' mode, or if 'perimeter' mode failed, we execute this block.
        if creds_dict is None:
            logging.info("Using 'global' authentication mode for Confluence.")
            creds_dict = self.global_config.get_global_confluence_credentials()

        if not creds_dict:
            raise ConfluenceAuthenticationError(
                "No Confluence credentials found. Ensure 'confluence_secret_id' is configured or "
                "environment variables are set.",
                resource=source.code
            )

        source_of_creds = "the configured global secret" if config.auth.confluence_auth_mode == 'global' else f"the '{source.perimeter_code}' perimeter secret"
        logging.info(f"Successfully obtained credentials for {source_of_creds}.")

        # 2. Use the factory method on ConfluenceCredentials to create a validated object.
        try:
            confluence_creds = ConfluenceCredentials.from_secret_dict(creds_dict)
            if not confluence_creds.is_valid():
                # This is also a configuration issue - the secret exists but is incomplete.
                raise ValueError("The provided credentials are not valid (missing URL, PAT, or user/token).")
        except ValueError as e:
            raise ConfluenceConfigurationError(
                message=f"The Confluence credentials secret is improperly formatted: {e}",
                resource=source.code,
                original_exception=e,
                config_key="confluence_secret_id"
            )

        # 3. Pass the clean, validated objects to the simplified client constructor.
        return ConfluenceClient(credentials=confluence_creds, config=config)

    # --- Mock mode management functions ---

    def _is_in_mock_mode(self, source: SourceBean) -> bool:
        """Check if the loader should operate in mock mode."""
        logging.debug(f"Checking mock mode for source: {source.code}")

        force_real = os.getenv("FORCE_REAL_CONFLUENCE", "false").lower() == "true"
        logging.debug(f"FORCE_REAL_CONFLUENCE = {force_real}")
        if force_real:
            logging.debug("Forced real mode - returning False")
            logging.debug("Real mode activated (forced real)")
            return False

        # Check if global mocking or authentication skip are enabled
        global_mocking = is_mocking_enabled()
        skip_auth = os.getenv("SKIP_EXTERNAL_AUTH", "false").lower() == "true"
        logging.debug(f"Global mocking enabled: {global_mocking}")
        logging.debug(f"SKIP_EXTERNAL_AUTH: {skip_auth}")

        if global_mocking or skip_auth:
            # Confluence mock is enabled by default, unless explicitly disabled
            confluence_mock_disabled = os.getenv("MOCK_CONFLUENCE", "true").lower() == "false"
            mock_result = not confluence_mock_disabled
            logging.debug(f"MOCK_CONFLUENCE enabled = {mock_result}")
            if mock_result:
                logging.debug("Mock mode activated (conditions met)")
            else:
                logging.debug("Real mode activated (mock disabled)")
            return mock_result

        logging.debug("Real mode activated (no mock)")
        return False

    def _get_mock_document_list(self, source: SourceBean) -> List[DocumentBean]:
        """Return a simulated document list."""
        logging.info(f"CONFLUENCE LOADER: Operating in MOCK mode for get_document_list (source: {source.code})")
        from kbotloadscheduler.mock.confluence_mocks import generate_mock_confluence_documents
        return generate_mock_confluence_documents(source)

    def _get_mock_document_content(self, source: SourceBean, document: DocumentBean, output_path: str) -> Dict[str, Any]:
        """Return simulated document content."""
        logging.info(f"CONFLUENCE LOADER: Operating in MOCK mode for get_document (document: {document.id})")
        from kbotloadscheduler.mock.confluence_mocks import generate_mock_document_content
        return generate_mock_document_content(source, document, output_path)
