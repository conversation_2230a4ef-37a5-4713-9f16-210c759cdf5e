"""Confluence configuration data classes.

This module defines the ConfluenceConfig dataclass and related configuration
classes for the Confluence loader.
"""

import logging
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)

# Maximum attachments to download per page
DEFAULT_ATTACHMENT_LIMIT = 50

# Default list of allowed file extensions
DEFAULT_ALLOWED_FILE_EXTENSIONS = [
    "pdf", "docx", "xlsx", "pptx", "txt", "md", "png", "jpg", "jpeg", "csv", "drawio", "xml", "svg"
]


@dataclass
class AuthConfig:
    """Authentication configuration for Confluence."""

    # Determines the authentication strategy.
    # - "global": Uses a single shared secret defined by 'confluence_secret_id' in the main app config.
    # - "perimeter": Uses a secret named '{perimeter_code}-confluence-credentials'. Falls back to global if not found.
    confluence_auth_mode: str = "global"


@dataclass
class AttachmentConfig:
    """Attachment handling configuration."""

    include_attachments: bool = True
    attachment_filter_mode: str = "content_used"  # "all_current" or "content_used" (only visible from the page)
    file_extensions: list[str] = field(default_factory=lambda: list(DEFAULT_ALLOWED_FILE_EXTENSIONS))
    extract_drawio_as_documents: bool = True  # Whether to extract Draw.io diagrams as separate documents
    include_drawio_png_exports: bool = False  # Whether to include PNG exports of Draw.io diagrams as attachments


@dataclass
class FilteringConfig:
    """Content filtering configuration."""

    labels: list[str] | None = None
    exclude_labels: list[str] | None = None
    last_modified_days: int | None = None
    custom_cql: str | None = None


@dataclass
class BasicConfig:
    """Basic Confluence configuration."""

    spaces: list[str] = field(default_factory=list)
    max_results: int | None = None
    export_format: str = "markdown"  # "html", "markdown", or "pdf"
    include_content_in_metadata: bool = False
    include_child_pages: bool = True
    child_page_depth: int = 15  # 49 is the maximum depth


@dataclass
class PerformanceConfig:
    """Performance and reliability configuration."""

    parallel_downloads: bool = False
    max_parallel_workers: int = 1
    enable_caching: bool = True
    cache_ttl_minutes: int = 60
    retry_attempts: int = 3
    retry_delay_seconds: int = 2
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout_seconds: int = 60
    enable_metrics: bool = True
    use_memory_efficient_processing: bool = True  # Use generator-based processing for large spaces


@dataclass
class FileProcessingConfig:
    """File processing configuration."""

    duplicate_filename_strategy: str = "append_id"
    temp_extensions: list[str] = field(default_factory=lambda: [".tmp", ".temp", ".bak", ".backup"])
    html_indicators: list[bytes] = field(default_factory=lambda: [b"<!doctype html", b"<html", b"<head>", b"<body>", b"text/html"])
    max_filename_length: int = 255
    default_timeout: int = 30





@dataclass
class ConfluenceConfig:
    """Main configuration class for Confluence loader.

    This class composes all configuration components needed for Confluence operations.
    """

    auth: AuthConfig = field(default_factory=AuthConfig)
    basic: BasicConfig = field(default_factory=BasicConfig)
    attachments: AttachmentConfig = field(default_factory=AttachmentConfig)
    filtering: FilteringConfig = field(default_factory=FilteringConfig)
    performance: PerformanceConfig = field(default_factory=PerformanceConfig)
    file_processing: FileProcessingConfig = field(default_factory=FileProcessingConfig)