## Documentation du Module de Configuration Confluence

### Introduction

Ce module fournit un système complet pour la gestion de la configuration d'un connecteur Confluence. Il est conçu pour être le point d'entrée unique pour la création, la validation et l'utilisation des paramètres de configuration.

Le système est architecturé autour du principe de découplage, agissant comme une couche spécialisée qui s'intègre à un environnement d'exécution plus générique (comme kbotloadscheduler).

Il prend en charge :

- L'adaptation des configurations provenant d'un contexte générique pour répondre aux besoins spécifiques du connecteur.
- La **conversion de types** de données brutes (chaînes de caractères) vers leurs types Python appropriés (booléens, entiers).
- Le **mappage** d'un dictionnaire de configuration plat vers une structure d'objets imbriqués et typés (`dataclasses`).
- La **validation** rigoureuse des paramètres pour garantir leur conformité et leur validité.
- Des **fonctions d'aide** (`helpers`) pour appliquer la logique de configuration dans le reste de l'application.

### Architecture et Flux de Création

Le point d'entrée principal est la classe `ConfluenceConfigFactory`. Lorsqu'on lui fournit une configuration source (un `SourceBean`), elle suit un processus en 5 étapes pour produire un objet `ConfluenceConfig` final, validé et prêt à l'emploi.

1.  **Transformation (`ConfluenceConfigTransformer`)** : Le dictionnaire de configuration brut est d'abord transformé pour gérer les anciens formats de configuration (par exemple, convertir `space_key` en `spaces`) et pour normaliser les formats de données (par exemple, les chaînes de caractères séparées par des virgules deviennent des listes).
2.  **Conversion de Types (`ConfigTypeConverter`)** : Les valeurs, qui sont souvent des chaînes de caractères, sont converties dans leurs types Python corrects. Par exemple, `"true"` devient `True`, et `"60"` devient l'entier `60`.
3.  **Mappage (`ConfigMapper`)** : Le dictionnaire plat est ensuite mappé à une structure d'objets imbriqués. Les clés sont regroupées par domaine fonctionnel (`auth`, `attachments`, `filtering`, etc.) pour créer des instances de `dataclasses` spécifiques.
4.  **Instanciation (`ConfluenceConfig`)** : Un objet `ConfluenceConfig` principal est créé en utilisant les objets de configuration imbriqués générés à l'étape précédente.
5.  **Validation (`ConfigValidator`)** : L'objet `ConfluenceConfig` final est rigoureusement validé. Le validateur vérifie que toutes les valeurs respectent les contraintes définies (par exemple, les entiers positifs, les valeurs appartenant à une liste prédéfinie, etc.). Si une règle n'est pas respectée, une exception `ConfluenceConfigurationError` est levée.



---

### Description Détaillée des Fichiers

#### 1. `confluence_config.py`

Ce fichier est le **cœur du modèle de données** de configuration. Il définit toutes les `dataclasses` qui structurent et contiennent les paramètres.

-   **`ConfluenceConfig`**: La classe principale qui agrège toutes les autres configurations. C'est l'objet final que les autres parties de l'application utilisent.
-   **`AuthConfig`**: Contient les paramètres d'authentification (`confluence_auth_mode`).
-   **`AttachmentConfig`**: Gère tout ce qui concerne les pièces jointes (inclusion, filtrage, extensions de fichiers, gestion des diagrammes Draw.io).
-   **`FilteringConfig`**: Définit les critères de filtrage des pages Confluence (par labels, date de modification, ou requête CQL personnalisée).
-   **`BasicConfig`**: Regroupe les paramètres de base comme les espaces Confluence à traiter (`spaces`), le format d'exportation (`export_format`), et la gestion des pages enfants.
-   **`PerformanceConfig`**: Contient les paramètres liés à la performance et à la fiabilité, comme le parallélisme, la mise en cache, les stratégies de nouvelle tentative (`retry`) et le disjoncteur (`circuit breaker`).
-   **`FileProcessingConfig`**: Gère le traitement des fichiers, comme la stratégie en cas de noms de fichiers dupliqués, la longueur maximale des noms de fichiers et la détection de contenu HTML.
-   **`FutureConfig`**: Espace réservé pour des fonctionnalités futures, permettant de les intégrer au modèle de données sans les activer.

#### 2. `config_factory.py`

Ce fichier contient la **fabrique** (`Factory`) qui orchestre le processus de création de la configuration.

-   **Classe `ConfluenceConfigFactory`**:
    -   **Méthode `create_from_source(source)`**: C'est le **seul point d'entrée public** pour créer un objet `ConfluenceConfig`. Elle exécute la séquence Transformation -> Conversion -> Mappage -> Instanciation -> Validation. Elle encapsule toute la complexité de la création de la configuration, offrant une interface simple et fiable au reste de l'application.

#### 3. `config_validator.py`

Ce fichier est le **gardien de la validité** de la configuration. Sa seule responsabilité est de vérifier que l'objet `ConfluenceConfig` est correct.

-   **Classe `ConfigValidator`**:
    -   Contient des constantes définissant les valeurs valides (ex: `VALID_EXPORT_FORMATS`, `VALID_AUTH_MODES`).
    -   **Méthode `validate(config)`**: Exécute une série de validations sur chaque partie de l'objet de configuration. Elle utilise des méthodes privées spécialisées (ex: `_validate_positive_int`, `_validate_spaces`).
    -   En cas d'erreur, elle lève une exception `ConfluenceConfigurationError` avec un message clair, indiquant le paramètre problématique et la raison de l'échec.

#### 4. `config_transformer.py`

Ce fichier gère la **compatibilité ascendante et la normalisation** des données de configuration brutes.

-   **Classe `ConfluenceConfigTransformer`**:
    -   **Méthode `transform_configuration(source)`**:
        -   Vérifie si la source est de type "confluence".
        -   Gère la rétrocompatibilité en transformant l'ancien paramètre `space_key` (une seule chaîne) en `spaces` (une liste de chaînes).
        -   Convertit les chaînes de caractères séparées par des virgules (comme `labels` ou `file_extensions`) en véritables listes Python.
        -   Définit des valeurs par défaut pour certains paramètres afin d'assurer un comportement prévisible.

#### 5. `config_type_converter.py`

Ce fichier est dédié à la **conversion des types de données**.

-   **Classe `ConfigTypeConverter`**:
    -   Utilise des ensembles de clés (`_BOOL_KEYS`, `_INT_KEYS`) pour identifier les paramètres à convertir.
    -   **Méthode `convert(data)`**: Modifie le dictionnaire de configuration *en place*.
        -   Convertit les chaînes `"true"`, `"1"`, `"yes"` en booléen `True`.
        -   Convertit les chaînes numériques en entiers. Si une chaîne est vide pour un champ entier, la clé est supprimée pour permettre à la `dataclass` d'utiliser sa valeur par défaut.
        -   Gère des cas spéciaux, comme la conversion d'une liste de chaînes en une liste de `bytes` pour `html_indicators`.

#### 6. `config_mapper.py`

Ce fichier est responsable de la **structuration** du dictionnaire plat en objets imbriqués.

-   **Classe `ConfigMapper`**:
    -   Définit des ensembles de champs correspondant à chaque `dataclass` de configuration (ex: `_AUTH_FIELDS`, `_ATTACHMENT_FIELDS`).
    -   **Méthode `map_to_nested_structure(data)`**:
        -   Crée une instance de chaque `dataclass` de configuration (`AuthConfig`, `BasicConfig`, etc.) en ne lui passant que les clés qui la concernent.
        -   Retourne un dictionnaire final avec une structure imbriquée (`{'auth': AuthConfig(...), 'basic': BasicConfig(...), ...}`), prêt à être utilisé pour instancier la `ConfluenceConfig` principale.

#### 7. `confluence_helpers.py`

Ce fichier contient des **fonctions utilitaires** qui utilisent un objet `ConfluenceConfig` déjà créé pour prendre des décisions logiques.

-   **Fonctions**:
    -   `should_include_attachment(config, attachment_name)`: Détermine si une pièce jointe doit être téléchargée en fonction de son extension et de la configuration.
    -   `should_include_by_labels(config, page_labels)`: Vérifie si une page doit être incluse en fonction des labels requis et exclus.
    -   `should_include_by_date(config, modification_date)`: Filtre le contenu en fonction de sa date de dernière modification.
    -   `is_temp_file(config, filename)`: Identifie les fichiers temporaires.
    -   `contains_html_indicators(config, content)`: Détecte la présence de balises HTML dans un contenu binaire.
    -   `is_filename_too_long(config, filename)`: Vérifie si un nom de fichier dépasse la longueur maximale autorisée.
    -   `create_minimal_config()`: Une fonction utilitaire, principalement pour les tests, qui crée une configuration minimale et valide.