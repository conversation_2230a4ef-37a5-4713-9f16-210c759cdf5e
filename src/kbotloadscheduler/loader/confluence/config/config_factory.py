import logging
from kbotloadscheduler.bean.beans import SourceBean
from .confluence_config import ConfluenceConfig
from .config_mapper import ConfigMapper
from .config_transformer import ConfluenceConfigTransformer
from .config_validator import ConfigValidator
from .config_type_converter import ConfigTypeConverter
from kbotloadscheduler.exceptions.confluence_exceptions import ConfluenceConfigurationError

logger = logging.getLogger(__name__)

class ConfluenceConfigFactory:
    """
    Factory to create, populate, and validate a ConfluenceConfig object.
    This is the single entry point for building the configuration.
    """

    @classmethod
    def create_from_source(cls, source: SourceBean) -> ConfluenceConfig:
        """
        Creates an instance of ConfluenceConfig from a SourceBean.
        """
        try:
            # 1. Transform for backward compatibility
            transformed_dict = ConfluenceConfigTransformer.transform_configuration(source)

            # 2. Convert string values to correct types
            ConfigTypeConverter.convert(transformed_dict)

            # 3. Map flat dictionary to nested config objects
            config_data = ConfigMapper.map_to_nested_structure(transformed_dict)

            # 4. Create the final composite config instance
            config = ConfluenceConfig(**config_data)

            # 5. Validate the final object
            validator = ConfigValidator()
            validator.validate(config)

            logger.debug(f"Validated configuration created for source '{source.code}'.")
            return config

        except (ValueError, TypeError) as e:
            raise ConfluenceConfigurationError(f"Type error in configuration: {e}") from e
        except ConfluenceConfigurationError:
            raise
        except Exception as e:
            raise ConfluenceConfigurationError(f"Unexpected error during configuration creation: {e}") from e
