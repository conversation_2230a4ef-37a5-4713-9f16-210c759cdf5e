"""Configuration validator for ConfluenceConfig.

This module handles validation of configuration parameters to ensure they
meet the required constraints and business rules. It is the single
source of truth for configuration validation.
"""
from .confluence_config import (
    ConfluenceConfig, AuthConfig, BasicConfig,
    PerformanceConfig, AttachmentConfig, FileProcessingConfig, FilteringConfig
)
from kbotloadscheduler.exceptions.confluence_exceptions import ConfluenceConfigurationError


class ConfigValidator:
    """Validator for configuration parameters."""


    # Constants provide a single source of truth for valid values and thresholds.
    MIN_FILENAME_LENGTH = 20
    VALID_EXPORT_FORMATS = ["html", "markdown", "md", "pdf"]
    VALID_ATTACHMENT_MODES = ["all_current", "content_used"]
    VALID_DUPLICATE_FILENAME_STRATEGIES = ["append_id", "append_counter", "overwrite"]
    VALID_AUTH_MODES = ["global", "perimeter"]

    def _validate_positive_int(self, value: any, key: str, is_optional: bool = False) -> None:
        """Validates that a value is a positive integer."""
        if is_optional and value is None:
            return
        if not isinstance(value, int):
            raise ConfluenceConfigurationError(f"{key} must be an integer, but got type: {type(value).__name__}.", config_key=key)
        if value <= 0:
            raise ConfluenceConfigurationError(f"{key} must be a positive integer, but got: {value}.", config_key=key)

    def _validate_non_negative_int(self, value: any, key: str) -> None:
        """Validates that a value is a non-negative integer."""
        if not isinstance(value, int):
            raise ConfluenceConfigurationError(f"{key} must be an integer, but got type: {type(value).__name__}.", config_key=key)
        if value < 0:
            raise ConfluenceConfigurationError(f"{key} must be a non-negative integer (>= 0), but got: {value}.", config_key=key)

    def _validate_non_negative_number(self, value: any, key: str) -> None:
        """Validates that a value is a non-negative number (int or float)."""
        if not isinstance(value, (int, float)):
            raise ConfluenceConfigurationError(f"{key} must be a number, but got type: {type(value).__name__}.", config_key=key)
        if value < 0:
            raise ConfluenceConfigurationError(f"{key} must be a non-negative number (>= 0), but got: {value}.", config_key=key)

    def validate(self, config: ConfluenceConfig) -> None:
        """
        Validate configuration parameters. Fails fast by raising an exception.

        Args:
            config: Configuration object to validate.

        Raises:
            ConfluenceConfigurationError: If any validation fails.
        """
        self._validate_auth_settings(config.auth)
        self._validate_spaces(config.basic)
        self._validate_export_settings(config.basic)
        self._validate_filtering_settings(config.filtering)
        self._validate_basic_limits(config.basic)
        self._validate_performance_settings(config.performance)
        self._validate_retry_settings(config.performance)
        self._validate_attachment_settings(config.attachments)
        self._validate_file_processing_settings(config.file_processing)

    def _validate_auth_settings(self, config: AuthConfig) -> None:
        """Validates authentication-related settings."""
        if config.confluence_auth_mode not in self.VALID_AUTH_MODES:
            raise ConfluenceConfigurationError(
                f"Invalid 'confluence_auth_mode': '{config.confluence_auth_mode}'. "
                f"Must be one of {self.VALID_AUTH_MODES}.",
                config_key="confluence_auth_mode"
            )

    def _validate_spaces(self, config: BasicConfig) -> None:
        # After transformation, only the 'spaces' list matters.
        if not config.spaces:
            raise ConfluenceConfigurationError(
                "At least one Confluence space must be specified via the 'spaces' (or legacy 'space_key') configuration.",
                config_key="spaces"
            )

    def _validate_export_settings(self, config: BasicConfig) -> None:
        if config.export_format not in self.VALID_EXPORT_FORMATS:
            raise ConfluenceConfigurationError(
                f"export_format must be one of {self.VALID_EXPORT_FORMATS}, but got: '{config.export_format}'.",
                config_key="export_format"
            )


    def _validate_basic_limits(self, config: BasicConfig) -> None:
        self._validate_positive_int(config.max_results, "max_results", is_optional=True)
        self._validate_non_negative_int(config.child_page_depth, "child_page_depth")



    def _validate_performance_settings(self, config: PerformanceConfig) -> None:
        self._validate_positive_int(config.max_parallel_workers, "max_parallel_workers")
        self._validate_positive_int(config.cache_ttl_minutes, "cache_ttl_minutes")
        self._validate_positive_int(config.circuit_breaker_threshold, "circuit_breaker_threshold")

    def _validate_retry_settings(self, config: PerformanceConfig) -> None:
        self._validate_non_negative_int(config.retry_attempts, "retry_attempts")
        self._validate_non_negative_int(config.retry_delay_seconds, "retry_delay_seconds")

    def _validate_filtering_settings(self, config: FilteringConfig) -> None:
        """Validates filtering-related settings."""
        self._validate_positive_int(config.last_modified_days, "last_modified_days", is_optional=True)

    def _validate_attachment_settings(self, config: AttachmentConfig) -> None:
        """Validates attachment-related settings."""
        if config.attachment_filter_mode not in self.VALID_ATTACHMENT_MODES:
            raise ConfluenceConfigurationError(
                f"attachment_filter_mode must be one of {self.VALID_ATTACHMENT_MODES}, but got: '{config.attachment_filter_mode}'.",
                config_key="attachment_filter_mode"
            )

    def _validate_file_processing_settings(self, config: FileProcessingConfig) -> None:
        """Validates file processing settings."""
        # No hasattr needed, as dataclass guarantees attribute existence.
        self._validate_positive_int(config.max_filename_length, "max_filename_length")
        if config.max_filename_length < self.MIN_FILENAME_LENGTH:
            raise ConfluenceConfigurationError(
                f"max_filename_length must be at least {self.MIN_FILENAME_LENGTH}, but got: {config.max_filename_length}.",
                config_key="max_filename_length"
            )
        # --- THIS IS THE FIXED LINE ---
        if config.duplicate_filename_strategy not in self.VALID_DUPLICATE_FILENAME_STRATEGIES:
            raise ConfluenceConfigurationError(
                f"duplicate_filename_strategy must be one of {self.VALID_DUPLICATE_FILENAME_STRATEGIES}, but got: '{config.duplicate_filename_strategy}'.",
                config_key="duplicate_filename_strategy"
            )