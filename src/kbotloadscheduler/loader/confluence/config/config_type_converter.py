"""
Type Converter for Configuration Data

This module provides a dedicated class for converting raw configuration
values (often strings) into their appropriate Python types.
"""
from typing import Dict, Any

class ConfigTypeConverter:
    """A utility class to handle type conversions for configuration dictionaries."""

    # Define the keys for each type conversion in one place.
    _BOOL_KEYS = {
        'include_attachments', 'extract_drawio_as_documents', 'include_drawio_png_exports',
        'include_child_pages', 'parallel_downloads', 'enable_caching', 'enable_metrics',
        'include_content_in_metadata',
    }

    _INT_KEYS = {
        'max_results', 'last_modified_days', 'child_page_depth', 'max_parallel_workers',
        'cache_ttl_minutes', 'retry_attempts', 'retry_delay_seconds',
        'circuit_breaker_threshold', 'max_filename_length', 'default_timeout',
        'circuit_breaker_timeout_seconds',
    }

    @classmethod
    def convert(cls, data: Dict[str, Any]) -> None:
        """
        Converts values in the dictionary in-place to their correct types.
        This method modifies the dictionary directly.

        Args:
            data: The configuration dictionary to process.
        """
        for key in list(data.keys()): # Iterate over a copy of keys to allow modification
            if key in cls._BOOL_KEYS:
                cls._convert_to_bool(data, key)
            elif key in cls._INT_KEYS:
                cls._convert_to_int(data, key)

        # Handle special conversions
        if 'html_indicators' in data:
            cls._convert_str_list_to_bytes_list(data, 'html_indicators')

    @staticmethod
    def _convert_to_bool(data: Dict[str, Any], key: str) -> None:
        """Converts a dictionary value to boolean."""
        if key in data and data[key] is not None:
            value = data[key]
            data[key] = str(value).lower() in ('true', '1', 'yes', 'on')

    @staticmethod
    def _convert_to_int(data: Dict[str, Any], key: str) -> None:
        """Converts a dictionary value to integer, removing it if empty."""
        if key in data and data[key] is not None:
            value = str(data[key]).strip()
            if value:
                try:
                    data[key] = int(value)
                except (ValueError, TypeError):
                    # Let the validator catch the incorrect type later.
                    # This converter's job is just to attempt the cast.
                    pass
            else:
                # An empty string for an int field means "use the default".
                # Removing the key ensures the dataclass factory uses its default.
                data.pop(key, None)

    @staticmethod
    def _convert_str_list_to_bytes_list(data: Dict[str, Any], key: str) -> None:
        """Converts a list of strings to a list of bytes."""
        value = data.get(key)
        if isinstance(value, list) and all(isinstance(item, str) for item in value):
            try:
                data[key] = [item.encode('utf-8') for item in value]
            except (TypeError, AttributeError):
                raise ValueError(f"All elements of '{key}' must be strings to be converted to bytes.")
