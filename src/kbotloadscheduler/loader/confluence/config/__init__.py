"""Configuration management for Confluence loader.

This module provides configuration classes and utilities for the Confluence loader,
including parsing, validation, and type conversion.
"""

from .confluence_config import (
    ConfluenceConfig,
    AuthConfig,
    AttachmentConfig,
    FilteringConfig,
    BasicConfig,
    PerformanceConfig,
    FileProcessingConfig,
    FutureConfig,
    DEFAULT_ATTACHMENT_LIMIT,
    DEFAULT_ALLOWED_FILE_EXTENSIONS,
)
from .confluence_helpers import (
    should_include_attachment,
    should_include_by_labels,
    is_temp_file,
    contains_html_indicators,
    is_filename_too_long,
    should_include_by_date,
    create_minimal_config,
)
from .config_factory import ConfluenceConfigFactory
from .config_validator import ConfigValidator

__all__ = [
    # Main configuration classes
    "ConfluenceConfig",
    "AuthConfig",
    "AttachmentConfig",
    "FilteringConfig",
    "BasicConfig",
    "PerformanceConfig",
    "FileProcessingConfig",
    "FutureConfig",

    # Helper functions
    "should_include_attachment",
    "should_include_by_labels",
    "is_temp_file",
    "contains_html_indicators",
    "is_filename_too_long",
    "should_include_by_date",
    "create_minimal_config",

    # Factory and validator
    "ConfluenceConfigFactory",
    "ConfigValidator",

    # Constants
    "DEFAULT_ATTACHMENT_LIMIT",
    "DEFAULT_ALLOWED_FILE_EXTENSIONS",
]
