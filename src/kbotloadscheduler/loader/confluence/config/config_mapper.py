from typing import Dict, Any

class ConfigMapper:
    # Class-level field mappings
    _AUTH_FIELDS = {'confluence_auth_mode'}

    _ATTACHMENT_FIELDS = {
        'include_attachments', 'attachment_filter_mode', 'file_extensions',
        'extract_drawio_as_documents', 'include_drawio_png_exports'
    }

    _FILTERING_FIELDS = {
        'labels', 'exclude_labels', 'last_modified_days', 'custom_cql'
    }

    _BASIC_FIELDS = {
        'spaces', 'max_results', 'export_format', 'include_content_in_metadata',
        'include_child_pages', 'child_page_depth'
    }

    _PERFORMANCE_FIELDS = {
        'parallel_downloads', 'max_parallel_workers', 'enable_caching',
        'cache_ttl_minutes', 'retry_attempts', 'retry_delay_seconds',
        'circuit_breaker_threshold', 'circuit_breaker_timeout_seconds',
        'enable_metrics', 'use_memory_efficient_processing'
    }

    _FILE_PROCESSING_FIELDS = {
        'duplicate_filename_strategy', 'temp_extensions', 'html_indicators',
        'max_filename_length', 'default_timeout'
    }



    @classmethod
    def map_to_nested_structure(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """Maps a flat dictionary to the nested config structure."""
        from .confluence_config import (
            AuthConfig, AttachmentConfig, FilteringConfig, BasicConfig,
            PerformanceConfig, FileProcessingConfig
        )

        return {
            'auth': AuthConfig(**{k: v for k, v in data.items() if k in cls._AUTH_FIELDS}),
            'attachments': AttachmentConfig(**{k: v for k, v in data.items() if k in cls._ATTACHMENT_FIELDS}),
            'filtering': FilteringConfig(**{k: v for k, v in data.items() if k in cls._FILTERING_FIELDS}),
            'basic': BasicConfig(**{k: v for k, v in data.items() if k in cls._BASIC_FIELDS}),
            'performance': PerformanceConfig(**{k: v for k, v in data.items() if k in cls._PERFORMANCE_FIELDS}),
            'file_processing': FileProcessingConfig(**{k: v for k, v in data.items() if k in cls._FILE_PROCESSING_FIELDS}),
        }