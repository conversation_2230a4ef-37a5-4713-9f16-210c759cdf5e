"""
Confluence Loader Module with Resilient Client Support

This module provides robust Confluence integration with automatic retry capabilities
to handle rate limiting and intermittent server errors.
"""

# Core components
from .client.confluence_client import ConfluenceClient
from .client.confluence_credentials import ConfluenceCredentials
from .confluence_loader import ConfluenceLoader
# Exceptions (now from standardized module)
from kbotloadscheduler.exceptions import ConfluenceClientException

__all__ = [
    # Core
    'ConfluenceClient',
    'ConfluenceCredentials',
    'ConfluenceClientException',
    'ConfluenceLoader',
]
