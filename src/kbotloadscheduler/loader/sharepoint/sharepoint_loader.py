import os
from datetime import datetime, timezone

from kbotloadscheduler.bean.beans import DocumentBean, Metadata, SourceBean
from kbotloadscheduler.gcs import gcs_utils
from kbotloadscheduler.gcs.gcs_utils import tidy_file_name
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret

from .sharepoint_client import SharepointClient
from kbotloadscheduler.exceptions import SharepointException
from .sharepoint_credentials import SharepointCredentials
from ..abstract_loader import <PERSON>bstractLoader, LoaderException


class SharepointLoader(AbstractLoader):
    """Loader pour sharepoint"""

    ID_SEPARATOR = "|"

    def __init__(self, config: ConfigWithSecret):
        super().__init__("sharepoint")
        self.sharepoint_url = os.getenv("URL_SERVICE_SHAREPOINT")
        self.config = config
        self.use_id = os.getenv("SHAREPOINT_IN_DOC_ID_USE_PROPERTY")

    def get_document_list(self, source: SourceBean):
        sharepoint_client = self.get_sharepoint_client(source)
        site_name = source.get_expected_conf("site_name")
        relative_directory = source.get_expected_conf("relative_directory")
        site_relative_directory = f"/sites/{site_name}/{relative_directory}"
        # Source config is always by path
        source_folder_info = sharepoint_client.get_folder_info_by_path(site_relative_directory)
        source_files = self.get_files_and_subfolders(sharepoint_client, source_folder_info)
        return [self.as_document_bean(source.domain_code, source.code, file_info) for file_info in source_files]

    def get_document(self, source: SourceBean, document: DocumentBean, output_path):
        site_name = source.get_expected_conf("site_name")
        relative_directory = source.get_expected_conf("relative_directory")
        site_relative_directory = f"/sites/{site_name}/{relative_directory}/"
        sharepoint_client = self.get_sharepoint_client(source)
        metadata = {}
        file_relative_path = document.name
        tidy_relative_path = tidy_file_name(file_relative_path.replace(site_relative_directory, ""))
        # file_relative_path starts with /sites (it's the ServerRelativeUrl given by sharepoint)
        source_url = self.sharepoint_url + file_relative_path
        destination_path = f"{output_path}/{tidy_relative_path}"
        metadata[Metadata.DOCUMENT_ID] = document.id
        metadata[Metadata.DOCUMENT_NAME] = document.name
        metadata[Metadata.SOURCE_URL] = source_url
        metadata[Metadata.LOCATION] = destination_path

        # NOUVEAU: Ajouter des métadonnées de relation basées sur la structure
        # Désactivé pour l'instant - A valider avec Pierre-Emmanuel
        # metadata.update(self._extract_folder_relationships(file_relative_path, site_relative_directory))

        try:
            simulated_file_info = {self.use_id: self.get_file_id_from_document_id(document.id)}
            file_info = sharepoint_client.get_file_info(self.use_id, simulated_file_info)
            file_content = sharepoint_client.get_file_bytes_content(self.use_id, simulated_file_info)
            gcs_utils.create_file_with_bytes_content(destination_path, file_content)
            if gcs_utils.exists_file_gcs(destination_path):
                file_creation_time = self.parse_datetime(file_info.get(SharepointClient.TIME_CREATED, ""))
                file_modification_time = self.parse_datetime(file_info.get(SharepointClient.TIME_LAST_MODIFIED, ""))
                metadata = {
                    **metadata,
                    Metadata.CREATION_TIME: file_creation_time,
                    Metadata.MODIFICATION_TIME: file_modification_time,
                }
                return metadata
            else:
                raise LoaderException(f"File {destination_path} not created at destination")
        except SharepointException as ex:
            raise LoaderException(f"{str(ex)} ({ex.status_code})")

    def _extract_folder_relationships(self, file_relative_path: str, site_relative_directory: str) -> dict:
        """Extract relationship metadata based on folder structure.

        Args:
            file_relative_path: Full path of the file (/sites/mysite/docs/products/guide.pdf)
            site_relative_directory: Base directory path (/sites/mysite/docs/)

        Returns:
            Dictionary with relationship metadata
        """
        import os

        # Extract relative path within the source directory
        relative_to_source = file_relative_path.replace(site_relative_directory, "")

        # Extract folder components
        path_parts = relative_to_source.split("/")
        file_name = path_parts[-1]
        folder_path = "/".join(path_parts[:-1]) if len(path_parts) > 1 else ""

        # Extract file components
        file_base, file_ext = os.path.splitext(file_name)

        metadata = {
            "folder_path": folder_path,  # "products/installation"
            "parent_folder": path_parts[-2] if len(path_parts) > 1 else "",  # "installation"
            "folder_hierarchy": path_parts[:-1],  # ["products", "installation"]
            "file_base_name": file_base,  # "installation-guide"
            "file_extension": file_ext.lower().lstrip('.'),  # "pdf"
            "depth_level": len(path_parts) - 1,  # 2 (niveaux de dossiers)
        }

        # Infer potential relationships
        if folder_path:
            # Files in the same folder are potentially related
            metadata["folder_group_id"] = f"{site_relative_directory.rstrip('/')}/{folder_path}"

        # Infer document families by base name patterns
        if "-" in file_base or "_" in file_base:
            # Extract potential document family (e.g., "installation-guide" → "installation")
            potential_family = file_base.split("-")[0].split("_")[0]
            metadata["document_family"] = potential_family

        return metadata

    def get_sharepoint_client(self, source: SourceBean):
        client_config = self.config.get_sharepoint_client_config(source.perimeter_code)
        client_private_key = self.config.get_sharepoint_client_private_key(source.perimeter_code)
        sharepoint_credentials = SharepointCredentials(
            tenant_id=client_config.get("tenant_id"),
            client_id=client_config.get("client_id"),
            client_certificate=client_config.get("certificate_thumbprint"),
            client_private_key=client_private_key,
            client_key_password=client_config.get("password"),
        )
        site_name = source.get_expected_conf("site_name")
        client = SharepointClient(sharepoint_credentials, self.sharepoint_url, site_name)
        return client

    def get_files_and_subfolders(self, sharepoint_client, folder_info):
        folder_files = sharepoint_client.get_folders_files(self.use_id, folder_info)
        folder_sub_folders = sharepoint_client.get_sub_folders(self.use_id, folder_info)
        for sub_folder in folder_sub_folders:
            sub_folder_files = self.get_files_and_subfolders(sharepoint_client, sub_folder)
            folder_files.extend(sub_folder_files)
        return folder_files

    def as_document_bean(self, domain_code, source_code, file_info):
        file_id = file_info.get(self.use_id, "")
        file_path = file_info.get(SharepointClient.SERVER_RELATIVE_URL)
        file_modification_time = self.parse_datetime(file_info.get(SharepointClient.TIME_LAST_MODIFIED, ""))
        return DocumentBean(
            id=self.ID_SEPARATOR.join([domain_code, source_code, file_id]),
            name=file_path,
            path=self.sharepoint_url + file_path,
            modification_time=file_modification_time,
        )

    def get_file_id_from_document_id(self, document_id):
        file_id = ""
        document_id_parts = document_id.split(self.ID_SEPARATOR)
        if len(document_id_parts) > 2:
            file_id = document_id_parts[2]
        return file_id

    @classmethod
    def parse_datetime(cls, string_datetime: str) -> datetime:
        """
        Traduit la date et l'heure reçues de l'api en un objet datetime.
        Le format reçu est 2024-09-03T13:00:44Z compatible avec la norme ISO 8601
        """
        d = datetime.fromisoformat(string_datetime[:-1]).astimezone(timezone.utc)
        return d

    def _generate_mock_documents(self, source: SourceBean) -> list[DocumentBean]:
        """Generate mock documents for testing purposes when in mock mode.

        Args:
            source: The source configuration

        Returns:
            List of mock DocumentBean instances for testing
        """
        from datetime import datetime, timedelta

        # Parse source configuration
        domain_code = source.domain_code or "test"
        source_code = source.code or "sharepoint_test"

        # Generate realistic mock SharePoint documents
        mock_documents = []

        files_data = [
            {
                "name": "Marketing-Strategy-2024.docx",
                "path": "/sites/Marketing/Shared Documents/Strategy/Marketing-Strategy-2024.docx",
                "last_modified": datetime.now() - timedelta(days=5),
                "size": 2048576,
                "file_type": "docx"
            },
            {
                "name": "Q4-Budget-Report.xlsx",
                "path": "/sites/Finance/Shared Documents/Reports/Q4-Budget-Report.xlsx",
                "last_modified": datetime.now() - timedelta(days=3),
                "size": 1024768,
                "file_type": "xlsx"
            },
            {
                "name": "Product-Roadmap.pptx",
                "path": "/sites/Product/Shared Documents/Roadmaps/Product-Roadmap.pptx",
                "last_modified": datetime.now() - timedelta(days=1),
                "size": 4096512,
                "file_type": "pptx"
            },
            {
                "name": "Technical-Specifications.pdf",
                "path": "/sites/Engineering/Shared Documents/Specs/Technical-Specifications.pdf",
                "last_modified": datetime.now() - timedelta(hours=12),
                "size": 1536384,
                "file_type": "pdf"
            },
            {
                "name": "Meeting-Notes.docx",
                "path": "/sites/General/Shared Documents/Meetings/Meeting-Notes.docx",
                "last_modified": datetime.now() - timedelta(hours=2),
                "size": 512256,
                "file_type": "docx"
            }
        ]

        for i, file_data in enumerate(files_data):
            doc_id = f"{domain_code}|{source_code}|{file_data['name'].replace(' ', '_').replace('-', '_')}"
            modification_time = file_data['last_modified'].strftime('%Y%m%d%H%M%S')

            document = DocumentBean(
                id=doc_id,
                path=file_data['path'],
                name=file_data['name'],
                modification_time=modification_time
            )
            mock_documents.append(document)

        print(f"🎭 Generated {len(mock_documents)} mock SharePoint documents for testing")
        return mock_documents
