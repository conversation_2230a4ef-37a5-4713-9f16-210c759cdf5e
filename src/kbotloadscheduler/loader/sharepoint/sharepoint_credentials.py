import uuid
from datetime import datetime, timedelta, timezone

import jwt
import requests
from cryptography.hazmat.primitives import serialization


class SharepointCredentials:
    def __init__(self, tenant_id, client_id, client_certificate, client_private_key, client_key_password):
        self.tenant_id = tenant_id
        self.client_id = client_id
        self.client_certificate = client_certificate
        self.client_private_key = client_private_key.encode(encoding="utf-8")
        self.client_key_password = client_key_password.encode(encoding="utf-8")
        self.base_url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token"
        self.resource_url = f"https://{tenant_id}.sharepoint.com/"

    def get_access_token(self):
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        body = {
            "grant_type": "client_credentials",
            "client_id": self.client_id,
            "scope": self.resource_url + ".default",
            "client_assertion_type": "urn:ietf:params:oauth:client-assertion-type:jwt-bearer",
            "client_assertion": self.get_jwt_client_assertion(),
        }
        response = requests.post(self.base_url, timeout=300, headers=headers, data=body)
        return response.json().get("access_token")  # Extract access token from the response

    def get_jwt_client_assertion(self):
        header = {"alg": "RS256", "typ": "JWT", "x5t": self.client_certificate}
        payload = {
            "sub": self.client_id,
            "iss": self.client_id,
            "aud": self.base_url,
            "jti": str(uuid.uuid4()),
            "exp": datetime.now(tz=timezone.utc) + timedelta(seconds=300),
        }
        private_key = serialization.load_pem_private_key(self.client_private_key, password=self.client_key_password)
        assertion_string = jwt.encode(payload, private_key, headers=header)
        return assertion_string
