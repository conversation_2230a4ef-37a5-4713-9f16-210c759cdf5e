from typing import Annotated, List

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Body, Depends, HTTPException, Path, status

from ..bean.api_post_beans import GetDocumentFileBean, LoaderGetListFileBean
from ..bean.beans import DocumentAnswerBean, DocumentWithMetadataBean
from ..dependency.container import Container
from ..loader.abstract_loader import LoaderException
from ..logging.load_scheduler_logging import log_exception
from ..service.loader_service import LoaderService

"""Routes de lancement des loader

Ces routes vont déclencher les actions suivantes pour les loader :
  * /loader/list : lance la récupération de la liste des documents
  * /loader/documents : lance la récupération des documents et leur copie sur GCS
"""

TAG = "loader"
DESCRIPTION = """Appels du loader associé à la source à charger
                pour lister les documents de la source et les récupérer sur GCP"""
BAD_PERIMETER_CODE = "perimeter_code_not_valid"
LOADER_EXCEPTION = "loader_exception"
router = APIRouter()


@router.post("/loader/list/{perimeter_code}")
@inject
async def get_document_list(
    perimeter_code: Annotated[str, Path(description="Code du périmètre concerné")],
    get_list_file_bean: Annotated[
        LoaderGetListFileBean,
        Body(
            description="""Fichier getlist contenant la définition de la source
            pour laquelle on veut récupérer la liste des documents"""
        ),
    ],
    loader_service: LoaderService = Depends(Provide[Container.loader_service]),
) -> List[DocumentAnswerBean]:
    """Lancement de la récupération de la liste des documents présents dans la source
    \f
    :param perimeter_code: le code du périmètre. Cela permet de savoir sur quel bucket écrire la liste
    :param get_list_file_bean: le fichier contenant la configuration de la source
    pour laquelle on veut lister les documents.
    Ce fichier a été généré par l'appel de l'api sources/load ou sources/loadall.
    :param loader_service: le service qui ordonnance l'appel au Loader en fonction de la source
    :return: la liste des documents récupérées
    """
    if not perimeter_code.isalnum():
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=BAD_PERIMETER_CODE)
    get_list_file = get_list_file_bean.get_list_file
    return loader_service.get_document_list(perimeter_code, get_list_file)


@router.post("/loader/document/{perimeter_code}")
@inject
async def get_document(
    perimeter_code: Annotated[str, Path(description="Code du périmètre concerné")],
    document_get_file_bean: Annotated[
        GetDocumentFileBean,
        Body(
            description="""Fichier getdoc contenant la définition de la source
            et le document à récupérer pour cette source"""
        ),
    ],
    loader_service: LoaderService = Depends(Provide[Container.loader_service]),
) -> DocumentWithMetadataBean:
    """Lancement de la récupération des documents d'une source
    \f
    :param perimeter_code: le code du périmètre. Cela permet de savoir sur quel bucket récupérer le document
    :param document_get_file_bean: fichier contenant la configuration de la source
    et du document à récupérer.
    Ce fichier a été généré par l'appel de l'api embedding/comparelist
    :param loader_service: le service qui ordonnance l'appel au Loader en fonction de la source du documents
    :return: les metadatas du document à récupérer
    """
    if not perimeter_code.isalnum():
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=BAD_PERIMETER_CODE)
    try:
        return loader_service.get_document(perimeter_code, document_get_file_bean.document_get_file)
    except LoaderException as e:
        log_exception(perimeter_code, "/loader/document/" + perimeter_code, document_get_file_bean.document_get_file, e)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=LOADER_EXCEPTION)
