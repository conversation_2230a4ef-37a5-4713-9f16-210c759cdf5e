from fastapi import APIRouter
import requests

TAG = "network-test"
DESCRIPTION = "Test de connectivité réseau sortant"

router = APIRouter()

@router.get("/network-test/confluence", tags=[TAG])
def test_confluence_connectivity():
    url = "https://espace.agir.orange.com"
    try:
        response = requests.get(url, timeout=10, verify=True)
        result = {
            "success": True,
            "status_code": response.status_code,
            "headers": dict(response.headers),
            "content_snippet": response.text[:200]
        }
        print("Network test result:", result)
        return result
    except Exception as e:
        error_result = {
            "success": False,
            "error": str(e)
        }
        print("Network test error:", error_result)
        return error_result