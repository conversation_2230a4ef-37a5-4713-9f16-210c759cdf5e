import urllib.parse

import requests

from ..bean.beans import DocumentBean
from ..logging.load_scheduler_logging import log_remove_document_error, log_message
from .auth_header import build_auth_header


class KbotEmbeddingApi:
    """Appels de l'api kbot-embedding"""

    NO_PROXY = {"http": "", "https": ""}

    def __init__(self, url: str, env: str = "local") -> None:
        self.url = url
        self.env = env

    def get_document_list(self, perimeter_code: str, domain_code: str, source_code: str) -> list[DocumentBean]:
        get_document_list_url = (
            self.url + f"/embedded_documents?" f"perimeter={perimeter_code}&domain={domain_code}&source={source_code}"
        )
        log_message(perimeter_code, f"Calling embedding API: {get_document_list_url}")
        log_message(perimeter_code, f"Environment: {self.env}")

        headers = build_auth_header(self.env, self.url)
        log_message(perimeter_code, f"Request headers prepared. Has auth header: {'X-Serverless-Authorization' in headers}")

        try:
            log_message(perimeter_code, f"Making GET request to embedding API...")
            response = requests.get(get_document_list_url, timeout=300, headers=headers, proxies=self.NO_PROXY)
            log_message(perimeter_code, f"API response received. Status code: {response.status_code}")

            if response.status_code == 200:
                documents = response.json()
                log_message(perimeter_code, f"Successfully retrieved {len(documents)} documents from embedding API")
                return [self.as_document_bean(document) for document in documents]
            else:
                log_message(perimeter_code, f"API call failed with status {response.status_code}. Response text: {response.text[:500]}")
                raise KbotEmbeddingApi.EmbeddingException(
                    "get_document_list for perimeter: %s domain: %s source: %s"
                    % (perimeter_code, domain_code, source_code),
                    response,
                )
        except requests.exceptions.RequestException as e:
            log_message(perimeter_code, f"Network error calling embedding API: {type(e).__name__}: {str(e)}")
            raise
        except Exception as e:
            log_message(perimeter_code, f"Unexpected error calling embedding API: {type(e).__name__}: {str(e)}")
            raise

    def embedd_document(self, perimeter_code: str, metadata_file: str):
        if not perimeter_code.isalnum():
            return {"status": "ko", "error": "perimeter_code non valide"}
        embedd_document_url = self.url + f"/embedded_documents/{perimeter_code}"
        headers = build_auth_header(self.env, self.url)
        response = requests.post(
            embedd_document_url, timeout=300, headers=headers, proxies=self.NO_PROXY, json={"json_path": metadata_file}
        )
        if response.status_code == 200:
            document = response.json()
            return {"status": "ok", "document": self.as_document_bean(document)}
        else:
            raise KbotEmbeddingApi.EmbeddingException(
                embedd_document_url + " with metadata file " + metadata_file, response
            )

    def remove_document(self, perimeter_code: str, document: DocumentBean) -> bool:
        remove_document_url = (
            self.url + f"/embedded_documents?"
            f"perimeter={perimeter_code}"
            f"&document_id={urllib.parse.quote_plus(document.id)}"
        )
        headers = build_auth_header(self.env, self.url)
        response = requests.delete(remove_document_url, timeout=300, headers=headers, proxies=self.NO_PROXY)
        if response.ok:
            return True
        else:
            log_remove_document_error(perimeter_code, remove_document_url, response.status_code)
            return False

    @classmethod
    def as_document_bean(cls, document) -> DocumentBean:
        """Transforme le dictionnaire document récupéré de l'api en un objet DocumentBean

        :param document: document à transformer
        :return: le DocumentBean correspondant au document
        """
        return DocumentBean.from_embedding_api(document)

    class EmbeddingException(Exception):

        def __init__(self, error_message, response):
            if response.status_code == 200:
                error_message += response.json()
            else:
                error_message += " => status_code is %s" % response.status_code
            super().__init__(error_message)
