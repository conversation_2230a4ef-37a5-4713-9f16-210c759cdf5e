import logging
import os
import time

import requests
from requests.auth import HTTPBasicAuth


class OrangeJwt:
    # Path to your certificate bundle or Orange G2 root certificate
    # (put in secret ca_certificate : value is pem format of PKI Interne
    # found in "Chain certification" menu in PKI Servers )
    PATH_TO_SECRET = os.environ.get("PATH_TO_SECRET_CONFIG", "/etc/secrets")
    CA_CERTIFICATE = os.path.join(PATH_TO_SECRET, "ca_certificate", "secret")

    def __init__(self, token_url=None, client_id=None, client_secret=None, scopes=None, time_before_expiration=300):
        """
        Optional arguments only needed for token request (client application)
          client_id, a string with  the client ID
          client_secret, a string with  the client secret
          scopes, a list of requested scopes
          time_before_expiration, when asking for a token,
              use the cached token until expiration minus time_before_expiration
        """
        if scopes is None:
            scopes = []
        self.token_url = token_url
        self.client_id = client_id
        self.client_secret = client_secret
        self.scopes = scopes
        self.time_before_expiration = time_before_expiration
        self.token_expiration = time.time()

        self.token = None
        self.decoded_token = None
        if (
            self.token_url is not None
            and self.client_id is not None
            and self.client_secret is not None
            and len(self.scopes) != 0
        ):
            self.get_token()

    def get_token(self, force_refresh=False):
        """
        Get and cache a valid oauth2 token
        If subsequent calls are made, use the cached token
        If the token is `self.time_before_expiration` seconds its expiration time, renew it
        If `force_refresh` is True, renew it
        """
        now = time.time()
        if force_refresh or self.token is None or (self.token_expiration - now) < self.time_before_expiration:
            logging.info("Refresh client token")
            r = requests.post(
                self.token_url,
                auth=HTTPBasicAuth(self.client_id, self.client_secret),
                timeout=300,
                headers={"Content-Type": "application/x-www-form-urlencoded", "Accept": "application/json"},
                data={"grant_type": "client_credentials", "scope": " ".join(self.scopes)},
                verify=self.CA_CERTIFICATE,
            )
            r.raise_for_status()
            self.token = r.json()["access_token"]
            self.token_expiration = now + int(r.json()["expires_in"])

        return self.token
