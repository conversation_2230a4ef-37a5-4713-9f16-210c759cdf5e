"""
Configuration d'authentification mockée pour les tests locaux

Ce module fournit une authentification simulée lorsque USE_MOCKS=true est défini.
Il empêche les appels API externes vers OKAPI et autres services d'authentification.

Fonctionnalités :
- Mock des tokens JWT Orange
- Mock du client ProcedureSheetClient pour éviter les appels OKAPI
- Activation automatique si USE_MOCKS=true
- Headers d'authentification factices pour les tests

Usage :
    Les mocks sont activés automatiquement si USE_MOCKS=true.
    Pour une activation manuelle : enable_auth_mocking()
"""

import os
from unittest.mock import Mock, patch
from typing import Optional

# Indicateur global pour suivre si le mock d'authentification est activé
_auth_mocking_enabled = False


class MockOrangeJwt:
    """Authentification JWT Orange mockée pour les tests locaux

    Cette classe remplace OrangeJwt en mode test pour éviter les appels
    externes aux services d'authentification Orange.

    Attributes:
        CA_CERTIFICATE (str): Certificat CA factice (vide en mode mock)
    """

    def __init__(self, *args, **kwargs):
        """Initialise le mock JWT sans paramètres externes requis"""
        self.CA_CERTIFICATE = ""

    def get_token(self, force_refresh=False):
        """Retourne un token JWT factice pour les tests

        Args:
            force_refresh (bool): Ignoré en mode mock

        Returns:
            str: Token JWT factice pour les tests
        """
        return "mock-jwt-token-for-testing"


class MockProcedureSheetClient:
    """Client API Basic mocké pour les tests locaux

    Cette classe évite les appels externes à OKAPI en fournissant
    des headers d'authentification prédéfinis.

    Attributes:
        headers (dict): Headers HTTP avec token d'authentification factice
    """

    def __init__(self, *args, **kwargs):
        """Initialise le client avec des headers factices"""
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Accept-Language": "fr",
            "Authorization": "Bearer mock-token"
        }

    def get_new_token_headers(self):
        """Retourne des headers factices sans appels externes

        Returns:
            dict: Headers HTTP avec authentification mockée
        """
        return self.headers


def enable_auth_mocking():
    """Active le mock d'authentification pour les services externes.

    Cette fonction empêche les vrais appels API pendant les tests locaux
    en remplaçant les classes d'authentification par des mocks.

    Fonctionnalités mockées :
    - OrangeJwt : Évite les appels aux services JWT Orange
    - ProcedureSheetClient : Évite les appels OKAPI

    La fonction est idempotente - peut être appelée plusieurs fois sans effet.

    Note:
        Cette fonction est appelée automatiquement si USE_MOCKS=true
    """
    global _auth_mocking_enabled

    if _auth_mocking_enabled:
        return

    print("🔐 Activation du mock d'authentification pour les tests locaux...")

    # Mock de l'authentification JWT Orange
    import kbotloadscheduler.apicall.orange_jwt as orange_jwt_module
    orange_jwt_module.OrangeJwt = MockOrangeJwt

    # Mock de l'authentification du client Basic
    from kbotloadscheduler.loader.basic import basic_client

    # Patch du ProcedureSheetClient pour éviter les appels OKAPI
    original_init = basic_client.ProcedureSheetClient.__init__

    def mock_init(self, url_service, okapi_url, client_id, client_secret, service_scope, ca_certificate, timeout, env):
        """Initialise sans faire d'appels d'authentification externes

        Args:
            url_service (str): URL du service (conservée)
            okapi_url (str): URL OKAPI (conservée mais non utilisée)
            client_id (str): ID client (conservé mais non utilisé)
            client_secret (str): Secret client (conservé mais non utilisé)
            service_scope (str): Scope du service (conservé)
            ca_certificate (str): Certificat CA (conservé)
            timeout (int): Timeout (conservé)
            env (str): Environnement (utilisé pour les status BA)
        """
        self.okapi_url = okapi_url
        self.client_id = client_id
        self.client_secret = client_secret
        self.service_scope = service_scope
        self.ca_certificate = ca_certificate
        self.timeout = timeout
        self.url_service = url_service
        self.env = env

        # Définit des headers factices sans appeler OKAPI
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Accept-Language": "fr",
            "Authorization": "Bearer mock-token-for-testing"
        }

        self.sheet_status = "published"
        self.failed = []
        self.empty = []

        # Configuration des status BA par environnement
        env_to_status_ba = {
            "local": [4, 5],
            "tests": [4, 5],
            "dev": [4, 5],
            "ppr": [2, 3, 4, 5],
            "prd": [4, 5],
        }
        self.status_ba_list = env_to_status_ba.get(env, [4, 5])

    # Mock de la méthode get_new_token_headers pour éviter les appels OKAPI
    def mock_get_new_token_headers(self):
        """Retourne des headers factices sans appeler OKAPI

        Returns:
            dict: Headers HTTP avec token d'authentification factice
        """
        return {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Accept-Language": "fr",
            "Authorization": "Bearer mock-token-for-testing"
        }

    basic_client.ProcedureSheetClient.__init__ = mock_init
    basic_client.ProcedureSheetClient.get_new_token_headers = mock_get_new_token_headers

    _auth_mocking_enabled = True
    print("✅ Mock d'authentification activé ! Les appels d'auth externes seront mockés.")


def is_auth_mocking_enabled() -> bool:
    """Vérifie si le mock d'authentification est actuellement activé

    Returns:
        bool: True si le mock est activé, False sinon
    """
    return _auth_mocking_enabled


# Auto-activation du mock d'authentification si la variable d'environnement est définie
if os.getenv("USE_MOCKS", "false").lower() == "true":
    enable_auth_mocking()
