from kbotloadscheduler.bean.beans import DocumentBean, SourceBean
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret
from kbotloadscheduler.loader.abstract_loader import AbstractLoader


class BasicMockLoader(AbstractLoader):
    """Loader pour les fiches basic"""

    def __init__(self, config_with_secret: ConfigWithSecret):
        super().__init__("basic")

    def get_document_list(self, source: SourceBean):
        print("🔧 BasicLoader.get_document_list() called in mock mode - returning empty list")
        return []

    def get_document(self, source: SourceBean, document: DocumentBean, output_path):
        print("🔧 BasicLoader.get_document() called in mock mode - returning empty metadata")
        return {}
