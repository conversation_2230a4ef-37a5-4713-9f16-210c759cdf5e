"""
Confluence Loader for kbot-loader-scheduler
Mock mode
"""
import logging
from kbotloadscheduler.bean.beans import DocumentBean, Metadata, SourceBean
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret
from kbotloadscheduler.loader.abstract_loader import Abstract<PERSON>oader
from typing import Any


logger = logging.getLogger(__name__)


class ConfluenceMockLoader(AbstractLoader):
    """Mock Loader for Confluence"""

    def __init__(self, config: ConfigWithSecret):
        """Initialize the Confluence loader.

        Args:
            config: Configuration with secrets
        """
        super().__init__("confluence")

    # --- API publique requise par AbstractLoader ---

    def get_document_list(self, source: SourceBean) -> list[DocumentBean]:
        """Get list of documents from Confluence source with advanced filtering."""
        print("🔧 ConfluenceLoader operating in mock mode - returning mock document list")
        return self._generate_mock_documents(source)

    def get_document(self, source: SourceBean, document: DocumentBean, output_path: str) -> dict[str, Any]:
        """Download a document from Confluence to GCS with production optimizations.
        """
        print("🔧 ConfluenceLoader.get_document() called in mock mode - returning empty metadata")
        return {}
