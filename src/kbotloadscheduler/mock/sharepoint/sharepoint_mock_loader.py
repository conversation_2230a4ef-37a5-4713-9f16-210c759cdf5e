from kbotloadscheduler.bean.beans import Document<PERSON><PERSON>, Metadata, SourceBean
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret
from kbotloadscheduler.loader.abstract_loader import AbstractLoader, LoaderException


class SharepointMockLoader(AbstractLoader):
    """Loader pour sharepoint en mode mock"""

    def __init__(self, config: ConfigWithSecret):
        super().__init__("sharepoint")

    def get_document_list(self, source: SourceBean):
        print("🔧 SharepointLoader operating in mock mode - returning mock document list")
        return self._generate_mock_documents(source)

    def get_document(self, source: SourceBean, document: DocumentBean, output_path):
        print("🔧 SharepointLoader.get_document() called in mock mode - returning empty metadata")
        return {}

    def _generate_mock_documents(self, source: SourceBean) -> list[DocumentBean]:
        """Generate mock documents for testing purposes when in mock mode.

        Args:
            source: The source configuration

        Returns:
            List of mock DocumentBean instances for testing
        """
        from datetime import datetime, timedelta

        # Parse source configuration
        domain_code = source.domain_code or "test"
        source_code = source.code or "sharepoint_test"

        # Generate realistic mock SharePoint documents
        mock_documents = []

        files_data = [
            {
                "name": "Marketing-Strategy-2024.docx",
                "path": "/sites/Marketing/Shared Documents/Strategy/Marketing-Strategy-2024.docx",
                "last_modified": datetime.now() - timedelta(days=5),
                "size": 2048576,
                "file_type": "docx"
            },
            {
                "name": "Q4-Budget-Report.xlsx",
                "path": "/sites/Finance/Shared Documents/Reports/Q4-Budget-Report.xlsx",
                "last_modified": datetime.now() - timedelta(days=3),
                "size": 1024768,
                "file_type": "xlsx"
            },
            {
                "name": "Product-Roadmap.pptx",
                "path": "/sites/Product/Shared Documents/Roadmaps/Product-Roadmap.pptx",
                "last_modified": datetime.now() - timedelta(days=1),
                "size": 4096512,
                "file_type": "pptx"
            },
            {
                "name": "Technical-Specifications.pdf",
                "path": "/sites/Engineering/Shared Documents/Specs/Technical-Specifications.pdf",
                "last_modified": datetime.now() - timedelta(hours=12),
                "size": 1536384,
                "file_type": "pdf"
            },
            {
                "name": "Meeting-Notes.docx",
                "path": "/sites/General/Shared Documents/Meetings/Meeting-Notes.docx",
                "last_modified": datetime.now() - timedelta(hours=2),
                "size": 512256,
                "file_type": "docx"
            }
        ]

        for i, file_data in enumerate(files_data):
            doc_id = f"{domain_code}|{source_code}|{file_data['name'].replace(' ', '_').replace('-', '_')}"
            modification_time = file_data['last_modified'].strftime('%Y%m%d%H%M%S')

            document = DocumentBean(
                id=doc_id,
                path=file_data['path'],
                name=file_data['name'],
                modification_time=modification_time
            )
            mock_documents.append(document)

        print(f"🎭 Generated {len(mock_documents)} mock SharePoint documents for testing")
        return mock_documents
