"""
Mock data generators for Confluence loader testing.
"""

from datetime import datetime, timedelta
from typing import List

from kbotloadscheduler.bean.beans import DocumentBean, SourceBean


def generate_mock_confluence_documents(source: SourceBean) -> List[DocumentBean]:
    """Generate mock documents for testing purposes.

    Args:
        source: Source configuration containing domain and source codes

    Returns:
        List of mock DocumentBean instances representing Confluence pages and attachments
    """
    # Parse source configuration to get domain/source codes
    domain_code = source.domain_code or "test"
    source_code = source.code or "confluence_test"

    # Generate realistic mock documents
    mock_documents = []

    # Mock Confluence pages
    pages_data = [
        {
            "id": "123456",
            "title": "Architecture Guide",
            "type": "page",
            "space": "ENG",
            "url": "https://confluence.example.com/pages/123456",
            "last_modified": datetime.now() - timedelta(days=2)
        },
        {
            "id": "234567",
            "title": "API Documentation",
            "type": "page",
            "space": "DOCS",
            "url": "https://confluence.example.com/pages/234567",
            "last_modified": datetime.now() - timedelta(days=1)
        },
        {
            "id": "345678",
            "title": "Development Guidelines",
            "type": "page",
            "space": "DEV",
            "url": "https://confluence.example.com/pages/345678",
            "last_modified": datetime.now() - timedelta(hours=6)
        }
    ]

    # Mock attachments
    attachments_data = [
        {
            "id": "att456789",
            "title": "system-diagram.png",
            "type": "attachment",
            "page_id": "123456",
            "url": "https://confluence.example.com/download/attachments/123456/system-diagram.png",
            "last_modified": datetime.now() - timedelta(days=3)
        },
        {
            "id": "att567890",
            "title": "api-spec.pdf",
            "type": "attachment",
            "page_id": "234567",
            "url": "https://confluence.example.com/download/attachments/234567/api-spec.pdf",
            "last_modified": datetime.now() - timedelta(days=2)
        }
    ]

    # Create DocumentBean instances for pages
    for page_data in pages_data:
        doc_id = f"{domain_code}|{source_code}|{page_data['id']}"
        path = f"/spaces/{page_data['space']}/page/{page_data['title'].replace(' ', '-')}"
        modification_time = page_data['last_modified']

        document = DocumentBean(
            id=doc_id,
            path=path,
            name=page_data['title'],
            modification_time=modification_time
        )
        mock_documents.append(document)

    # Create DocumentBean instances for attachments
    for att_data in attachments_data:
        doc_id = f"{domain_code}|{source_code}|{att_data['id']}"
        path = f"/spaces/attachments/{att_data['page_id']}/{att_data['title']}"
        modification_time = att_data['last_modified']

        document = DocumentBean(
            id=doc_id,
            path=path,
            name=att_data['title'],
            modification_time=modification_time
        )
        mock_documents.append(document)

    print(f"🎭 Generated {len(mock_documents)} mock documents for testing")
    return mock_documents


def generate_mock_document_content(source: SourceBean, document: DocumentBean, output_path: str) -> dict:
    """Generate mock document content for testing purposes.

    Args:
        source: Source configuration
        document: Document to generate content for
        output_path: Output path where content would be written

    Returns:
        Dictionary containing mock metadata for the document
    """
    # Parse document ID to get the confluence ID
    parts = document.id.split("|")
    confluence_id = parts[2] if len(parts) >= 3 else "unknown"

    # Check if it's an attachment
    is_attachment = confluence_id.startswith("att")

    # Generate mock metadata
    mock_metadata = {
        "document_id": document.id,
        "document_name": document.name,
        "source_path": document.path,
        "modificationDate": document.modification_time.isoformat(),
        "confluence_id": confluence_id,
        "is_attachment": is_attachment,
        "output_path": output_path,
        "content_type": "attachment" if is_attachment else "page",
        "mock_data": True,
        "space_key": "MOCK_SPACE",
        "author": "mock_user",
        "version": 1,
        "labels": ["mock", "test"],
        "content_length": 1024 if is_attachment else 2048
    }

    if is_attachment:
        mock_metadata.update({
            "file_size": 1024,
            "mime_type": "application/octet-stream",
            "download_url": document.path
        })
    else:
        mock_metadata.update({
            "content_html": f"<h1>{document.name}</h1><p>Mock content for page {confluence_id}</p>",
            "content_markdown": f"# {document.name}\n\nMock content for page {confluence_id}",
            "page_url": document.path
        })

    print(f"🎭 Generated mock content for document {document.id} ({'attachment' if is_attachment else 'page'})")
    return mock_metadata
