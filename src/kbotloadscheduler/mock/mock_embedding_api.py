"""
Mock pour KbotEmbeddingApi utilisé lorsque USE_MOCKS=true.

Ce module fournit une implémentation mock de l'API embedding pour permettre
le développement et les tests locaux sans dépendre du service réel.
"""

import os
from typing import List

from ..bean.beans import DocumentBean


class MockKbotEmbeddingApi:
    """Mock de KbotEmbeddingApi pour les tests et développement local."""

    def __init__(self, url: str, env: str = "local") -> None:
        self.url = url
        self.env = env

    def get_document_list(self, perimeter_code: str, domain_code: str, source_code: str) -> List[DocumentBean]:
        """
        Mock qui retourne une liste vide de documents embeddings.
        Cela permet au comparateur de traiter tous les documents du repository
        comme nouveaux documents à traiter.
        """
        print(f"[MOCK] Getting document list for perimeter={perimeter_code}, domain={domain_code}, source={source_code}")
        return []

    def embedd_document(self, perimeter_code: str, metadata_file: str):
        """Mock qui simule l'embedding d'un document."""
        print(f"[MOCK] Embedding document for perimeter={perimeter_code}, metadata_file={metadata_file}")
        return {"status": "ok", "document": None}

    def remove_document(self, perimeter_code: str, document: DocumentBean) -> bool:
        """Mock qui simule la suppression d'un document."""
        print(f"[MOCK] Removing document {document.id} for perimeter={perimeter_code}")
        return True

    @classmethod
    def as_document_bean(cls, document) -> DocumentBean:
        """Transforme le dictionnaire document en DocumentBean."""
        return DocumentBean.from_embedding_api(document)

    class EmbeddingException(Exception):
        """Exception mock pour compatibilité."""
        def __init__(self, error_message, response=None):
            super().__init__(error_message)


def should_use_mock() -> bool:
    """Vérifie si les mocks doivent être utilisés."""
    return os.getenv("USE_MOCKS", "false").lower() == "true"


def get_embedding_api_class():
    """
    Retourne la classe d'API embedding appropriée selon la configuration.

    Returns:
        MockKbotEmbeddingApi si USE_MOCKS=true, sinon KbotEmbeddingApi
    """
    if should_use_mock():
        print("[MOCK] Using MockKbotEmbeddingApi")
        return MockKbotEmbeddingApi
    else:
        from ..apicall.kbot_embedding_api import KbotEmbeddingApi
        return KbotEmbeddingApi
