"""
Configuration unifiée des mocks pour les tests locaux

Point d'entrée unique pour tous les mocks du système :
- GCS (Google Cloud Storage)
- Authentification (JWT, OKAPI)
- Futurs mocks...

Usage:
    Les mocks sont activés automatiquement selon USE_MOCKS :

    from kbotloadscheduler.mock import enable_all_mocks
    enable_all_mocks()

Avantages :
- Point d'entrée unifié
- Configuration centralisée
- Facilite l'ajout de nouveaux mocks
- Meilleure organisation du code
"""

import os
from typing import Optional


def enable_all_mocks() -> None:
    """
    Active tous les mocks nécessaires selon la configuration d'environnement

    Cette fonction vérifie USE_MOCKS et active automatiquement :
    - Mock GCS (Google Cloud Storage)
    - Mock d'authentification (JWT, OKAPI)
    - Futurs mocks selon les besoins

    Environment Variables:
        USE_MOCKS (str): "true" pour activer tous les mocks

    Returns:
        None

    Note:
        Cette fonction est safe à appeler même si USE_MOCKS=false
        Dans ce cas, aucun mock n'est activé.
    """
    should_enable_mocks = os.getenv("USE_MOCKS", "false").lower() == "true"

    if not should_enable_mocks:
        return

    print("🔧 Enabling unified mocking for local testing...")

    try:
        # Activer le mock GCS (temporairement depuis l'ancien emplacement)
        from .mock_gcs_config import enable_gcs_mocking
        enable_gcs_mocking()
        print("   ✅ GCS mocking enabled")

        # Activer le mock d'authentification
        from .mock_auth_config import enable_auth_mocking
        enable_auth_mocking()
        print("   ✅ Auth mocking enabled")

        print("✅ All mocks enabled successfully")

    except ImportError as e:
        print(f"⚠️ Warning: Could not enable some mocks: {e}")
        print("   This might be expected in some test environments")
    except Exception as e:
        print(f"❌ Error enabling mocks: {e}")
        print("   Continuing without mocks...")


def is_mocking_enabled() -> bool:
    """
    Vérifie si le mode mock est activé

    Returns:        bool: True si USE_MOCKS=true, False sinon
    """
    return os.getenv("USE_MOCKS", "false").lower() == "true"


def is_mocking_enabled_legacy_compatible() -> bool:
    """
    Version de compatibilité pour faciliter la migration depuis les checks directs.

    Cette fonction vérifie à la fois la nouvelle approche centralisée et l'ancienne
    approche directe pour s'assurer qu'aucun comportement n'est cassé pendant
    la migration.

    Returns:
        bool: True si le mode mock est activé (nouvelle ou ancienne méthode)

    Note:
        Cette fonction est temporaire et sera supprimée après migration complète.
        Utiliser is_mocking_enabled() pour les nouveaux développements.
    """
    # Nouvelle approche centralisée
    centralized_check = is_mocking_enabled()

    # Ancienne approche directe (pour compatibilité - sera supprimée)
    direct_check = os.getenv("USE_MOCKS", "false").lower() == "true"

    # Les deux doivent donner le même résultat
    if centralized_check != direct_check:
        print(f"⚠️ Warning: Mock check inconsistency - centralized: {centralized_check}, direct: {direct_check}")
        # En cas d'incohérence, utiliser la nouvelle approche
        return centralized_check

    return centralized_check


def get_mock_status() -> dict:
    """
    Retourne le statut actuel des mocks

    Returns:
        dict: Informations sur l'état des mocks
    """
    return {
        "mocking_enabled": is_mocking_enabled(),
        "environment_variable": os.getenv("USE_MOCKS", "false"),
        "available_mocks": [
            "GCS (Google Cloud Storage)",
            "Authentication (JWT, OKAPI)",
        ]
    }


def debug_mock_configuration() -> None:
    """
    Affiche des informations de debug sur la configuration des mocks

    Utile pour diagnostiquer les problèmes de configuration mock
    """
    print("🔍 Mock Configuration Debug")
    print("=" * 40)

    status = get_mock_status()
    for key, value in status.items():
        print(f"   {key}: {value}")

    print("\n🔧 Environment Variables:")
    relevant_vars = [
        "USE_MOCKS",  # Variable principale pour activer TOUS les mocks
        "ENV", "KBOT_WORK_BUCKET_PREFIX",
        "SKIP_EXTERNAL_AUTH", "GCP_PROJECT_ID"
    ]

    for var in relevant_vars:
        value = os.getenv(var, "Not set")
        print(f"   {var}: {value}")

    print("\n📁 Mock Modules Available:")
    try:
        from .mock_gcs_config import enable_gcs_mocking
        print("   ✅ GCS Mock: Available (in /gcs/ directory)")
    except ImportError:
        print("   ❌ GCS Mock: Not available")

    try:
        from .mock_auth_config import enable_auth_mocking
        print("   ✅ Auth Mock: Available (in /mock/ directory)")
    except ImportError:
        print("   ❌ Auth Mock: Not available")

    print("\n📂 Mock Files Distribution:")
    mock_files = list_mock_files()
    print(f"   Files in /mock/: {mock_files['files_in_mock']}")
    print(f"   Files outside /mock/: {mock_files['files_outside_mock']}")

    if mock_files['files_outside_mock']:
        print("   ⚠️  Some mock files are not in the /mock/ directory")
        print("   💡 Consider moving them for better organization")


def list_mock_files() -> dict:
    """
    Liste tous les fichiers de mock disponibles dans le système

    Returns:
        dict: Emplacement des fichiers de mock
    """
    import os

    mock_files = {
        "mock_directory": "/mock/",
        "files_in_mock": [],
        "files_outside_mock": []
    }

    # Vérifier les fichiers dans /mock/
    mock_dir = os.path.dirname(__file__)
    if os.path.exists(mock_dir):
        for file in os.listdir(mock_dir):
            if file.endswith('.py') and file != '__init__.py':
                mock_files["files_in_mock"].append(f"mock/{file}")

    # Vérifier les fichiers mock dans d'autres répertoires
    gcs_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "gcs")
    if os.path.exists(gcs_dir):
        for file in os.listdir(gcs_dir):
            if file.startswith('mock_') and file.endswith('.py'):
                mock_files["files_outside_mock"].append(f"gcs/{file}")

    return mock_files


# Expose les fonctions principales
__all__ = [
    "enable_all_mocks",
    "is_mocking_enabled",
    "is_mocking_enabled_legacy_compatible",
    "get_mock_status",
    "debug_mock_configuration",
    "list_mock_files"
]
