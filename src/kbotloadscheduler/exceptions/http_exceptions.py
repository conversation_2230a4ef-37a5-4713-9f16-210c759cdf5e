"""
Exceptions HTTP standardisées pour kbot-load-scheduler.

Ce module fournit des exceptions spécialisées pour la gestion des erreurs HTTP
avec classification automatique selon les codes de statut.
"""

from typing import Optional, Dict, Any, Union
import requests
from .base_exceptions import LoaderException, LoaderNetworkError


class HttpException(LoaderNetworkError):
    """
    Exception de base pour les erreurs HTTP.

    Cette classe encapsule les erreurs HTTP avec classification automatique
    selon le code de statut et support pour les objets Response.
    """

    # Codes de statut non critiques (ne nécessitent pas d'alerte)
    NON_CRITICAL_CODES = {404, 408, 410, 429}  # Not Found, Timeout, Gone, Rate Limit

    # Codes de statut retryables
    RETRYABLE_CODES = {429, 500, 502, 503, 504}  # Rate limit, Server errors

    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response: Optional[requests.Response] = None,
        url: Optional[str] = None,
        operation: Optional[str] = None,
        **kwargs
    ):
        """
        Initialise une HttpException.

        Args:
            message: Message d'erreur
            status_code: Code de statut HTTP
            response: Objet Response de requests
            url: URL de la requête
            **kwargs: Arguments supplémentaires pour LoaderNetworkError
        """
        # Extraire les informations de la response si fournie
        if response is not None:
            status_code = response.status_code
            url = url or response.url

            # Enrichir le message avec les détails de la response
            if response.status_code == 200:
                # Cas spécial : erreur dans une réponse 200 (ex: OAuth)
                try:
                    error_data = response.json()
                    if "error_description" in error_data:
                        message = f"{message}: {error_data['error_description']}"
                except (ValueError, KeyError):
                    pass
            else:
                message = f"{message} (HTTP {response.status_code})"

        # Déterminer la criticité et la possibilité de retry
        is_critical = status_code not in self.NON_CRITICAL_CODES if status_code else True
        is_retryable = status_code in self.RETRYABLE_CODES if status_code else False

        super().__init__(
            message,
            operation=operation or "http_request",
            **kwargs
        )

        # Override les valeurs par défaut de LoaderNetworkError
        self.is_critical = is_critical
        self.is_retryable = is_retryable
        self.resource = url

        self.status_code = status_code
        self.response = response
        self.url = url

        # Ajouter les informations au contexte
        if status_code:
            self.add_context("status_code", status_code)
        if url:
            self.add_context("url", url)
            # Also add as resource for consistency with tests
            self.add_context("resource", url)

        # Extraire les headers utiles
        if response and response.headers:
            retry_after = response.headers.get("Retry-After")
            if retry_after:
                self.add_context("retry_after", retry_after)

            rate_limit_remaining = response.headers.get("X-RateLimit-Remaining")
            if rate_limit_remaining:
                self.add_context("rate_limit_remaining", rate_limit_remaining)

    @classmethod
    def from_response(
        cls,
        response: requests.Response,
        message: Optional[str] = None,
        **kwargs
    ) -> "HttpException":
        """
        Crée une HttpException à partir d'un objet Response.

        Args:
            response: Objet Response de requests
            message: Message personnalisé (optionnel)
            **kwargs: Arguments supplémentaires

        Returns:
            HttpException appropriée selon le code de statut
        """
        url = response.url
        status_code = response.status_code

        if not message:
            message = f"HTTP request failed for {url}"

        # Retourner l'exception spécialisée selon le code de statut
        if status_code == 401:
            return HttpAuthenticationError(message, response=response, **kwargs)
        elif status_code == 403:
            return HttpPermissionError(message, response=response, **kwargs)
        elif status_code == 404:
            return HttpNotFoundError(message, response=response, **kwargs)
        elif status_code == 429:
            return HttpRateLimitError(message, response=response, **kwargs)
        elif status_code == 408 or status_code == 504:
            return HttpTimeoutError(message, response=response, **kwargs)
        elif 400 <= status_code < 500:
            return HttpClientError(message, response=response, **kwargs)
        elif 500 <= status_code < 600:
            return HttpServerError(message, response=response, **kwargs)
        else:
            return cls(message, response=response, **kwargs)


class HttpClientError(HttpException):
    """Erreur client HTTP (4xx)."""

    def __init__(self, message: str, **kwargs):
        kwargs.setdefault('operation', 'http_client_error')
        super().__init__(
            message,
            **kwargs
        )


class HttpServerError(HttpException):
    """Erreur serveur HTTP (5xx)."""

    def __init__(self, message: str, **kwargs):
        kwargs.setdefault('operation', 'http_server_error')
        super().__init__(
            message,
            **kwargs
        )
        # Override pour les erreurs serveur
        self.is_retryable = True


class HttpAuthenticationError(HttpClientError):
    """Erreur d'authentification HTTP (401)."""

    def __init__(self, message: str, **kwargs):
        # Ensure status_code is set to 401 if not provided
        if 'status_code' not in kwargs and 'response' not in kwargs:
            kwargs['status_code'] = 401
        kwargs.setdefault('operation', 'http_authentication')
        super().__init__(
            message,
            **kwargs
        )
        # Override pour les erreurs d'authentification
        self.is_retryable = False


class HttpPermissionError(HttpClientError):
    """Erreur de permissions HTTP (403)."""

    def __init__(self, message: str, **kwargs):
        # Ensure status_code is set to 403 if not provided
        if 'status_code' not in kwargs and 'response' not in kwargs:
            kwargs['status_code'] = 403
        kwargs.setdefault('operation', 'http_permission')
        super().__init__(
            message,
            **kwargs
        )
        # Override pour les erreurs de permission
        self.is_retryable = False


class HttpNotFoundError(HttpClientError):
    """Ressource non trouvée HTTP (404)."""

    def __init__(self, message: str, **kwargs):
        # Ensure status_code is set to 404 if not provided
        if 'status_code' not in kwargs and 'response' not in kwargs:
            kwargs['status_code'] = 404
        kwargs.setdefault('operation', 'http_not_found')
        super().__init__(
            message,
            **kwargs
        )
        # Override pour les erreurs 404
        self.is_critical = False
        self.is_retryable = False


class HttpRateLimitError(HttpClientError):
    """Erreur de rate limiting HTTP (429)."""

    def __init__(self, message: str, retry_after: Optional[int] = None, **kwargs):
        # Ensure status_code is set to 429 if not provided
        if 'status_code' not in kwargs and 'response' not in kwargs:
            kwargs['status_code'] = 429
        kwargs.setdefault('operation', 'http_rate_limit')
        super().__init__(
            message,
            **kwargs
        )
        # Override pour les erreurs de rate limit
        self.is_critical = False
        self.is_retryable = True

        if retry_after:
            self.add_context("retry_after", retry_after)


class HttpTimeoutError(HttpException):
    """Erreur de timeout HTTP (408, 504)."""

    def __init__(self, message: str, timeout_seconds: Optional[float] = None, **kwargs):
        # Ensure status_code is set to 408 if not provided
        if 'status_code' not in kwargs and 'response' not in kwargs:
            kwargs['status_code'] = 408
        kwargs.setdefault('operation', 'http_timeout')
        super().__init__(
            message,
            **kwargs
        )
        # Override pour les erreurs de timeout
        self.is_critical = False
        self.is_retryable = True

        if timeout_seconds:
            self.add_context("timeout_seconds", timeout_seconds)
