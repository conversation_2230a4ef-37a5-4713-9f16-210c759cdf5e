"""
Exceptions de base pour le système kbot-load-scheduler.

Ce module définit la hiérarchie d'exceptions de base utilisée par tous les loaders
et composants du système.
"""

from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


class LoaderException(Exception):
    """
    Exception de base pour tous les loaders.

    Cette classe fournit une structure commune pour toutes les exceptions
    du système avec support pour les informations contextuelles et la
    classification des erreurs.
    """

    def __init__(
        self,
        message: str,
        original_exception: Optional[Exception] = None,
        context: Optional[Dict[str, Any]] = None,
        is_critical: bool = True,
        is_retryable: bool = False,
        operation: Optional[str] = None,
        resource: Optional[str] = None,
    ):
        """
        Initialise une LoaderException.

        Args:
            message: Message d'erreur descriptif
            original_exception: Exception originale qui a causé cette erreur
            context: Informations contextuelles supplémentaires
            is_critical: Indique si l'erreur est critique
            is_retryable: Indique si l'opération peut être retentée
            operation: Nom de l'opération qui a échoué
            resource: Ressource concernée (URL, ID, etc.)
        """
        super().__init__(message)
        self.message = message
        self.original_exception = original_exception
        self.context = context or {}
        self.is_critical = is_critical
        self.is_retryable = is_retryable
        self.operation = operation or "loader_operation"
        self.resource = resource

        # Ajouter les informations non-None au contexte
        if self.operation:
            self.context["operation"] = self.operation
        if self.resource:
            self.context["resource"] = self.resource

        # Logger automatiquement l'erreur
        self._log_error()

    def __str__(self) -> str:
        """Représentation string de l'exception."""
        return self.message

    def get_context(self, key: Optional[str] = None, default: Any = None) -> Any:
        """
        Récupère une valeur du contexte ou tout le contexte.

        Args:
            key: Clé spécifique à récupérer. Si None, retourne tout le contexte.
            default: Valeur par défaut si la clé n'existe pas

        Returns:
            La valeur de la clé ou tout le dictionnaire de contexte
        """
        if key is None:
            return self.context.copy()
        return self.context.get(key, default)

    def add_context(self, key: str, value: Any) -> None:
        """Ajoute une information au contexte."""
        self.context[key] = value

    def _log_error(self) -> None:
        """Log automatiquement l'erreur selon sa classification."""
        log_message = self._get_detailed_message()
        if self.is_retryable and not self.is_critical:
            logger.info(log_message)
        elif self.is_critical:
            logger.error(log_message)
        else:
            logger.warning(log_message)

    def _get_detailed_message(self) -> str:
        """Retourne un message détaillé pour le logging."""
        parts = [self.message]

        if self.operation:
            parts.append(f"Operation: {self.operation}")

        if self.resource:
            parts.append(f"Resource: {self.resource}")

        if self.original_exception:
            parts.append(f"Caused by: {self.original_exception}")

        return " | ".join(parts)

    def log_error(self, logger_instance: Optional[logging.Logger] = None) -> None:
        """Log l'erreur avec le niveau approprié."""
        log = logger_instance or logger
        level = logging.ERROR if self.is_critical else logging.WARNING
        log.log(level, str(self))


class LoaderConfigurationError(LoaderException):
    """Erreur de configuration du loader."""

    def __init__(self, message: str, config_key: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            is_critical=True,
            is_retryable=False,
            operation="loader_configuration",
            **kwargs
        )
        if config_key:
            self.add_context("config_key", config_key)


class LoaderAuthenticationError(LoaderException):
    """Erreur d'authentification."""

    def __init__(self, message: str, auth_method: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            is_critical=True,
            is_retryable=False,
            operation="loader_authentication",
            **kwargs
        )
        if auth_method:
            self.add_context("auth_method", auth_method)


class LoaderPermissionError(LoaderException):
    """Erreur de permissions insuffisantes."""

    def __init__(self, message: str, required_permission: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            is_critical=True,
            is_retryable=False,
            operation="loader_permission",
            **kwargs
        )
        if required_permission:
            self.add_context("required_permission", required_permission)


class LoaderNetworkError(LoaderException):
    """Erreur réseau générique."""

    def __init__(self, message: str, operation: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            is_critical=False,
            is_retryable=True,
            operation=operation or "loader_network",
            **kwargs
        )


class LoaderTimeoutError(LoaderNetworkError):
    """Erreur de timeout."""

    def __init__(self, message: str, timeout_seconds: Optional[float] = None, **kwargs):
        super().__init__(
            message,
            operation="loader_timeout",
            **kwargs
        )
        if timeout_seconds:
            self.add_context("timeout_seconds", timeout_seconds)


class LoaderRateLimitError(LoaderNetworkError):
    """Erreur de rate limiting."""

    def __init__(self, message: str, retry_after: Optional[int] = None, **kwargs):
        super().__init__(
            message,
            operation="loader_rate_limit",
            **kwargs
        )
        if retry_after:
            self.add_context("retry_after", retry_after)


class LoaderNotFoundError(LoaderException):
    """Ressource non trouvée."""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message,
            is_critical=False,
            is_retryable=False,
            operation="loader_not_found",
            **kwargs
        )


class LoaderValidationError(LoaderException):
    """Erreur de validation des données."""

    def __init__(self, message: str, validation_field: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            is_critical=True,
            is_retryable=False,
            operation="loader_validation",
            **kwargs
        )
        if validation_field:
            self.add_context("validation_field", validation_field)


class LoaderCircuitBreakerError(LoaderException):
    """Erreur du circuit breaker."""

    def __init__(self, message: str, failure_count: Optional[int] = None, **kwargs):
        super().__init__(
            message,
            is_critical=False,
            is_retryable=True,
            operation="loader_circuit_breaker",
            **kwargs
        )
        if failure_count:
            self.add_context("failure_count", failure_count)
