"""
Module d'exceptions standardisées pour kbot-load-scheduler.

Ce module fournit une hiérarchie d'exceptions cohérente pour tous les loaders
et composants du système, avec support pour la classification des erreurs,
les codes de statut HTTP, et les informations contextuelles.
"""

from .base_exceptions import (
    LoaderException,
    LoaderConfigurationError,
    LoaderAuthenticationError,
    LoaderPermissionError,
    LoaderNetworkError,
    LoaderTimeoutError,
    LoaderRateLimitError,
    LoaderNotFoundError,
    LoaderValidationError,
    LoaderCircuitBreakerError,
)

from .http_exceptions import (
    HttpException,
    HttpClientError,
    HttpServerError,
    HttpAuthenticationError,
    HttpPermissionError,
    HttpNotFoundError,
    HttpRateLimitError,
    HttpTimeoutError,
)

from .confluence_exceptions import (
    ConfluenceException,
    ConfluenceClientException,
    ConfluenceAuthenticationError,
    ConfluencePermissionError,
    ConfluenceNotFoundError,
    ConfluenceRateLimitError,
    ConfluenceTimeoutError,
    ConfluenceConfigurationError,
)

from .sharepoint_exceptions import (
    SharepointException,
    SharepointClientException,
    SharepointAuthenticationError,
    SharepointPermissionError,
    SharepointNotFoundError,
    SharepointRateLimitError,
    SharepointTimeoutError,
)

from .basic_exceptions import (
    BasicException,
    BasicApiException,
    BasicAuthenticationError,
    BasicPermissionError,
    BasicNotFoundError,
    BasicRateLimitError,
    BasicTimeoutError,
)

__all__ = [
    # Base exceptions
    "LoaderException",
    "LoaderConfigurationError",
    "LoaderAuthenticationError",
    "LoaderPermissionError",
    "LoaderNetworkError",
    "LoaderTimeoutError",
    "LoaderRateLimitError",
    "LoaderNotFoundError",
    "LoaderValidationError",
    "LoaderCircuitBreakerError",
    
    # HTTP exceptions
    "HttpException",
    "HttpClientError",
    "HttpServerError",
    "HttpAuthenticationError",
    "HttpPermissionError",
    "HttpNotFoundError",
    "HttpRateLimitError",
    "HttpTimeoutError",
    
    # Confluence exceptions
    "ConfluenceException",
    "ConfluenceClientException",
    "ConfluenceAuthenticationError",
    "ConfluencePermissionError",
    "ConfluenceNotFoundError",
    "ConfluenceRateLimitError",
    "ConfluenceTimeoutError",
    "ConfluenceConfigurationError",
    
    # SharePoint exceptions
    "SharepointException",
    "SharepointClientException",
    "SharepointAuthenticationError",
    "SharepointPermissionError",
    "SharepointNotFoundError",
    "SharepointRateLimitError",
    "SharepointTimeoutError",
    
    # Basic exceptions
    "BasicException",
    "BasicApiException",
    "BasicAuthenticationError",
    "BasicPermissionError",
    "BasicNotFoundError",
    "BasicRateLimitError",
    "BasicTimeoutError",
]
