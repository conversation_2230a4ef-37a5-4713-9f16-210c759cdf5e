"""
Exceptions spécialisées pour le loader Confluence.

Ce module fournit des exceptions spécifiques au loader Confluence,
héritant du système d'exceptions standardisé.
"""

from typing import Optional, Dict, Any
import requests
from .base_exceptions import LoaderException
from .http_exceptions import HttpException


class ConfluenceException(LoaderException):
    """Exception de base pour le loader Confluence."""

    def __init__(self, message: str, **kwargs):
        # Extraire operation des kwargs pour éviter les conflits
        operation = kwargs.pop("operation", "confluence_operation")
        super().__init__(
            message,
            operation=operation,
            **kwargs
        )


class ConfluenceClientException(ConfluenceException):
    """
    Exception client Confluence - compatible avec l'ancienne ConfluenceClientException.

    Cette classe maintient la compatibilité avec l'API existante tout en
    bénéficiant du nouveau système d'exceptions standardisé.
    """

    def __init__(
        self,
        message: str,
        original_exception: Optional[Exception] = None,
        **kwargs
    ):
        # Allow operation to be overridden from kwargs, but remove it to avoid conflicts
        operation = kwargs.pop("operation", "confluence_client")
        # Extract known args to pass them explicitly
        resource = kwargs.pop('resource', None)
        context = kwargs.pop('context', None)
        is_critical = kwargs.pop('is_critical', None)
        is_retryable = kwargs.pop('is_retryable', None)

        # Prepare args for parent
        parent_kwargs = {}
        if original_exception is not None:
            parent_kwargs['original_exception'] = original_exception
        if resource is not None:
            parent_kwargs['resource'] = resource
        if context is not None:
            parent_kwargs['context'] = context
        if is_critical is not None:
            parent_kwargs['is_critical'] = is_critical
        if is_retryable is not None:
            parent_kwargs['is_retryable'] = is_retryable
        # Add remaining kwargs
        parent_kwargs.update(kwargs)

        super().__init__(
            message,
            operation=operation,
            **parent_kwargs
        )

    @classmethod
    def from_http_error(
        cls,
        response: requests.Response,
        message: Optional[str] = None,
        **kwargs
    ) -> "ConfluenceClientException":
        """
        Crée une ConfluenceClientException à partir d'une erreur HTTP.

        Args:
            response: Réponse HTTP
            message: Message personnalisé
            **kwargs: Arguments supplémentaires

        Returns:
            Exception Confluence appropriée
        """
        url = response.url
        status_code = response.status_code

        if not message:
            message = f"Confluence API call failed for {url}"

        # Créer l'exception HTTP correspondante
        http_exception = HttpException.from_response(response, message)

        # Helper to remove keys that are passed explicitly
        def clean_kwargs(kwargs):
            return {k: v for k, v in kwargs.items() if k not in ('resource', 'original_exception')}

        if status_code == 401:
            auth_kwargs = clean_kwargs(kwargs)
            resource_value = kwargs.get('resource', url)
            context = auth_kwargs.get('context', {})
            context['url'] = url
            context['status_code'] = status_code
            auth_kwargs['context'] = context
            return ConfluenceAuthenticationError(
                message,
                original_exception=http_exception,
                resource=resource_value,
                **auth_kwargs
            )
        elif status_code == 403:
            perm_kwargs = clean_kwargs(kwargs)
            resource_value = kwargs.get('resource', url)
            context = perm_kwargs.get('context', {})
            context['url'] = url
            context['status_code'] = status_code
            perm_kwargs['context'] = context
            return ConfluencePermissionError(
                message,
                original_exception=http_exception,
                resource=resource_value,
                **perm_kwargs
            )
        elif status_code == 404:
            notfound_kwargs = clean_kwargs(kwargs)
            resource_value = kwargs.get('resource', url)
            context = notfound_kwargs.get('context', {})
            context['url'] = url
            context['status_code'] = status_code
            notfound_kwargs['context'] = context
            return ConfluenceNotFoundError(
                message,
                original_exception=http_exception,
                resource=resource_value,
                **notfound_kwargs
            )
        elif status_code == 429:
            ratelimit_kwargs = clean_kwargs(kwargs)
            resource_value = kwargs.get('resource', url)
            context = ratelimit_kwargs.get('context', {})
            context['url'] = url
            context['status_code'] = status_code
            ratelimit_kwargs['context'] = context
            return ConfluenceRateLimitError(
                message,
                original_exception=http_exception,
                resource=resource_value,
                **ratelimit_kwargs
            )
        elif status_code in [408, 504]:
            timeout_kwargs = clean_kwargs(kwargs)
            resource_value = kwargs.get('resource', url)
            context = timeout_kwargs.get('context', {})
            context['url'] = url
            context['status_code'] = status_code
            timeout_kwargs['context'] = context
            return ConfluenceTimeoutError(
                message,
                original_exception=http_exception,
                resource=resource_value,
                **timeout_kwargs
            )
        else:
            client_kwargs = clean_kwargs(kwargs)
            resource_value = kwargs.get('resource', url)
            context = client_kwargs.get('context', {})
            context['url'] = url
            context['status_code'] = status_code
            client_kwargs['context'] = context
            return cls(
                message,
                original_exception=http_exception,
                resource=resource_value,
                **client_kwargs
            )

class ConfluenceAuthenticationError(ConfluenceClientException):
    """Erreur d'authentification Confluence."""

    def __init__(self, message: str = "Confluence authentication failed", auth_method: Optional[str] = None, **kwargs):
        # Allow operation to be overridden from kwargs
        operation = kwargs.pop("operation", "confluence_authentication")
        super().__init__(
            message,
            operation=operation,
            is_critical=True,
            is_retryable=False,
            **kwargs
        )
        if auth_method:
            self.add_context("auth_method", auth_method)
        # Add URL to context if resource is available and URL not already in context
        if self.resource and 'url' not in self.get_context():
            self.add_context("url", self.resource)


class ConfluencePermissionError(ConfluenceClientException):
    """Erreur de permissions Confluence."""

    def __init__(self, message: str = "Confluence permission denied", required_permission: Optional[str] = None, **kwargs):
        # Remove operation from kwargs since we're setting it explicitly
        kwargs.pop("operation", None)
        super().__init__(
            message,
            operation="confluence_permission",
            is_critical=True,
            is_retryable=False,
            **kwargs
        )
        if required_permission:
            self.add_context("required_permission", required_permission)
        # Add URL to context if resource is available and URL not already in context
        if self.resource and 'url' not in self.get_context():
            self.add_context("url", self.resource)


class ConfluenceNotFoundError(ConfluenceClientException):
    """Ressource Confluence non trouvée."""

    def __init__(self, message: str = "Confluence resource not found", resource_type: Optional[str] = None, **kwargs):
        # Remove operation from kwargs since we're setting it explicitly
        kwargs.pop("operation", None)
        super().__init__(
            message,
            operation="confluence_not_found",
            is_critical=False,
            is_retryable=False,
            **kwargs
        )
        if resource_type:
            self.add_context("resource_type", resource_type)
        # Add URL to context if resource is available and URL not already in context
        if self.resource and 'url' not in self.get_context():
            self.add_context("url", self.resource)


class ConfluenceRateLimitError(ConfluenceClientException):
    """Erreur de rate limiting Confluence."""

    def __init__(self, message: str = "Confluence rate limit exceeded", retry_after: Optional[int] = None, **kwargs):
        # Remove operation from kwargs since we're setting it explicitly
        kwargs.pop("operation", None)
        super().__init__(
            message,
            operation="confluence_rate_limit",
            is_critical=False,
            is_retryable=True,
            **kwargs
        )
        if retry_after:
            self.add_context("retry_after", retry_after)
        # Add URL to context if resource is available and URL not already in context
        if self.resource and 'url' not in self.get_context():
            self.add_context("url", self.resource)


class ConfluenceTimeoutError(ConfluenceClientException):
    """Erreur de timeout Confluence."""

    def __init__(self, message: str = "Confluence request timeout", timeout_seconds: Optional[float] = None, **kwargs):
        # Remove operation from kwargs since we're setting it explicitly
        kwargs.pop("operation", None)
        super().__init__(
            message,
            operation="confluence_timeout",
            is_critical=False,
            is_retryable=True,
            **kwargs
        )
        if timeout_seconds:
            self.add_context("timeout_seconds", timeout_seconds)
        # Add URL to context if resource is available and URL not already in context
        if self.resource and 'url' not in self.get_context():
            self.add_context("url", self.resource)


class ConfluenceConfigurationError(ConfluenceException):
    """Erreur de configuration Confluence."""

    def __init__(self, message: str = "Confluence configuration error", config_key: Optional[str] = None, **kwargs):
        # Remove operation from kwargs since we're setting it explicitly
        kwargs.pop("operation", None)
        super().__init__(
            message,
            operation="confluence_configuration",
            is_critical=True,
            is_retryable=False,
            **kwargs
        )
        if config_key:
            self.add_context("config_key", config_key)
