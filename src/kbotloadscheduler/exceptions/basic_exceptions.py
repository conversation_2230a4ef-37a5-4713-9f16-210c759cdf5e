"""
Exceptions spécialisées pour le loader Basic.

Ce module fournit des exceptions spécifiques au loader Basic,
héritant du système d'exceptions standardisé.
"""

from typing import Optional
import requests
from .base_exceptions import LoaderException
from .http_exceptions import HttpException


class BasicException(LoaderException):
    """Exception de base pour le loader Basic."""

    def __init__(self, message: str, **kwargs):
        operation = kwargs.pop("operation", "basic_operation")
        super().__init__(
            message,
            operation=operation,
            **kwargs
        )


class BasicApiException(BasicException):
    """
    Exception API Basic - compatible avec l'ancienne BasicApiException.

    Cette classe maintient la compatibilité avec l'API existante tout en
    bénéficiant du nouveau système d'exceptions standardisé.
    """

    def __init__(
        self,
        message: str = None,
        url: Optional[str] = None,
        response: Optional[requests.Response] = None,
        original_exception: Optional[Exception] = None,
        resource: Optional[str] = None,
        is_critical: Optional[bool] = None,
        is_retryable: Optional[bool] = None,
        **kwargs
    ):
        """
        Initialise une BasicApiException.

        Args:
            message: Message d'erreur (optionnel, sera généré si non fourni)
            url: URL de la requête
            response: Objet Response de requests
            original_exception: Exception originale
            resource: Ressource concernée
            is_critical: Criticité (optionnel, calculée automatiquement)
            is_retryable: Possibilité de retry (optionnel, calculée automatiquement)
            **kwargs: Arguments supplémentaires
        """
        # Remove operation and other conflicting args from kwargs to avoid conflicts
        operation = kwargs.pop('operation', None)
        context = kwargs.pop('context', {})
        # Remove any other potential conflicting kwargs
        kwargs.pop('is_critical', None)
        kwargs.pop('is_retryable', None)
        kwargs.pop('original_exception', None)
        kwargs.pop('resource', None)

        # Compatibilité avec l'ancienne API BasicApiException(url, response)
        if message is None and url is not None and response is not None:
            message = self._generate_message_from_response(url, response)

        # Extraire les informations de la response
        if response is not None:
            url = url or response.url
            status_code = response.status_code

            # Toutes les erreurs sont critiques dans l'ancienne implémentation
            if is_critical is None:
                is_critical = True
            if is_retryable is None:
                is_retryable = status_code in [429, 500, 502, 503, 504]
        else:
            status_code = None
            if is_critical is None:
                is_critical = True
            if is_retryable is None:
                is_retryable = False

        # Use resource or url for resource field
        final_resource = resource or url

        super().__init__(
            message or "Basic API operation failed",
            operation=operation or "basic_api",
            resource=final_resource,
            is_critical=is_critical,
            is_retryable=is_retryable,
            original_exception=original_exception,
            context=context,
            **kwargs
        )

        self.status_code = status_code
        self.response = response
        self.url = url

        # Ajouter les informations au contexte
        if status_code:
            self.add_context("status_code", status_code)
        if url:
            self.add_context("url", url)

    def _generate_message_from_response(self, url: str, response: requests.Response) -> str:
        """
        Génère un message d'erreur à partir de la response (logique héritée).

        Args:
            url: URL de la requête
            response: Réponse HTTP

        Returns:
            Message d'erreur formaté
        """
        error_message = f"Basic api call on url {url} failed: "
        error_message += f"[critical] status_code is {response.status_code}"
        return error_message

    @classmethod
    def from_response(
        cls,
        url: str,
        response: requests.Response,
        **kwargs
    ) -> "BasicApiException":
        """
        Crée une BasicApiException à partir d'une réponse HTTP.

        Args:
            url: URL de la requête
            response: Réponse HTTP
            **kwargs: Arguments supplémentaires

        Returns:
            Exception Basic appropriée
        """
        status_code = response.status_code

        # Retourner l'exception spécialisée selon le code de statut
        if status_code == 401:
            return BasicAuthenticationError(url=url, response=response, **kwargs)
        elif status_code == 403:
            return BasicPermissionError(url=url, response=response, **kwargs)
        elif status_code == 404:
            return BasicNotFoundError(url=url, response=response, **kwargs)
        elif status_code == 429:
            return BasicRateLimitError(url=url, response=response, **kwargs)
        elif status_code in [408, 504]:
            return BasicTimeoutError(url=url, response=response, **kwargs)
        else:
            return cls(url=url, response=response, **kwargs)


class BasicAuthenticationError(BasicException):
    """Erreur d'authentification Basic."""

    def __init__(self, message: str = None, **kwargs):
        if message is None:
            message = "Basic API authentication failed"

        super().__init__(
            message,
            operation="basic_authentication",
            is_critical=True,
            is_retryable=False,
            **kwargs
        )


class BasicPermissionError(BasicException):
    """Erreur de permissions Basic."""

    def __init__(self, message: str = None, **kwargs):
        if message is None:
            message = "Basic API permission denied"

        super().__init__(
            message,
            operation="basic_permission",
            is_critical=True,
            is_retryable=False,
            **kwargs
        )


class BasicNotFoundError(BasicException):
    """Ressource Basic non trouvée."""

    def __init__(self, message: str = None, **kwargs):
        if message is None:
            message = "Basic API resource not found"

        super().__init__(
            message,
            operation="basic_not_found",
            is_critical=True,  # Dans Basic, même les 404 sont critiques
            is_retryable=False,
            **kwargs
        )


class BasicRateLimitError(BasicException):
    """Erreur de rate limiting Basic."""

    def __init__(self, message: str = None, retry_after: Optional[int] = None, **kwargs):
        if message is None:
            message = "Basic API rate limit exceeded"

        super().__init__(
            message,
            operation="basic_rate_limit",
            is_critical=False,
            is_retryable=True,
            **kwargs
        )
        if retry_after:
            self.add_context("retry_after", retry_after)


class BasicTimeoutError(BasicException):
    """Erreur de timeout Basic."""

    def __init__(self, message: str = None, timeout_seconds: Optional[float] = None, **kwargs):
        if message is None:
            message = "Basic API request timeout"

        super().__init__(
            message,
            operation="basic_timeout",
            is_critical=False,
            is_retryable=True,
            **kwargs
        )
        if timeout_seconds:
            self.add_context("timeout_seconds", timeout_seconds)
