"""
Exceptions spécialisées pour le loader SharePoint.

Ce module fournit des exceptions spécifiques au loader SharePoint,
héritant du système d'exceptions standardisé.
"""

from typing import Optional
import requests
from .base_exceptions import LoaderException
from .http_exceptions import HttpException


class SharepointException(LoaderException):
    """
    Exception de base pour le loader SharePoint.

    Compatible avec l'ancienne SharepointException tout en bénéficiant
    du nouveau système d'exceptions standardisé.
    """

    # Codes de statut non critiques (hérités de l'ancienne implémentation)
    NOT_CRITICAL_CODES = [404]

    def __init__(
        self,
        message: str = None,
        url: Optional[str] = None,
        response: Optional[requests.Response] = None,
        original_exception: Optional[Exception] = None,
        resource: Optional[str] = None,
        is_critical: Optional[bool] = None,
        is_retryable: Optional[bool] = None,
        **kwargs
    ):
        """
        Initialise une SharepointException.

        Args:
            message: Message d'erreur (optionnel, sera généré si non fourni)
            url: URL de la requête
            response: Objet Response de requests
            original_exception: Exception originale
            resource: Ressource concernée
            is_critical: Criticité (optionnel, calculée automatiquement)
            is_retryable: Possibilité de retry (optionnel, calculée automatiquement)
            **kwargs: Arguments supplémentaires
        """
        # Remove operation and context from kwargs to avoid conflicts
        operation = kwargs.pop('operation', None)
        context = kwargs.pop('context', {})
        # Remove any other potential conflicting kwargs
        kwargs.pop('is_critical', None)
        kwargs.pop('is_retryable', None)
        kwargs.pop('original_exception', None)
        kwargs.pop('resource', None)

        # Compatibilité avec l'ancienne API SharepointException(url, response)
        if message is None and url is not None and response is not None:
            message = self._generate_message_from_response(url, response)

        # Extraire les informations de la response
        if response is not None:
            url = url or response.url
            status_code = response.status_code

            # Déterminer la criticité selon l'ancienne logique
            if is_critical is None:
                is_critical = status_code not in self.NOT_CRITICAL_CODES
            if is_retryable is None:
                is_retryable = status_code in [429, 500, 502, 503, 504]
        else:
            status_code = None
            if is_critical is None:
                is_critical = True
            if is_retryable is None:
                is_retryable = False

        # Use resource or url for resource field
        final_resource = resource or url

        super().__init__(
            message or "SharePoint operation failed",
            operation=operation or "sharepoint_operation",
            resource=final_resource,
            is_critical=is_critical,
            is_retryable=is_retryable,
            original_exception=original_exception,
            context=context,
            **kwargs
        )

        self.status_code = status_code
        self.response = response
        self.url = url

        # Ajouter les informations au contexte
        if status_code:
            self.add_context("status_code", status_code)
        if url:
            self.add_context("url", url)

    def _generate_message_from_response(self, url: str, response: requests.Response) -> str:
        """
        Génère un message d'erreur à partir de la response (logique héritée).

        Args:
            url: URL de la requête
            response: Réponse HTTP

        Returns:
            Message d'erreur formaté
        """
        error_message = f"Sharepoint call on url {url} failed: "

        if response.status_code == 200:
            try:
                error_data = response.json()
                error_message += error_data.get("error_description", "Unknown error")
            except (ValueError, KeyError):
                error_message += "Unknown error in 200 response"
        elif response.status_code in self.NOT_CRITICAL_CODES:
            error_message += f"status_code is {response.status_code}"
        else:
            error_message += f"[critical] status_code is {response.status_code}"

        return error_message

    @classmethod
    def from_response(
        cls,
        url: str,
        response: requests.Response,
        **kwargs
    ) -> "SharepointException":
        """
        Crée une SharepointException à partir d'une réponse HTTP.

        Args:
            url: URL de la requête
            response: Réponse HTTP
            **kwargs: Arguments supplémentaires

        Returns:
            Exception SharePoint appropriée
        """
        status_code = response.status_code

        # Retourner l'exception spécialisée selon le code de statut
        if status_code == 401:
            return SharepointAuthenticationError(url=url, response=response, **kwargs)
        elif status_code == 403:
            return SharepointPermissionError(url=url, response=response, **kwargs)
        elif status_code == 404:
            return SharepointNotFoundError(url=url, response=response, **kwargs)
        elif status_code == 429:
            return SharepointRateLimitError(url=url, response=response, **kwargs)
        elif status_code in [408, 504]:
            return SharepointTimeoutError(url=url, response=response, **kwargs)
        else:
            return cls(url=url, response=response, **kwargs)


class SharepointClientException(SharepointException):
    """Exception client SharePoint."""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message,
            operation="sharepoint_client",
            **kwargs
        )


class SharepointAuthenticationError(SharepointException):
    """Erreur d'authentification SharePoint."""

    def __init__(self, message: str = None, **kwargs):
        if message is None:
            message = "SharePoint authentication failed"

        # Allow operation to be overridden from kwargs
        operation = kwargs.pop("operation", "sharepoint_authentication")
        super().__init__(
            message,
            operation=operation,
            is_critical=True,
            is_retryable=False,
            **kwargs
        )


class SharepointPermissionError(SharepointException):
    """Erreur de permissions SharePoint."""

    def __init__(self, message: str = None, **kwargs):
        if message is None:
            message = "SharePoint permission denied"

        # Allow operation to be overridden from kwargs
        operation = kwargs.pop("operation", "sharepoint_permission")
        super().__init__(
            message,
            operation=operation,
            is_critical=True,
            is_retryable=False,
            **kwargs
        )


class SharepointNotFoundError(SharepointException):
    """Ressource SharePoint non trouvée."""

    def __init__(self, message: str = None, **kwargs):
        if message is None:
            message = "SharePoint resource not found"

        # Allow operation to be overridden from kwargs
        operation = kwargs.pop("operation", "sharepoint_not_found")
        super().__init__(
            message,
            operation=operation,
            is_critical=False,
            is_retryable=False,
            **kwargs
        )


class SharepointRateLimitError(SharepointException):
    """Erreur de rate limiting SharePoint."""

    def __init__(self, message: str = None, retry_after: Optional[int] = None, **kwargs):
        if message is None:
            message = "SharePoint rate limit exceeded"

        # Allow operation to be overridden from kwargs
        operation = kwargs.pop("operation", "sharepoint_rate_limit")
        super().__init__(
            message,
            operation=operation,
            is_critical=False,
            is_retryable=True,
            **kwargs
        )
        if retry_after:
            self.add_context("retry_after", retry_after)


class SharepointTimeoutError(SharepointException):
    """Erreur de timeout SharePoint."""

    def __init__(self, message: str = None, timeout_seconds: Optional[float] = None, **kwargs):
        if message is None:
            message = "SharePoint request timeout"

        # Allow operation to be overridden from kwargs
        operation = kwargs.pop("operation", "sharepoint_timeout")
        super().__init__(
            message,
            operation=operation,
            is_critical=False,
            is_retryable=True,
            **kwargs
        )
        if timeout_seconds:
            self.add_context("timeout_seconds", timeout_seconds)
