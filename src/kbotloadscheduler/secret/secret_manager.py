from functools import lru_cache
import os
import json
import logging
from dependency_injector.providers import Configuration
from google.cloud import secretmanager

logger = logging.getLogger(__name__)


class ConfigWithSecret(object):
    def __init__(self, config: Configuration) -> None:
        self.config = config
        self.gcp_project_id = self.config.get('gcp_project_id', '')
        self.secret_manager_client = None
        if self.config.get('env') != 'tests':
            self.secret_manager_client = secretmanager.SecretManagerServiceClient()

    def get_config(self) -> Configuration:
        return self.config

    def get_kbot_loadscheduler_client_id(self):
        key = self.config.get('kbot_loadscheduler_client_id')
        return self.retrieve_secret(key)

    def get_kbot_loadscheduler_client_secret(self):
        key = self.config.get('kbot_loadscheduler_client_secret')
        return self.retrieve_secret(key)

    def get_sharepoint_client_config(self, perimeter_code):
        key = perimeter_code + '-sharepoint-client-config'
        client_config = json.loads(self.retrieve_secret(key))
        return client_config

    def get_sharepoint_client_private_key(self, perimeter_code):
        key = perimeter_code + '-sharepoint-client-private-key'
        client_private_key = self.retrieve_secret(key)
        return client_private_key

    def get_basic_client_id(self):
        key = 'basic-client-id'
        return self.retrieve_secret(key)

    def get_basic_client_secret(self):
        key = 'basic-client-secret'
        return self.retrieve_secret(key)

    ### START: ADDED FOR CONFLUENCE ###

    def get_perimeter_confluence_credentials(self, perimeter_code: str) -> dict | None:
        """
        Gets Confluence credentials for a SPECIFIC perimeter.

        It constructs the secret name using the pattern:
        `{perimeter_code}-confluence-credentials`.

        Example: If perimeter_code is 'sandbox', it looks for a secret named
        'sandbox-confluence-credentials'.

        Args:
            perimeter_code: The perimeter identifier.

        Returns:
            A dictionary with credentials if found, otherwise None.
        """
        if not perimeter_code:
            logger.warning("Perimeter code is empty; cannot fetch perimeter-specific credentials.")
            return None

        secret_id = f"{perimeter_code}-confluence-credentials"
        logger.info("Getting perimeter-specific Confluence credentials for perimeter: '%s' using secret: '%s'", perimeter_code, secret_id)
        # Use the existing helper to fetch and parse the secret
        result = self._get_json_secret(secret_id)
        if result:
            logger.info("Successfully retrieved perimeter-specific Confluence credentials for perimeter: '%s'", perimeter_code)
        else:
            logger.warning("No perimeter-specific Confluence credentials found for perimeter: '%s'", perimeter_code)
        return result

    # --- This method is for the GLOBAL mode ---
    def get_global_confluence_credentials(self) -> dict:
        """
        Gets the global Confluence credentials.

        This method follows a specific priority order:
        1.  **Secret Manager**: It reads the name of the secret from the
            'confluence_secret_id' key in the main application configuration
            (e.g., config.yml).

        2.  **Environment Variables**: If the secret is not found via the first
            method, it falls back to looking for environment variables.
            The required variables are:
            - `CONFLUENCE_URL` (mandatory)
            - `CONFLUENCE_PAT_TOKEN` (preferred)
            - OR `CONFLUENCE_USERNAME` and `CONFLUENCE_API_TOKEN`

        Returns:
            A dictionary of credentials or an empty dict if none are found.
        """
        logger.info("Getting global Confluence credentials.")

        # 1. Try Secret Manager using a globally configured secret ID
        secret_id = self.config.get('confluence_secret_id')
        if secret_id:
            logger.info("Found confluence_secret_id configuration: '%s'. Attempting to retrieve global Confluence credentials.", secret_id)
            credentials = self._get_json_secret(secret_id)
            if credentials:
                logger.info("Successfully retrieved global Confluence credentials from Secret Manager using ID: '%s'", secret_id)
                return credentials
        else:
            logger.info("Config key 'confluence_secret_id' not set, skipping Secret Manager.")

        # 2. Fallback to Environment Variables
        logger.info("Attempting to retrieve Confluence credentials from environment variables as fallback.")
        env_creds = self._get_confluence_credentials_from_env()
        if env_creds:
            logger.info("Successfully retrieved Confluence credentials from environment variables as fallback.")
            return env_creds

        logger.error("Could not find any Confluence credentials in config or environment.")
        return {}

    def _get_json_secret(self, secret_id: str) -> dict | None:
        """
        A robust helper to retrieve and parse a JSON secret.
        """
        logger.info("Attempting to retrieve Confluence credentials from secret: '%s'", secret_id)
        try:
            secret_json = self.retrieve_secret(secret_id)
            if secret_json:
                logger.info("Successfully retrieved and parsed Confluence credentials from secret: '%s'", secret_id)
                return json.loads(secret_json)
            # If the secret is not found, retrieve_secret returns None or '', which is handled here.
            logger.warning("Secret with id '%s' was empty or not found.", secret_id)
            return None
        except json.JSONDecodeError as e:
            logger.error("JSON decode error for secret '%s': %s. Check format in secret store.", secret_id, e)
            return None
        except Exception as e:
            # Catch any other unexpected errors during retrieval or parsing.
            logger.error("Unexpected error retrieving JSON secret '%s': %s", secret_id, e)
            return None

    def _get_confluence_credentials_from_env(self) -> dict | None:
        """
        Gets a complete set of Confluence credentials from environment variables,
        including the mandatory URL.
        """
        logger.debug("Checking for Confluence credentials in environment variables.")
        url = os.getenv("CONFLUENCE_URL")
        if not url:
            logger.debug("CONFLUENCE_URL environment variable not set.")
            return None # URL is mandatory for credentials to be valid.

        logger.info("Found CONFLUENCE_URL in environment variables: '%s'", url)
        creds = {
            "url": url,
            "cloud": os.getenv("CONFLUENCE_CLOUD", "true").lower() == "true"
        }

        # Check for PAT token first (preferred)
        if pat_token := os.getenv("CONFLUENCE_PAT_TOKEN"):
            logger.info("Using CONFLUENCE_PAT_TOKEN from environment variables for authentication.")
            creds["pat_token"] = pat_token
            return creds

        # Then check for username/api_token pair
        if (username := os.getenv("CONFLUENCE_USERNAME")) and (api_token := os.getenv("CONFLUENCE_API_TOKEN")):
            logger.info("Using CONFLUENCE_USERNAME and CONFLUENCE_API_TOKEN from environment variables for authentication.")
            creds["username"] = username
            creds["api_token"] = api_token
            return creds

        logger.warning("CONFLUENCE_URL is set, but no valid auth method (PAT or user/token) was found in env vars.")
        return None

    ### END: ADDED FOR CONFLUENCE ###

    @lru_cache
    def retrieve_secret(self, secret_id):
        try:
            secret_value = self.retrieve_secret_from_file(secret_id)
        except OSError:
            secret_value = None
        if secret_value is None:
            secret_value = self.retrieve_secret_directly(secret_id)
        return secret_value

    def retrieve_secret_from_file(self, secret_id):
        secret_path = os.path.join(self.config.get('path_to_secret_config', '/etc/secrets'), secret_id, 'secret')
        logger.debug("Looking for secret file at path: '%s'", secret_path)
        if os.path.exists(secret_path):
            logger.info("Found and loading secret from file: '%s'", secret_path)
            with open(secret_path) as f:
                content = f.read()
            return content.strip(" \n\r\t")
        logger.debug("Secret file not found at path: '%s'", secret_path)
        return None

    def retrieve_secret_directly(self, secret_id):
        if self.secret_manager_client is None:
            logger.debug("No secret manager client available, returning empty string for secret: '%s'", secret_id)
            return ''
        logger.info("Attempting to retrieve secret '%s' from Google Secret Manager", secret_id)
        secret_resource_name = f"projects/{self.gcp_project_id}/secrets/{secret_id}/versions/latest"
        secret_version_response = self.secret_manager_client.access_secret_version(
            name=secret_resource_name
        )
        logger.info("Successfully retrieved secret '%s' from Google Secret Manager", secret_id)
        return secret_version_response.payload.data.decode("UTF-8").strip(" \n\r\t")
