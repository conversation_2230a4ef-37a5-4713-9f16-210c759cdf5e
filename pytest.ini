[pytest]
pythonpath = src tests

# Configuration des répertoires de sortie
addopts =
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings
    # Répertoires de sortie dans le projet
    --basetemp=./tmp/pytest

markers =
    unit: Tests unitaires rapides
    integration: Tests d'intégration
    performance: Tests de performance
    security: Tests de sécurité
    tracking: Tests du système de suivi des changements
    gcs: Tests nécessitant Google Cloud Storage
    filesystem: Tests utilisant le stockage filesystem
    slow: Tests lents (>5s)
    network: Tests nécessitant le réseau
    confluence: Tests spécifiques à Confluence

filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*__fields__.*:UserWarning
