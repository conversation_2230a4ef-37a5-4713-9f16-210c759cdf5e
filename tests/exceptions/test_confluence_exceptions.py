"""
Tests unitaires pour les exceptions Confluence.

Ce module teste les exceptions spécialisées pour le loader Confluence,
vérifiant la compatibilité avec l'ancienne API et les factory methods.
"""

import pytest
import requests
from unittest.mock import Mock

from kbotloadscheduler.exceptions.confluence_exceptions import (
    ConfluenceException,
    ConfluenceClientException,
    ConfluenceAuthenticationError,
    ConfluencePermissionError,
    ConfluenceNotFoundError,
    ConfluenceRateLimitError,
    ConfluenceTimeoutError,
    ConfluenceConfigurationError,
)


class TestConfluenceException:
    """Tests pour la classe ConfluenceException de base."""

    def test_initialization(self):
        """Test l'initialisation de ConfluenceException."""
        exception = ConfluenceException("Confluence error")

        assert str(exception) == "Confluence error"
        assert exception.operation == "confluence_operation"


class TestConfluenceClientException:
    """Tests pour ConfluenceClientException."""

    def test_basic_initialization(self):
        """Test l'initialisation basique (compatibilité ancienne API)."""
        exception = ConfluenceClientException("Client error")

        assert str(exception) == "Client error"
        assert exception.operation == "confluence_client"
        assert exception.original_exception is None

    def test_initialization_with_original_exception(self):
        """Test l'initialisation avec exception originale."""
        original_exc = ValueError("Original error")
        exception = ConfluenceClientException(
            "Client error",
            original_exception=original_exc
        )

        assert str(exception) == "Client error"
        assert exception.original_exception is original_exc

    def test_from_http_error_factory_method(self):
        """Test la factory method from_http_error."""
        # Mock response
        response = Mock(spec=requests.Response)
        response.status_code = 401
        response.url = "https://confluence.example.com/rest/api/content"
        response.headers = {}

        exception = ConfluenceClientException.from_http_error(response)

        assert isinstance(exception, ConfluenceAuthenticationError)
        assert "Confluence API call failed" in str(exception)
        assert exception.get_context()["url"] == response.url

    def test_from_http_error_with_custom_message(self):
        """Test from_http_error avec message personnalisé."""
        response = Mock(spec=requests.Response)
        response.status_code = 500
        response.url = "https://confluence.example.com/rest/api/content"
        response.headers = {}

        exception = ConfluenceClientException.from_http_error(
            response,
            "Custom error message"
        )

        assert "Custom error message" in str(exception)

    def test_from_http_error_403_permission(self):
        """Test from_http_error pour erreur 403."""
        response = Mock(spec=requests.Response)
        response.status_code = 403
        response.url = "https://confluence.example.com/rest/api/content"
        response.headers = {}

        exception = ConfluenceClientException.from_http_error(response)

        assert isinstance(exception, ConfluencePermissionError)

    def test_from_http_error_404_not_found(self):
        """Test from_http_error pour erreur 404."""
        response = Mock(spec=requests.Response)
        response.status_code = 404
        response.url = "https://confluence.example.com/rest/api/content"
        response.headers = {}

        exception = ConfluenceClientException.from_http_error(response)

        assert isinstance(exception, ConfluenceNotFoundError)

    def test_from_http_error_429_rate_limit(self):
        """Test from_http_error pour erreur 429."""
        response = Mock(spec=requests.Response)
        response.status_code = 429
        response.url = "https://confluence.example.com/rest/api/content"
        response.headers = {"Retry-After": "60"}

        exception = ConfluenceClientException.from_http_error(response)

        assert isinstance(exception, ConfluenceRateLimitError)

    def test_from_http_error_timeout_codes(self):
        """Test from_http_error pour codes de timeout."""
        timeout_codes = [408, 504]

        for code in timeout_codes:
            response = Mock(spec=requests.Response)
            response.status_code = code
            response.url = "https://confluence.example.com/rest/api/content"
            response.headers = {}

            exception = ConfluenceClientException.from_http_error(response)

            assert isinstance(exception, ConfluenceTimeoutError)

    def test_from_http_error_generic_error(self):
        """Test from_http_error pour erreur générique."""
        response = Mock(spec=requests.Response)
        response.status_code = 422
        response.url = "https://confluence.example.com/rest/api/content"
        response.headers = {}

        exception = ConfluenceClientException.from_http_error(response)

        assert isinstance(exception, ConfluenceClientException)
        assert not isinstance(exception, ConfluenceAuthenticationError)


class TestConfluenceAuthenticationError:
    """Tests pour ConfluenceAuthenticationError."""

    def test_initialization(self):
        """Test l'initialisation de ConfluenceAuthenticationError."""
        exception = ConfluenceAuthenticationError("Auth failed")

        assert str(exception) == "Auth failed"
        assert exception.operation == "confluence_authentication"
        assert exception.is_critical is True
        assert exception.is_retryable is False

    def test_default_message(self):
        """Test le message par défaut."""
        exception = ConfluenceAuthenticationError()

        assert "Confluence authentication failed" in str(exception)

    def test_with_auth_method(self):
        """Test avec méthode d'authentification."""
        exception = ConfluenceAuthenticationError(
            "Auth failed",
            auth_method="pat_token"
        )

        context = exception.get_context()
        assert context["auth_method"] == "pat_token"


class TestConfluencePermissionError:
    """Tests pour ConfluencePermissionError."""

    def test_initialization(self):
        """Test l'initialisation de ConfluencePermissionError."""
        exception = ConfluencePermissionError("Permission denied")

        assert str(exception) == "Permission denied"
        assert exception.operation == "confluence_permission"
        assert exception.is_critical is True
        assert exception.is_retryable is False

    def test_default_message(self):
        """Test le message par défaut."""
        exception = ConfluencePermissionError()

        assert "Confluence permission denied" in str(exception)

    def test_with_required_permission(self):
        """Test avec permission requise."""
        exception = ConfluencePermissionError(
            "Permission denied",
            required_permission="read"
        )

        context = exception.get_context()
        assert context["required_permission"] == "read"


class TestConfluenceNotFoundError:
    """Tests pour ConfluenceNotFoundError."""

    def test_initialization(self):
        """Test l'initialisation de ConfluenceNotFoundError."""
        exception = ConfluenceNotFoundError("Page not found")

        assert str(exception) == "Page not found"
        assert exception.operation == "confluence_not_found"
        assert exception.is_critical is False
        assert exception.is_retryable is False

    def test_default_message(self):
        """Test le message par défaut."""
        exception = ConfluenceNotFoundError()

        assert "Confluence resource not found" in str(exception)

    def test_with_resource_type(self):
        """Test avec type de ressource."""
        exception = ConfluenceNotFoundError(
            "Resource not found",
            resource_type="page"
        )

        context = exception.get_context()
        assert context["resource_type"] == "page"


class TestConfluenceRateLimitError:
    """Tests pour ConfluenceRateLimitError."""

    def test_initialization(self):
        """Test l'initialisation de ConfluenceRateLimitError."""
        exception = ConfluenceRateLimitError("Rate limit exceeded")

        assert str(exception) == "Rate limit exceeded"
        assert exception.operation == "confluence_rate_limit"
        assert exception.is_critical is False
        assert exception.is_retryable is True

    def test_default_message(self):
        """Test le message par défaut."""
        exception = ConfluenceRateLimitError()

        assert "Confluence rate limit exceeded" in str(exception)

    def test_with_retry_after(self):
        """Test avec retry_after."""
        exception = ConfluenceRateLimitError("Rate limit exceeded", retry_after=60)

        context = exception.get_context()
        assert context["retry_after"] == 60


class TestConfluenceTimeoutError:
    """Tests pour ConfluenceTimeoutError."""

    def test_initialization(self):
        """Test l'initialisation de ConfluenceTimeoutError."""
        exception = ConfluenceTimeoutError("Request timeout")

        assert str(exception) == "Request timeout"
        assert exception.operation == "confluence_timeout"
        assert exception.is_critical is False
        assert exception.is_retryable is True

    def test_default_message(self):
        """Test le message par défaut."""
        exception = ConfluenceTimeoutError()

        assert "Confluence request timeout" in str(exception)

    def test_with_timeout_seconds(self):
        """Test avec timeout en secondes."""
        exception = ConfluenceTimeoutError("Request timeout", timeout_seconds=30.0)

        context = exception.get_context()
        assert context["timeout_seconds"] == 30.0


class TestConfluenceConfigurationError:
    """Tests pour ConfluenceConfigurationError."""

    def test_initialization(self):
        """Test l'initialisation de ConfluenceConfigurationError."""
        exception = ConfluenceConfigurationError("Config error")

        assert str(exception) == "Config error"
        assert exception.operation == "confluence_configuration"
        assert exception.is_critical is True
        assert exception.is_retryable is False

    def test_default_message(self):
        """Test le message par défaut."""
        exception = ConfluenceConfigurationError()

        assert "Confluence configuration error" in str(exception)

    def test_with_config_key(self):
        """Test avec clé de configuration."""
        exception = ConfluenceConfigurationError("Config error", config_key="spaces")

        context = exception.get_context()
        assert context["config_key"] == "spaces"
