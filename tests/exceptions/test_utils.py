"""
Tests unitaires pour les utilitaires d'exceptions.

Ce module teste les fonctions utilitaires du système d'exceptions,
incluant les factory methods et les utilitaires de migration.
"""

import pytest
import requests
from unittest.mock import Mock

from kbotloadscheduler.exceptions.utils import (
    create_http_exception,
    is_retryable_exception,
    migrate_legacy_exception,
    extract_error_details,
)
from kbotloadscheduler.exceptions import (
    LoaderException,
    ConfluenceClientException,
    ConfluenceAuthenticationError,
    SharepointException,
    BasicApiException,
    HttpException,
    HttpAuthenticationError,
)


class TestCreateHttpException:
    """Tests pour la fonction create_http_exception."""

    def test_create_confluence_exception(self):
        """Test création d'exception Confluence."""
        response = Mock(spec=requests.Response)
        response.status_code = 401
        response.url = "https://confluence.example.com/rest/api/content"
        response.headers = {}

        exception = create_http_exception(response, "confluence")

        assert isinstance(exception, ConfluenceAuthenticationError)
        context = exception.get_context()
        assert context["url"] == response.url

    def test_create_sharepoint_exception(self):
        """Test création d'exception SharePoint."""
        response = Mock(spec=requests.Response)
        response.status_code = 404
        response.url = "https://sharepoint.example.com/api/files"

        exception = create_http_exception(
            response,
            "sharepoint",
            url="https://sharepoint.example.com/api/files"
        )

        assert isinstance(exception, SharepointException)
        assert exception.get_context()["url"] == response.url

    def test_create_basic_exception(self):
        """Test création d'exception Basic."""
        response = Mock(spec=requests.Response)
        response.status_code = 500
        response.url = "https://basic.example.com/api/data"

        exception = create_http_exception(
            response,
            "basic",
            url="https://basic.example.com/api/data"
        )

        assert isinstance(exception, BasicApiException)
        assert exception.get_context()["url"] == response.url

    def test_create_generic_http_exception(self):
        """Test création d'exception HTTP générique."""
        response = Mock(spec=requests.Response)
        response.status_code = 401
        response.url = "https://api.example.com/test"

        exception = create_http_exception(response, "unknown")

        assert isinstance(exception, HttpAuthenticationError)
        assert exception.get_context()["url"] == response.url

    def test_create_exception_with_custom_message(self):
        """Test création d'exception avec message personnalisé."""
        response = Mock(spec=requests.Response)
        response.status_code = 500
        response.url = "https://api.example.com/test"

        exception = create_http_exception(
            response,
            "confluence",
            message="Custom error message"
        )

        assert "Custom error message" in str(exception)

    def test_create_exception_with_kwargs(self):
        """Test création d'exception avec arguments supplémentaires."""
        response = Mock(spec=requests.Response)
        response.status_code = 401
        response.url = "https://confluence.example.com/rest/api/content"

        exception = create_http_exception(
            response,
            "confluence",
            auth_method="pat_token",
            operation="get_page"
        )

        context = exception.get_context()
        assert context["auth_method"] == "pat_token"
        assert context["operation"] == "get_page"


class TestIsRetryableException:
    """Tests pour la fonction is_retryable_exception."""

    def test_retryable_exception(self):
        """Test détection d'exception retryable."""
        exception = LoaderException(
            "Retryable error",
            is_retryable=True
        )

        assert is_retryable_exception(exception) is True

    def test_non_retryable_exception(self):
        """Test détection d'exception non-retryable."""
        exception = LoaderException(
            "Non-retryable error",
            is_retryable=False
        )

        assert is_retryable_exception(exception) is False

    def test_exception_without_is_retryable_attribute(self):
        """Test exception sans attribut is_retryable."""
        exception = ValueError("Standard exception")

        assert is_retryable_exception(exception) is False

    def test_none_exception(self):
        """Test avec None."""
        assert is_retryable_exception(None) is False


class TestMigrateLegacyException:
    """Tests pour la fonction migrate_legacy_exception."""

    def test_migrate_to_confluence_exception(self):
        """Test migration vers exception Confluence."""
        legacy_exception = ValueError("Legacy error")

        new_exception = migrate_legacy_exception(
            legacy_exception,
            "confluence",
            operation="get_page",
            resource="page_123"
        )

        assert isinstance(new_exception, ConfluenceClientException)
        assert "Legacy error" in str(new_exception)
        assert new_exception.original_exception is legacy_exception
        assert new_exception.get_context()["operation"] == "get_page"
        assert new_exception.get_context()["resource"] == "page_123"

    def test_migrate_to_sharepoint_exception(self):
        """Test migration vers exception SharePoint."""
        legacy_exception = ConnectionError("Connection failed")

        new_exception = migrate_legacy_exception(
            legacy_exception,
            "sharepoint",
            operation="get_file",
            resource="file_456"
        )

        assert isinstance(new_exception, SharepointException)
        assert "Connection failed" in str(new_exception)
        assert new_exception.original_exception is legacy_exception

    def test_migrate_to_basic_exception(self):
        """Test migration vers exception Basic."""
        legacy_exception = TimeoutError("Request timeout")

        new_exception = migrate_legacy_exception(
            legacy_exception,
            "basic",
            operation="get_data",
            resource="data_789"
        )

        assert isinstance(new_exception, BasicApiException)
        assert "Request timeout" in str(new_exception)
        assert new_exception.original_exception is legacy_exception

    def test_migrate_to_generic_loader_exception(self):
        """Test migration vers exception LoaderException générique."""
        legacy_exception = RuntimeError("Runtime error")

        new_exception = migrate_legacy_exception(
            legacy_exception,
            "unknown",
            operation="unknown_operation",
            resource="unknown_resource"
        )

        assert isinstance(new_exception, LoaderException)
        assert "Runtime error" in str(new_exception)
        assert new_exception.original_exception is legacy_exception


class TestExtractErrorDetails:
    """Tests pour la fonction extract_error_details."""

    def test_extract_from_response_with_json(self):
        """Test extraction depuis response avec JSON."""
        response = Mock(spec=requests.Response)
        response.status_code = 400
        response.headers = {"Content-Type": "application/json"}
        response.json.return_value = {
            "error": "Bad Request",
            "message": "Invalid parameter",
            "code": "INVALID_PARAM"
        }

        details = extract_error_details(response)

        assert details["status_code"] == 400
        assert details["error_type"] == "Bad Request"
        assert details["error_message"] == "Invalid parameter"
        assert details["error_code"] == "INVALID_PARAM"

    def test_extract_from_response_with_text(self):
        """Test extraction depuis response avec texte."""
        response = Mock(spec=requests.Response)
        response.status_code = 500
        response.headers = {"Content-Type": "text/html"}
        response.json.side_effect = ValueError("No JSON")
        response.text = "Internal Server Error"

        details = extract_error_details(response)

        assert details["status_code"] == 500
        assert details["error_message"] == "Internal Server Error"
        assert "error_type" not in details

    def test_extract_from_response_with_retry_after(self):
        """Test extraction avec header Retry-After."""
        response = Mock(spec=requests.Response)
        response.status_code = 429
        response.headers = {
            "Content-Type": "application/json",
            "Retry-After": "60"
        }
        response.json.return_value = {"error": "Rate limit exceeded"}

        details = extract_error_details(response)

        assert details["status_code"] == 429
        assert details["retry_after"] == "60"

    def test_extract_from_response_with_exception(self):
        """Test extraction avec exception lors du parsing JSON."""
        response = Mock(spec=requests.Response)
        response.status_code = 400
        response.headers = {"Content-Type": "application/json"}
        response.json.side_effect = ValueError("Invalid JSON")
        response.text = "Bad Request"

        details = extract_error_details(response)

        assert details["status_code"] == 400
        assert details["error_message"] == "Bad Request"

    def test_extract_from_none_response(self):
        """Test extraction avec response None."""
        details = extract_error_details(None)

        assert details == {}

    def test_extract_from_response_without_content_type(self):
        """Test extraction sans Content-Type."""
        response = Mock(spec=requests.Response)
        response.status_code = 404
        response.headers = {}
        response.json.side_effect = ValueError("No JSON")
        response.text = "Not Found"

        details = extract_error_details(response)

        assert details["status_code"] == 404
        assert details["error_message"] == "Not Found"


class TestIntegration:
    """Tests d'intégration pour le système d'exceptions."""

    def test_end_to_end_confluence_workflow(self):
        """Test workflow complet Confluence."""
        # Simuler une réponse d'erreur Confluence
        response = Mock(spec=requests.Response)
        response.status_code = 401
        response.url = "https://confluence.example.com/rest/api/content/123"
        response.headers = {"Content-Type": "application/json"}
        response.json.return_value = {
            "statusCode": 401,
            "message": "Unauthorized"
        }

        # Créer exception via factory method
        exception = create_http_exception(
            response,
            "confluence",
            operation="get_page",
            resource="page_123"
        )

        # Vérifier le type et les propriétés
        assert isinstance(exception, ConfluenceAuthenticationError)
        assert exception.is_critical is True
        assert exception.is_retryable is False
        assert not is_retryable_exception(exception)

        # Vérifier le contexte
        context = exception.get_context()
        assert context["url"] == response.url
        assert context["operation"] == "get_page"
        assert context["resource"] == "page_123"
        assert context["status_code"] == 401

    def test_end_to_end_sharepoint_workflow(self):
        """Test workflow complet SharePoint."""
        # Simuler une réponse d'erreur SharePoint
        response = Mock(spec=requests.Response)
        response.status_code = 404
        response.url = "https://sharepoint.example.com/api/files/test.docx"

        # Créer exception via factory method
        exception = create_http_exception(
            response,
            "sharepoint",
            url="https://sharepoint.example.com/api/files/test.docx",
            operation="get_file",
            resource="test.docx"
        )

        # Vérifier le type et les propriétés (404 non-critique pour SharePoint)
        assert isinstance(exception, SharepointException)
        assert exception.is_critical is False  # 404 non-critique pour SharePoint
        assert exception.is_retryable is False
        assert not is_retryable_exception(exception)

    def test_migration_and_retry_detection(self):
        """Test migration d'exception legacy et détection retry."""
        # Exception legacy
        legacy_exception = ConnectionError("Network connection failed")

        # Migration vers nouvelle exception
        new_exception = migrate_legacy_exception(
            legacy_exception,
            "confluence",
            operation="sync_pages",
            is_retryable=True
        )

        # Vérifier migration
        assert isinstance(new_exception, ConfluenceClientException)
        assert new_exception.original_exception is legacy_exception
        assert "Network connection failed" in str(new_exception)

        # Vérifier détection retry
        assert is_retryable_exception(new_exception) is True
