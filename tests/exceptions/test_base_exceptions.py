"""
Tests unitaires pour les exceptions de base.

Ce module teste la classe LoaderException et ses sous-classes,
vérifiant la classification automatique, le contexte, et le logging.
"""

import logging
import pytest
from unittest.mock import Mock, patch

from kbotloadscheduler.exceptions.base_exceptions import (
    LoaderException,
    LoaderConfigurationError,
    LoaderAuthenticationError,
    LoaderPermissionError,
    LoaderNetworkError,
    LoaderTimeoutError,
    LoaderRateLimitError,
    LoaderNotFoundError,
    LoaderValidationError,
    LoaderCircuitBreakerError,
)


class TestLoaderException:
    """Tests pour la classe LoaderException de base."""

    def test_basic_initialization(self):
        """Test l'initialisation basique d'une LoaderException."""
        exception = LoaderException("Test message")
        
        assert str(exception) == "Test message"
        assert exception.operation == "loader_operation"
        assert exception.resource is None
        assert exception.is_critical is True  # Par défaut
        assert exception.is_retryable is False  # Par défaut
        assert exception.original_exception is None

    def test_initialization_with_parameters(self):
        """Test l'initialisation avec tous les paramètres."""
        original_exc = ValueError("Original error")
        
        exception = LoaderException(
            "Test message",
            operation="test_operation",
            resource="test_resource",
            is_critical=False,
            is_retryable=True,
            original_exception=original_exc
        )
        
        assert str(exception) == "Test message"
        assert exception.operation == "test_operation"
        assert exception.resource == "test_resource"
        assert exception.is_critical is False
        assert exception.is_retryable is True
        assert exception.original_exception is original_exc

    def test_context_management(self):
        """Test la gestion du contexte."""
        exception = LoaderException("Test message")
        
        # Ajouter du contexte
        exception.add_context("key1", "value1")
        exception.add_context("key2", 42)
        
        context = exception.get_context()
        assert context["key1"] == "value1"
        assert context["key2"] == 42
        assert context["operation"] == "loader_operation"

    def test_context_with_none_values(self):
        """Test que les valeurs None ne sont pas ajoutées au contexte."""
        exception = LoaderException("Test message", resource=None)
        
        context = exception.get_context()
        assert "resource" not in context
        assert context["operation"] == "loader_operation"

    @patch('kbotloadscheduler.exceptions.base_exceptions.logger')
    def test_logging_critical_error(self, mock_logger):
        """Test que les erreurs critiques sont loggées en ERROR."""
        LoaderException("Critical error", is_critical=True)
        
        mock_logger.error.assert_called_once()
        args = mock_logger.error.call_args[0]
        assert "Critical error" in args[0]

    @patch('kbotloadscheduler.exceptions.base_exceptions.logger')
    def test_logging_non_critical_error(self, mock_logger):
        """Test que les erreurs non-critiques sont loggées en WARNING."""
        LoaderException("Non-critical error", is_critical=False)
        
        mock_logger.warning.assert_called_once()
        args = mock_logger.warning.call_args[0]
        assert "Non-critical error" in args[0]

    @patch('kbotloadscheduler.exceptions.base_exceptions.logger')
    def test_logging_retryable_error(self, mock_logger):
        """Test que les erreurs retryables sont loggées en INFO."""
        LoaderException("Retryable error", is_retryable=True, is_critical=False)
        
        mock_logger.info.assert_called_once()
        args = mock_logger.info.call_args[0]
        assert "Retryable error" in args[0]


class TestLoaderConfigurationError:
    """Tests pour LoaderConfigurationError."""

    def test_initialization(self):
        """Test l'initialisation de LoaderConfigurationError."""
        exception = LoaderConfigurationError("Config error")
        
        assert str(exception) == "Config error"
        assert exception.operation == "loader_configuration"
        assert exception.is_critical is True
        assert exception.is_retryable is False

    def test_with_config_key(self):
        """Test avec une clé de configuration."""
        exception = LoaderConfigurationError("Config error", config_key="test_key")
        
        context = exception.get_context()
        assert context["config_key"] == "test_key"


class TestLoaderAuthenticationError:
    """Tests pour LoaderAuthenticationError."""

    def test_initialization(self):
        """Test l'initialisation de LoaderAuthenticationError."""
        exception = LoaderAuthenticationError("Auth error")
        
        assert str(exception) == "Auth error"
        assert exception.operation == "loader_authentication"
        assert exception.is_critical is True
        assert exception.is_retryable is False

    def test_with_auth_method(self):
        """Test avec une méthode d'authentification."""
        exception = LoaderAuthenticationError("Auth error", auth_method="oauth")
        
        context = exception.get_context()
        assert context["auth_method"] == "oauth"


class TestLoaderPermissionError:
    """Tests pour LoaderPermissionError."""

    def test_initialization(self):
        """Test l'initialisation de LoaderPermissionError."""
        exception = LoaderPermissionError("Permission denied")
        
        assert str(exception) == "Permission denied"
        assert exception.operation == "loader_permission"
        assert exception.is_critical is True
        assert exception.is_retryable is False

    def test_with_required_permission(self):
        """Test avec une permission requise."""
        exception = LoaderPermissionError(
            "Permission denied", 
            required_permission="read"
        )
        
        context = exception.get_context()
        assert context["required_permission"] == "read"


class TestLoaderNetworkError:
    """Tests pour LoaderNetworkError."""

    def test_initialization(self):
        """Test l'initialisation de LoaderNetworkError."""
        exception = LoaderNetworkError("Network error")
        
        assert str(exception) == "Network error"
        assert exception.operation == "loader_network"
        assert exception.is_critical is False
        assert exception.is_retryable is True


class TestLoaderTimeoutError:
    """Tests pour LoaderTimeoutError."""

    def test_initialization(self):
        """Test l'initialisation de LoaderTimeoutError."""
        exception = LoaderTimeoutError("Timeout error")
        
        assert str(exception) == "Timeout error"
        assert exception.operation == "loader_timeout"
        assert exception.is_critical is False
        assert exception.is_retryable is True

    def test_with_timeout_seconds(self):
        """Test avec timeout en secondes."""
        exception = LoaderTimeoutError("Timeout error", timeout_seconds=30.0)
        
        context = exception.get_context()
        assert context["timeout_seconds"] == 30.0


class TestLoaderRateLimitError:
    """Tests pour LoaderRateLimitError."""

    def test_initialization(self):
        """Test l'initialisation de LoaderRateLimitError."""
        exception = LoaderRateLimitError("Rate limit exceeded")
        
        assert str(exception) == "Rate limit exceeded"
        assert exception.operation == "loader_rate_limit"
        assert exception.is_critical is False
        assert exception.is_retryable is True

    def test_with_retry_after(self):
        """Test avec retry_after."""
        exception = LoaderRateLimitError("Rate limit exceeded", retry_after=60)
        
        context = exception.get_context()
        assert context["retry_after"] == 60


class TestLoaderNotFoundError:
    """Tests pour LoaderNotFoundError."""

    def test_initialization(self):
        """Test l'initialisation de LoaderNotFoundError."""
        exception = LoaderNotFoundError("Resource not found")
        
        assert str(exception) == "Resource not found"
        assert exception.operation == "loader_not_found"
        assert exception.is_critical is False
        assert exception.is_retryable is False


class TestLoaderValidationError:
    """Tests pour LoaderValidationError."""

    def test_initialization(self):
        """Test l'initialisation de LoaderValidationError."""
        exception = LoaderValidationError("Validation failed")
        
        assert str(exception) == "Validation failed"
        assert exception.operation == "loader_validation"
        assert exception.is_critical is True
        assert exception.is_retryable is False

    def test_with_validation_field(self):
        """Test avec champ de validation."""
        exception = LoaderValidationError(
            "Validation failed", 
            validation_field="email"
        )
        
        context = exception.get_context()
        assert context["validation_field"] == "email"


class TestLoaderCircuitBreakerError:
    """Tests pour LoaderCircuitBreakerError."""

    def test_initialization(self):
        """Test l'initialisation de LoaderCircuitBreakerError."""
        exception = LoaderCircuitBreakerError("Circuit breaker open")
        
        assert str(exception) == "Circuit breaker open"
        assert exception.operation == "loader_circuit_breaker"
        assert exception.is_critical is False
        assert exception.is_retryable is True

    def test_with_failure_count(self):
        """Test avec nombre d'échecs."""
        exception = LoaderCircuitBreakerError(
            "Circuit breaker open", 
            failure_count=5
        )
        
        context = exception.get_context()
        assert context["failure_count"] == 5
