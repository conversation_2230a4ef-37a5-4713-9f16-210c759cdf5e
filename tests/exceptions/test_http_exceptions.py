"""
Tests unitaires pour les exceptions HTTP.

Ce module teste la classe HttpException et ses sous-classes,
vérifiant la classification automatique selon les codes de statut HTTP.
"""

import pytest
import requests
from unittest.mock import Mock

from kbotloadscheduler.exceptions.http_exceptions import (
    HttpException,
    HttpClientError,
    HttpServerError,
    HttpAuthenticationError,
    HttpPermissionError,
    HttpNotFoundError,
    HttpRateLimitError,
    HttpTimeoutError,
)


class TestHttpException:
    """Tests pour la classe HttpException de base."""

    def test_basic_initialization(self):
        """Test l'initialisation basique d'une HttpException."""
        exception = HttpException("HTTP error", status_code=500)

        assert str(exception) == "HTTP error"
        assert exception.status_code == 500
        assert exception.operation == "http_request"
        assert exception.is_critical is True  # 5xx par défaut
        assert exception.is_retryable is True  # 5xx par défaut

    def test_classification_4xx_errors(self):
        """Test la classification des erreurs 4xx."""
        exception = HttpException("Client error", status_code=400)

        assert exception.is_critical is True
        assert exception.is_retryable is False

    def test_classification_5xx_errors(self):
        """Test la classification des erreurs 5xx."""
        exception = HttpException("Server error", status_code=500)

        assert exception.is_critical is True
        assert exception.is_retryable is True

    def test_classification_retryable_codes(self):
        """Test la classification des codes retryables spécifiques."""
        retryable_codes = [429, 500, 502, 503, 504]

        for code in retryable_codes:
            exception = HttpException("Error", status_code=code)
            assert exception.is_retryable is True

    def test_classification_non_retryable_codes(self):
        """Test la classification des codes non-retryables."""
        non_retryable_codes = [400, 401, 403, 404, 422]

        for code in non_retryable_codes:
            exception = HttpException("Error", status_code=code)
            assert exception.is_retryable is False

    def test_from_response_factory_method(self):
        """Test la factory method from_response."""
        # Mock response
        response = Mock(spec=requests.Response)
        response.status_code = 404
        response.url = "https://api.example.com/test"
        response.text = "Not Found"
        response.headers = {}

        exception = HttpException.from_response(response, "Custom message")

        assert isinstance(exception, HttpNotFoundError)
        assert exception.status_code == 404
        assert "Custom message" in str(exception)
        assert exception.get_context()["url"] == "https://api.example.com/test"

    def test_from_response_with_default_message(self):
        """Test from_response avec message par défaut."""
        response = Mock(spec=requests.Response)
        response.status_code = 500
        response.url = "https://api.example.com/test"
        response.text = "Internal Server Error"
        response.headers = {}

        exception = HttpException.from_response(response)

        assert "HTTP 500" in str(exception)
        assert "https://api.example.com/test" in str(exception)

    def test_from_response_401_authentication(self):
        """Test from_response pour erreur 401."""
        response = Mock(spec=requests.Response)
        response.status_code = 401
        response.url = "https://api.example.com/test"
        response.headers = {}

        exception = HttpException.from_response(response)

        assert isinstance(exception, HttpAuthenticationError)
        assert exception.is_critical is True
        assert exception.is_retryable is False

    def test_from_response_403_permission(self):
        """Test from_response pour erreur 403."""
        response = Mock(spec=requests.Response)
        response.status_code = 403
        response.url = "https://api.example.com/test"
        response.headers = {}

        exception = HttpException.from_response(response)

        assert isinstance(exception, HttpPermissionError)
        assert exception.is_critical is True
        assert exception.is_retryable is False

    def test_from_response_404_not_found(self):
        """Test from_response pour erreur 404."""
        response = Mock(spec=requests.Response)
        response.status_code = 404
        response.url = "https://api.example.com/test"
        response.headers = {}

        exception = HttpException.from_response(response)

        assert isinstance(exception, HttpNotFoundError)
        assert exception.is_critical is False
        assert exception.is_retryable is False

    def test_from_response_429_rate_limit(self):
        """Test from_response pour erreur 429."""
        response = Mock(spec=requests.Response)
        response.status_code = 429
        response.url = "https://api.example.com/test"
        response.headers = {"Retry-After": "60"}

        exception = HttpException.from_response(response)

        assert isinstance(exception, HttpRateLimitError)
        assert exception.is_critical is False
        assert exception.is_retryable is True
        assert exception.get_context()["retry_after"] == "60"

    def test_from_response_timeout_codes(self):
        """Test from_response pour codes de timeout."""
        timeout_codes = [408, 504]

        for code in timeout_codes:
            response = Mock(spec=requests.Response)
            response.status_code = code
            response.url = "https://api.example.com/test"
            response.headers = {}

            exception = HttpException.from_response(response)

            assert isinstance(exception, HttpTimeoutError)
            assert exception.is_critical is False
            assert exception.is_retryable is True


class TestHttpClientError:
    """Tests pour HttpClientError."""

    def test_initialization(self):
        """Test l'initialisation de HttpClientError."""
        exception = HttpClientError("Client error", status_code=400)

        assert str(exception) == "Client error"
        assert exception.status_code == 400
        assert exception.is_critical is True
        assert exception.is_retryable is False


class TestHttpServerError:
    """Tests pour HttpServerError."""

    def test_initialization(self):
        """Test l'initialisation de HttpServerError."""
        exception = HttpServerError("Server error", status_code=500)

        assert str(exception) == "Server error"
        assert exception.status_code == 500
        assert exception.is_critical is True
        assert exception.is_retryable is True


class TestHttpAuthenticationError:
    """Tests pour HttpAuthenticationError."""

    def test_initialization(self):
        """Test l'initialisation de HttpAuthenticationError."""
        exception = HttpAuthenticationError("Authentication failed")

        assert str(exception) == "Authentication failed"
        assert exception.status_code == 401
        assert exception.operation == "http_authentication"
        assert exception.is_critical is True
        assert exception.is_retryable is False


class TestHttpPermissionError:
    """Tests pour HttpPermissionError."""

    def test_initialization(self):
        """Test l'initialisation de HttpPermissionError."""
        exception = HttpPermissionError("Permission denied")

        assert str(exception) == "Permission denied"
        assert exception.status_code == 403
        assert exception.operation == "http_permission"
        assert exception.is_critical is True
        assert exception.is_retryable is False


class TestHttpNotFoundError:
    """Tests pour HttpNotFoundError."""

    def test_initialization(self):
        """Test l'initialisation de HttpNotFoundError."""
        exception = HttpNotFoundError("Resource not found")

        assert str(exception) == "Resource not found"
        assert exception.status_code == 404
        assert exception.operation == "http_not_found"
        assert exception.is_critical is False
        assert exception.is_retryable is False


class TestHttpRateLimitError:
    """Tests pour HttpRateLimitError."""

    def test_initialization(self):
        """Test l'initialisation de HttpRateLimitError."""
        exception = HttpRateLimitError("Rate limit exceeded")

        assert str(exception) == "Rate limit exceeded"
        assert exception.status_code == 429
        assert exception.operation == "http_rate_limit"
        assert exception.is_critical is False
        assert exception.is_retryable is True

    def test_with_retry_after(self):
        """Test avec retry_after."""
        exception = HttpRateLimitError("Rate limit exceeded", retry_after=60)

        context = exception.get_context()
        assert context["retry_after"] == 60


class TestHttpTimeoutError:
    """Tests pour HttpTimeoutError."""

    def test_initialization(self):
        """Test l'initialisation de HttpTimeoutError."""
        exception = HttpTimeoutError("Request timeout")

        assert str(exception) == "Request timeout"
        assert exception.status_code == 408
        assert exception.operation == "http_timeout"
        assert exception.is_critical is False
        assert exception.is_retryable is True

    def test_with_timeout_seconds(self):
        """Test avec timeout en secondes."""
        exception = HttpTimeoutError("Request timeout", timeout_seconds=30.0)

        context = exception.get_context()
        assert context["timeout_seconds"] == 30.0
