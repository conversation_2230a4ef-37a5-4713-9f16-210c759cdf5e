import json
from datetime import datetime

from data.gcs_repo_test_data import GcsRepoTestData
from data.kbot_embedding_api_test_data import KbotEmbeddingApiTestData
from kbotloadscheduler.bean.beans import SourceBean
from kbotloadscheduler.gcs.treatment_file_manager import TreatmentFileManager
from testutils.mock_gcs import MockGcs


# Tests du TreatmentFileManager utilisant le mock_gcs sous testutils
class TestTreatmentFileManager:

    # TEST extract_info_from_file_name #####
    def test_extract_info_from_file_name_first(self):
        file_name = "gs://mon_bucket-perimetre/202409121630/domaine/source/type/type.json"
        treatment_file_manager = TreatmentFileManager("gs://mon_bucket-[perimeter_code]")
        [date, domain, source, file_type, file_sig] = treatment_file_manager.extract_info_from_file_name(file_name)
        assert date == "202409121630"
        assert domain == "domaine"
        assert source == "source"
        assert file_type == "type"
        assert file_sig == "domaine/source/type/type.json"

    def test_extract_info_from_file_name_after(self):
        file_name = "gs://mon_bucket-perimetre/dir1/dir2/dir3/202409121630/domaine/source/type/type.json"
        treatment_file_manager = TreatmentFileManager("gs://mon_bucket-[perimeter_code]")
        [date, domain, source, file_type, file_sig] = treatment_file_manager.extract_info_from_file_name(file_name)
        assert date == "202409121630"
        assert domain == "domaine"
        assert source == "source"
        assert file_type == "type"
        assert file_sig == "domaine/source/type/type.json"

    def test_extract_info_from_file_name_wrong_date(self):
        file_name = "gs://mon_bucket-perimetre/dir1/dir2/dir3/20240912/domaine/source/type/type.json"
        treatment_file_manager = TreatmentFileManager("gs://mon_bucket-[perimeter_code]")
        [date, domain, source, file_type, file_sig] = treatment_file_manager.extract_info_from_file_name(file_name)
        assert date is None
        assert domain is None
        assert source is None
        assert file_type is None
        assert file_sig is None

    def test_extract_info_from_file_name_not_enough_item(self):
        file_name = "gs://mon_bucket-perimetre/dir1/dir2/dir3/202409121630/domaine"
        treatment_file_manager = TreatmentFileManager("gs://mon_bucket-[perimeter_code]")
        [date, domain, source, file_type, file_sig] = treatment_file_manager.extract_info_from_file_name(file_name)
        assert date is None
        assert domain is None
        assert source is None
        assert file_type is None
        assert file_sig is None

    # TEST build_directory_name #####
    def test_build_directory_name(self):
        treatment_file_manager = TreatmentFileManager("gs://mon_bucket-[perimeter_code]")
        directory = treatment_file_manager.build_directory_name("perim", "202409121630", "dom", "src", "typedir")
        assert directory == "gs://mon_bucket-perim/202409121630/dom/src/typedir"

    # TEST write_get_list #####
    def test_write_get_list(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket("mon_bucket-perim")

        conf_json = {"conf1": "val1", "conf2": "val2"}
        source = SourceBean(
            id=42,
            code="src",
            label="sourcetest",
            src_type="typsrc",
            configuration=json.dumps(conf_json),
            last_load_time=1726156483,
            load_interval="24",
            domain_code="dom",
            perimeter_code="perim",
        )

        treatment_file_manager = TreatmentFileManager("gs://mon_bucket-[perimeter_code]")
        treatment_file_manager.write_get_list("202409121630", source)
        blob = my_mock_gcs.return_blob("mon_bucket-perim", "202409121630/dom/src/getlist/getlist.json")
        actual_source = SourceBean(**json.loads(blob.download_as_string()))
        assert actual_source == source

    # TEST read_get_list #####
    def test_read_get_list(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        GcsRepoTestData.write_get_list(my_mock_gcs)

        get_list_file = f"gs://{GcsRepoTestData.WORK_BUCKET}/{GcsRepoTestData.GET_LIST_FILE}"
        treatment_file_manager = TreatmentFileManager("gs://mon_bucket-[perimeter_code]")
        actual_source = treatment_file_manager.read_get_list(get_list_file)
        assert actual_source == SourceBean(**GcsRepoTestData.SOURCE)

    def test_write_document_list(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(GcsRepoTestData.WORK_BUCKET)

        treatment_file_manager = TreatmentFileManager("gs://mon_bucket-[perimeter_code]")
        actual_answer_list = treatment_file_manager.write_document_list(
            load_date=GcsRepoTestData.LOAD_DATE,
            source=SourceBean(**GcsRepoTestData.SOURCE),
            documents=GcsRepoTestData.get_document_list_for_source(),
        )
        assert actual_answer_list == GcsRepoTestData.get_serialized_document_list_for_source()

        blob_list = my_mock_gcs.return_blob(GcsRepoTestData.WORK_BUCKET, GcsRepoTestData.LIST_FILE)
        actual_file_list_content = json.loads(blob_list.download_as_string())

        expected_file_list_content = {
            "source": GcsRepoTestData.SOURCE,
            "documents": GcsRepoTestData.get_serialized_document_list_for_source(),
        }
        assert actual_file_list_content == expected_file_list_content

    def test_write_document_list_with_type(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(GcsRepoTestData.WORK_BUCKET)

        treatment_file_manager = TreatmentFileManager("gs://mon_bucket-[perimeter_code]")
        actual_answer_list = treatment_file_manager.write_document_list(
            load_date=GcsRepoTestData.LOAD_DATE,
            source=SourceBean(**GcsRepoTestData.SOURCE),
            documents=GcsRepoTestData.get_document_list_for_source(),
            file_type="anothertype",
        )
        assert actual_answer_list == GcsRepoTestData.get_serialized_document_list_for_source()

        file_type_path = f"{GcsRepoTestData.RELATIVE_OUTPUT}/anothertype/anothertype.json"
        blob_list = my_mock_gcs.return_blob(GcsRepoTestData.WORK_BUCKET, file_type_path)
        actual_file_list_content = json.loads(blob_list.download_as_string())

        expected_file_list_content = {
            "source": GcsRepoTestData.SOURCE,
            "documents": GcsRepoTestData.get_serialized_document_list_for_source(),
        }
        assert actual_file_list_content == expected_file_list_content

    def test_read_document_list(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        json_document_list = GcsRepoTestData.get_serialized_document_list_for_source()
        GcsRepoTestData.write_document_list(my_mock_gcs, json_document_list)

        document_list_file = f"gs://{GcsRepoTestData.WORK_BUCKET}/{GcsRepoTestData.LIST_FILE}"
        treatment_file_manager = TreatmentFileManager("gs://mon_bucket-[perimeter_code]")
        actual_dict = treatment_file_manager.read_document_list(document_list_file)
        expected_dict = {
            "source": SourceBean(**GcsRepoTestData.SOURCE),
            "documents": GcsRepoTestData.get_document_list_for_source(),
        }
        assert actual_dict == expected_dict

    def test_write_document_metadata(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(KbotEmbeddingApiTestData.WORK_BUCKET)

        treatment_file_manager = TreatmentFileManager("gs://mon_bucket-[perimeter_code]")
        relative_file_path = KbotEmbeddingApiTestData.DOCUMENT_NAME
        metadata = KbotEmbeddingApiTestData.METADATA
        serialized_md = treatment_file_manager.write_document_metadata(metadata)

        metadata_path = f"{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/docs/{relative_file_path}.metadata.json"
        blob_metadata = my_mock_gcs.return_blob(KbotEmbeddingApiTestData.WORK_BUCKET, metadata_path)
        actual_metadata_content = json.loads(blob_metadata.download_as_string())

        expected_metadata = KbotEmbeddingApiTestData.METADATA_SERIALIZED

        assert serialized_md == expected_metadata
        assert actual_metadata_content == expected_metadata

    def test_read_document_metadata(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        relative_file_path = KbotEmbeddingApiTestData.DOCUMENT_NAME
        metadata_path = f"{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/docs/{relative_file_path}.metadata.json"
        metadata_for_file = KbotEmbeddingApiTestData.METADATA_SERIALIZED
        metadata_json_for_file = json.dumps(metadata_for_file)
        my_mock_gcs.add_bucket(KbotEmbeddingApiTestData.WORK_BUCKET)

        my_mock_gcs.add_blob(
            KbotEmbeddingApiTestData.WORK_BUCKET,
            metadata_path,
            True,
            metadata_json_for_file,
            len(metadata_json_for_file),
        )

        metadata_full_path = f"gs://{KbotEmbeddingApiTestData.WORK_BUCKET}/{metadata_path}"
        treatment_file_manager = TreatmentFileManager("gs://mon_bucket-[perimeter_code]")
        actual_metadata = treatment_file_manager.read_document_metadata(metadata_full_path)

        expected_metadata = KbotEmbeddingApiTestData.METADATA

        assert actual_metadata == expected_metadata

    def test_set_in_progress(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(KbotEmbeddingApiTestData.WORK_BUCKET)
        json_file = f"{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/anything/something.json"
        content = "whetever"
        my_mock_gcs.add_blob(KbotEmbeddingApiTestData.WORK_BUCKET, json_file, True, content, len(content))

        treatment_file_manager = TreatmentFileManager("gs://mon_bucket-[perimeter_code]")
        treatment_file_manager.set_in_progress(f"gs://{KbotEmbeddingApiTestData.WORK_BUCKET}/{json_file}")

        expected_in_progress_file = f"{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/anything/something.inprogress"
        blob = my_mock_gcs.return_blob(
            KbotEmbeddingApiTestData.WORK_BUCKET, expected_in_progress_file, get_blob_call=True
        )
        assert blob is not None

    def test_unset_in_progress(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(KbotEmbeddingApiTestData.WORK_BUCKET)
        content = "whetever"
        json_file = f"{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/anything/something.json"
        my_mock_gcs.add_blob(KbotEmbeddingApiTestData.WORK_BUCKET, json_file, True, content, len(content))
        in_progress_file = f"{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/anything/something.inprogress"
        my_mock_gcs.add_blob(KbotEmbeddingApiTestData.WORK_BUCKET, in_progress_file, True, content, len(content))

        treatment_file_manager = TreatmentFileManager("gs://mon_bucket-[perimeter_code]")
        treatment_file_manager.unset_in_progress(f"gs://{KbotEmbeddingApiTestData.WORK_BUCKET}/{json_file}")

        blob_in_progress = my_mock_gcs.return_blob(
            KbotEmbeddingApiTestData.WORK_BUCKET, in_progress_file, get_blob_call=True
        )
        assert blob_in_progress is None

    def test_set_done(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(KbotEmbeddingApiTestData.WORK_BUCKET)
        content = "whetever"
        json_file = f"{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/anything/something.json"
        my_mock_gcs.add_blob(KbotEmbeddingApiTestData.WORK_BUCKET, json_file, True, content, len(content))
        in_progress_file = f"{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/anything/something.inprogress"
        my_mock_gcs.add_blob(KbotEmbeddingApiTestData.WORK_BUCKET, in_progress_file, True, content, len(content))

        treatment_file_manager = TreatmentFileManager("gs://mon_bucket-[perimeter_code]")
        treatment_file_manager.set_done(f"gs://{KbotEmbeddingApiTestData.WORK_BUCKET}/{json_file}")

        expected_done_file = f"{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/anything/something.done"
        blob_done = my_mock_gcs.return_blob(
            KbotEmbeddingApiTestData.WORK_BUCKET, expected_done_file, get_blob_call=True
        )
        assert blob_done is not None
        blob_in_progress = my_mock_gcs.return_blob(
            KbotEmbeddingApiTestData.WORK_BUCKET, in_progress_file, get_blob_call=True
        )
        assert blob_in_progress is None

    def test_move_embedded_document_to_done(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(KbotEmbeddingApiTestData.WORK_BUCKET)

        relative_file_path = KbotEmbeddingApiTestData.DOCUMENT_NAME
        document_path = f"{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/docs/{relative_file_path}"
        content = "Just for test"
        my_mock_gcs.add_blob(KbotEmbeddingApiTestData.WORK_BUCKET, document_path, True, content, len(content))

        metadata = KbotEmbeddingApiTestData.METADATA
        metadata_json = json.dumps(KbotEmbeddingApiTestData.METADATA_SERIALIZED)
        my_mock_gcs.add_blob(
            KbotEmbeddingApiTestData.WORK_BUCKET,
            document_path + ".metadata.json",
            True,
            metadata_json,
            len(metadata_json),
        )

        treatment_file_manager = TreatmentFileManager("gs://mon_bucket-[perimeter_code]")
        treatment_file_manager.move_embedded_document_to_done(KbotEmbeddingApiTestData.PERIMETER_CODE, metadata)

        expected_document_path = f"{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/docs_done/{relative_file_path}"
        expected_document_blob = my_mock_gcs.return_blob(
            KbotEmbeddingApiTestData.WORK_BUCKET, expected_document_path, True
        )
        assert expected_document_blob is not None
        assert expected_document_blob.download_as_string() == content

        expected_metadata_path = (
            f"{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}" + f"/docs_done/{relative_file_path}.metadata.json"
        )
        expected_metadata_blob = my_mock_gcs.return_blob(
            KbotEmbeddingApiTestData.WORK_BUCKET, expected_metadata_path, True
        )
        assert expected_metadata_blob is not None
        assert json.loads(expected_metadata_blob.download_as_string()) == KbotEmbeddingApiTestData.METADATA_SERIALIZED

        origin_document_blob = my_mock_gcs.return_blob(KbotEmbeddingApiTestData.WORK_BUCKET, document_path, True)
        assert origin_document_blob is None
        origin_metadata_blob = my_mock_gcs.return_blob(
            KbotEmbeddingApiTestData.WORK_BUCKET, document_path + ".metadata.json", True
        )
        assert origin_metadata_blob is None

    def test_move_embedded_document_to_error(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(KbotEmbeddingApiTestData.WORK_BUCKET)

        relative_file_path = KbotEmbeddingApiTestData.DOCUMENT_NAME
        document_path = f"{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/docs/{relative_file_path}"
        content = "Just for test"
        my_mock_gcs.add_blob(KbotEmbeddingApiTestData.WORK_BUCKET, document_path, True, content, len(content))
        metadata = KbotEmbeddingApiTestData.METADATA
        metadata_json = json.dumps(KbotEmbeddingApiTestData.METADATA_SERIALIZED)
        my_mock_gcs.add_blob(
            KbotEmbeddingApiTestData.WORK_BUCKET,
            document_path + ".metadata.json",
            True,
            metadata_json,
            len(metadata_json),
        )

        error_message = "Mon sublime message d'erreur"
        treatment_file_manager = TreatmentFileManager("gs://mon_bucket-[perimeter_code]")
        treatment_file_manager.move_embedded_document_to_error(
            KbotEmbeddingApiTestData.PERIMETER_CODE, metadata, error_message
        )

        expected_document_path = f"{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/docs_error/{relative_file_path}"
        expected_document_blob = my_mock_gcs.return_blob(
            KbotEmbeddingApiTestData.WORK_BUCKET, expected_document_path, True
        )
        assert expected_document_blob is not None
        assert expected_document_blob.download_as_string() == content

        expected_metadata_path = (
            f"{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/docs_error/{relative_file_path}.metadata.json"
        )
        expected_metadata_blob = my_mock_gcs.return_blob(
            KbotEmbeddingApiTestData.WORK_BUCKET, expected_metadata_path, True
        )
        assert expected_metadata_blob is not None
        assert expected_metadata_blob.download_as_string() == metadata_json

        expected_error_path = f"{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/docs_error/{relative_file_path}.error.log"
        expected_error_blob = my_mock_gcs.return_blob(KbotEmbeddingApiTestData.WORK_BUCKET, expected_error_path, True)
        assert expected_error_blob is not None
        assert json.loads(expected_error_blob.download_as_string()) == {
            **KbotEmbeddingApiTestData.METADATA_SERIALIZED,
            "error": error_message,
        }

        origin_document_blob = my_mock_gcs.return_blob(KbotEmbeddingApiTestData.WORK_BUCKET, document_path, True)
        assert origin_document_blob is None
        origin_metadata_blob = my_mock_gcs.return_blob(
            KbotEmbeddingApiTestData.WORK_BUCKET, document_path + ".metadata.json", True
        )
        assert origin_metadata_blob is None

    def test_get_load_dates(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(KbotEmbeddingApiTestData.WORK_BUCKET)
        self.add_treatment_files(my_mock_gcs, "202410141400/dom/src/", "getlist/getlist.json")
        self.add_treatment_files(my_mock_gcs, "202410141500/dom/src/", "getlist/getlist.json")
        self.add_treatment_files(my_mock_gcs, "202410142000/dom/src/", "getlist/getlist.json")
        self.add_treatment_files(my_mock_gcs, "202410151400/dom/src/", "getlist/getlist.json")
        self.add_treatment_files(my_mock_gcs, "202410151500/dom/src/", "getlist/getlist.json")

        treatment_file_manager = TreatmentFileManager("gs://" + KbotEmbeddingApiTestData.BASE_BUCKET)
        list_dates = treatment_file_manager.get_load_dates(
            KbotEmbeddingApiTestData.PERIMETER_CODE, datetime(2024, 10, 15, 14, 12, 0)
        )
        assert len(list_dates) == 3
        assert list_dates == ["202410141500", "202410142000", "202410151400"]

    def test_get_dates_to_purge(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(KbotEmbeddingApiTestData.WORK_BUCKET)
        self.add_treatment_files(my_mock_gcs, "202410101400/dom/src/", "getlist/getlist.json")
        self.add_treatment_files(my_mock_gcs, "202410111500/dom/src/", "getlist/getlist.json")
        self.add_treatment_files(my_mock_gcs, "202410142000/dom/src/", "getlist/getlist.json")
        self.add_treatment_files(my_mock_gcs, "202410151400/dom/src/", "getlist/getlist.json")
        self.add_treatment_files(my_mock_gcs, "202410151500/dom/src/", "getlist/getlist.json")

        treatment_file_manager = TreatmentFileManager("gs://" + KbotEmbeddingApiTestData.BASE_BUCKET)
        list_dates = treatment_file_manager.get_dates_to_purge(
            KbotEmbeddingApiTestData.PERIMETER_CODE, datetime(2024, 10, 15, 14, 12, 0)
        )
        assert len(list_dates) == 2
        assert list_dates == ["202410101400", "202410111500"]

    def test_purge_old_treatments_files(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(KbotEmbeddingApiTestData.WORK_BUCKET)
        self.add_treatment_files(my_mock_gcs, "202410101400/dom/src/", "getlist/getlist.json")
        self.add_treatment_files(my_mock_gcs, "202410142000/dom/src/", "getlist/getlist.json")
        self.add_treatment_files(my_mock_gcs, "202410151400/dom/src/", "getlist/getlist.json")
        self.add_treatment_files(my_mock_gcs, "202410151500/dom/src/", "getlist/getlist.json")

        treatment_file_manager = TreatmentFileManager("gs://" + KbotEmbeddingApiTestData.BASE_BUCKET)
        treatment_file_manager.purge_old_treatments_files(
            KbotEmbeddingApiTestData.PERIMETER_CODE, datetime(2024, 10, 15, 14, 12, 0)
        )
        deleted_blob = my_mock_gcs.return_blob(
            KbotEmbeddingApiTestData.WORK_BUCKET, "202410101400/dom/src/getlist/getlist.json", get_blob_call=True
        )
        assert deleted_blob is None

        not_deleted_blob = my_mock_gcs.return_blob(
            KbotEmbeddingApiTestData.WORK_BUCKET, "202410142000/dom/src/getlist/getlist.json", get_blob_call=True
        )
        assert not_deleted_blob is not None

    def test_get_treatments_files_getlist(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(KbotEmbeddingApiTestData.WORK_BUCKET)
        self.add_treatment_files(my_mock_gcs, "202410141400/dom/src/", "getlist/getlist.json")

        treatment_file_manager = TreatmentFileManager("gs://" + KbotEmbeddingApiTestData.BASE_BUCKET)
        treatment_files = treatment_file_manager.get_treatments_files(
            KbotEmbeddingApiTestData.PERIMETER_CODE, "202410141400"
        )

        self.assert_has_treatment_file(
            treatment_files, "202410141400/dom/src/", "getlist", "getlist", "getlist/getlist.json", False, False
        )

    def test_get_treatments_files_getlist_in_progress(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(KbotEmbeddingApiTestData.WORK_BUCKET)
        self.add_treatment_files(
            my_mock_gcs, "202410141400/dom/src/", "getlist/getlist.json", "getlist/getlist.inprogress"
        )

        treatment_file_manager = TreatmentFileManager("gs://" + KbotEmbeddingApiTestData.BASE_BUCKET)
        treatment_files = treatment_file_manager.get_treatments_files(
            KbotEmbeddingApiTestData.PERIMETER_CODE, "202410141400"
        )

        self.assert_has_treatment_file(
            treatment_files, "202410141400/dom/src/", "getlist", "getlist", "getlist/getlist.json", True, False
        )

    def test_get_treatments_files_getlist_done(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(KbotEmbeddingApiTestData.WORK_BUCKET)
        self.add_treatment_files(my_mock_gcs, "202410141400/dom/src/", "getlist/getlist.json", "getlist/getlist.done")

        treatment_file_manager = TreatmentFileManager("gs://" + KbotEmbeddingApiTestData.BASE_BUCKET)
        treatment_files = treatment_file_manager.get_treatments_files(
            KbotEmbeddingApiTestData.PERIMETER_CODE, "202410141400"
        )

        self.assert_has_treatment_file(
            treatment_files, "202410141400/dom/src/", "getlist", "getlist", "getlist/getlist.json", False, True
        )

    def test_get_treatments_files_metadata(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(KbotEmbeddingApiTestData.WORK_BUCKET)
        self.add_treatment_files(
            my_mock_gcs,
            "202410141400/dom/src/",
            "docs/path/to/doc/document_name.pdf",
            "docs/path/to/doc/document_name.pdf.metadata.json",
        )

        treatment_file_manager = TreatmentFileManager("gs://" + KbotEmbeddingApiTestData.BASE_BUCKET)
        treatment_files = treatment_file_manager.get_treatments_files(
            KbotEmbeddingApiTestData.PERIMETER_CODE, "202410141400"
        )

        self.assert_has_treatment_file(
            treatment_files,
            "202410141400/dom/src/",
            "docs|path/to/doc/document_name.pdf",
            "docs",
            "docs/path/to/doc/document_name.pdf.metadata.json",
            False,
            False,
        )

    def test_get_treatments_files_metadata_in_progress(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(KbotEmbeddingApiTestData.WORK_BUCKET)
        self.add_treatment_files(
            my_mock_gcs,
            "202410141400/dom/src/",
            "docs/path/to/doc/document_name.pdf",
            "docs/path/to/doc/document_name.pdf.metadata.json",
            "docs/path/to/doc/document_name.pdf.metadata.inprogress",
        )

        treatment_file_manager = TreatmentFileManager("gs://" + KbotEmbeddingApiTestData.BASE_BUCKET)
        treatment_files = treatment_file_manager.get_treatments_files(
            KbotEmbeddingApiTestData.PERIMETER_CODE, "202410141400"
        )

        self.assert_has_treatment_file(
            treatment_files,
            "202410141400/dom/src/",
            "docs|path/to/doc/document_name.pdf",
            "docs",
            "docs/path/to/doc/document_name.pdf.metadata.json",
            True,
            False,
        )

    def test_get_treatments_files_metadata_done(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(KbotEmbeddingApiTestData.WORK_BUCKET)
        self.add_treatment_files(
            my_mock_gcs,
            "202410141400/dom/src/",
            "docs/path/to/doc/document_name.pdf",
            "docs/path/to/doc/document_name.pdf.metadata.json",
            "docs/path/to/doc/document_name.pdf.metadata.done",
        )

        treatment_file_manager = TreatmentFileManager("gs://" + KbotEmbeddingApiTestData.BASE_BUCKET)
        treatment_files = treatment_file_manager.get_treatments_files(
            KbotEmbeddingApiTestData.PERIMETER_CODE, "202410141400"
        )

        self.assert_has_treatment_file(
            treatment_files,
            "202410141400/dom/src/",
            "docs|path/to/doc/document_name.pdf",
            "docs",
            "docs/path/to/doc/document_name.pdf.metadata.json",
            False,
            True,
        )

    @classmethod
    def add_treatment_files(cls, mock_gcs, date_dom_src_dir, *files):
        content = "Content of files is not important"
        for file in files:
            mock_gcs.add_blob(
                KbotEmbeddingApiTestData.WORK_BUCKET, date_dom_src_dir + file, True, content, len(content)
            )

    @classmethod
    def assert_has_treatment_file(cls, treatment_files, date_dom_src_dir, file_key, file_type, file, in_progress, done):
        expected_key = "dom|src|" + file_key
        file_path = f"gs://{KbotEmbeddingApiTestData.WORK_BUCKET}/{date_dom_src_dir}{file}"
        extension = file.split(".")[-1]
        if in_progress:
            extension = "inprogress"
        if done:
            extension = "done"
        file_sig = "/".join(date_dom_src_dir.split("/")[1:]) + ".".join(file.split(".")[:-1]) + f".{extension}"
        parts = date_dom_src_dir.split("/")
        expected_value = {
            "load_date": parts[0],
            "domain": parts[1],
            "source": parts[2],
            "file_type": file_type,
            "file": file_path,
            "file_sig": file_sig,
        }
        if in_progress:
            expected_value["in_progress"] = in_progress
        if done:
            expected_value["done"] = done
        assert expected_key in treatment_files
        assert treatment_files[expected_key] == expected_value
