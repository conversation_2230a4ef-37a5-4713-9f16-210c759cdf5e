import json
from datetime import datetime

from data.gcs_repo_test_data import GcsRepoTestData
from kbotloadscheduler.bean.beans import DocumentBean, Metadata


class TestBeans:

    def test_document_bean_to_serializable_dict(self):
        document = DocumentBean(
            id="domA|srcA1|domA/srcA1/chemin/fichier.pdf",
            name="domA/srcA1/chemin/fichier.pdf",
            path="gs://repo-bucket/domA/srcA1/chemin/fichier.pdf",
            modification_time=datetime(2024, 9, 12, 12, 42, 36),
        )
        serializable = document.to_serializable_dict()
        assert serializable == {
            "id": "domA|srcA1|domA/srcA1/chemin/fichier.pdf",
            "name": "domA/srcA1/chemin/fichier.pdf",
            "path": "gs://repo-bucket/domA/srcA1/chemin/fichier.pdf",
            "modification_time": "20240912124236",
        }

    def test_document_bean_from_serializable_dict(self):
        document_dict = {
            "id": "domA|srcA1|domA/srcA1/chemin/fichier.pdf",
            "name": "domA/srcA1/chemin/fichier.pdf",
            "path": "gs://repo-bucket/domA/srcA1/chemin/fichier.pdf",
            "modification_time": "20240912124236",
        }
        actual_bean = DocumentBean.from_serializable_dict(document_dict)
        expected_bean = DocumentBean(
            id="domA|srcA1|domA/srcA1/chemin/fichier.pdf",
            name="domA/srcA1/chemin/fichier.pdf",
            path="gs://repo-bucket/domA/srcA1/chemin/fichier.pdf",
            modification_time=datetime(2024, 9, 12, 12, 42, 36),
        )
        assert actual_bean == expected_bean

    def test_document_bean_from_embedding_api(self):
        document_dict = {
            "document_id": "domA|srcA1|domA/srcA1/chemin/fichier.pdf",
            "modification_date": "2024-09-12T12:42:36",
        }
        actual_bean = DocumentBean.from_embedding_api(document_dict)
        expected_bean = DocumentBean(
            id="domA|srcA1|domA/srcA1/chemin/fichier.pdf",
            name="",
            path="",
            modification_time=datetime(2024, 9, 12, 12, 42, 36),
        )
        assert actual_bean == expected_bean

    def test_metadata_match_datetime_value(self):
        test_values = {
            "20240923095324": True,
            "20240923240000": False,
            "20241323093246": False,
            "20241232092342": False,
        }
        for value in test_values:
            assert Metadata.match_date_value(value) == test_values[value]

    def test_metadata_serialize(self):
        relative_file_path = "src1/domainA/diralpha/fileAlpha2.txt"
        metadata = {
            Metadata.DOMAIN_CODE: GcsRepoTestData.DOMAINE_CODE,
            Metadata.SOURCE_CODE: GcsRepoTestData.SOURCE_CODE,
            Metadata.SOURCE_TYPE: "gcs",
            Metadata.SOURCE_CONF: json.dumps(GcsRepoTestData.CONF_SOURCE),
            Metadata.SOURCE_URL: f"gs://{GcsRepoTestData.REPO_BUCKET}/{relative_file_path}",
            Metadata.LOCATION: f"gs://{GcsRepoTestData.WORK_BUCKET}/"
                               + f"{GcsRepoTestData.RELATIVE_OUTPUT}/docs/{relative_file_path}",
            Metadata.CREATION_TIME: GcsRepoTestData.DATE_CREATED_A2,
            Metadata.MODIFICATION_TIME: GcsRepoTestData.DATE_A2,
            "key1": "val1",
            "key2": "val2",
        }
        actual_serialized = Metadata.serialize(metadata)
        expected_serialized = {
            Metadata.DOMAIN_CODE: GcsRepoTestData.DOMAINE_CODE,
            Metadata.SOURCE_CODE: GcsRepoTestData.SOURCE_CODE,
            Metadata.SOURCE_TYPE: "gcs",
            Metadata.SOURCE_CONF: json.dumps(GcsRepoTestData.CONF_SOURCE),
            Metadata.SOURCE_URL: f"gs://{GcsRepoTestData.REPO_BUCKET}/{relative_file_path}",
            Metadata.LOCATION: f"gs://{GcsRepoTestData.WORK_BUCKET}/"
                               + f"{GcsRepoTestData.RELATIVE_OUTPUT}/docs/{relative_file_path}",
            Metadata.CREATION_TIME: "20240811081242",
            Metadata.MODIFICATION_TIME: "20240911081242",
            "key1": "val1",
            "key2": "val2",
        }
        assert actual_serialized == expected_serialized

    def test_metadata_unserialize(self):
        relative_file_path = "src1/domainA/diralpha/fileAlpha2.txt"
        metadata = {
            Metadata.DOMAIN_CODE: GcsRepoTestData.DOMAINE_CODE,
            Metadata.SOURCE_CODE: GcsRepoTestData.SOURCE_CODE,
            Metadata.SOURCE_TYPE: "gcs",
            Metadata.SOURCE_CONF: json.dumps(GcsRepoTestData.CONF_SOURCE),
            Metadata.SOURCE_URL: f"gs://{GcsRepoTestData.REPO_BUCKET}/{relative_file_path}",
            Metadata.LOCATION: f"gs://{GcsRepoTestData.WORK_BUCKET}/"
                               + f"{GcsRepoTestData.RELATIVE_OUTPUT}/docs/{relative_file_path}",
            Metadata.CREATION_TIME: "20240811081242",
            Metadata.MODIFICATION_TIME: "20240911081242",
            "key1": "val1",
            "key2": "val2",
        }
        actual_unserialized = Metadata.unserialize(metadata)
        expected_unserialized = {
            Metadata.DOMAIN_CODE: GcsRepoTestData.DOMAINE_CODE,
            Metadata.SOURCE_CODE: GcsRepoTestData.SOURCE_CODE,
            Metadata.SOURCE_TYPE: "gcs",
            Metadata.SOURCE_CONF: json.dumps(GcsRepoTestData.CONF_SOURCE),
            Metadata.SOURCE_URL: f"gs://{GcsRepoTestData.REPO_BUCKET}/{relative_file_path}",
            Metadata.LOCATION: f"gs://{GcsRepoTestData.WORK_BUCKET}/"
                               + f"{GcsRepoTestData.RELATIVE_OUTPUT}/docs/{relative_file_path}",
            Metadata.CREATION_TIME: GcsRepoTestData.DATE_CREATED_A2,
            Metadata.MODIFICATION_TIME: GcsRepoTestData.DATE_A2,
            "key1": "val1",
            "key2": "val2",
        }
        assert actual_unserialized == expected_unserialized
