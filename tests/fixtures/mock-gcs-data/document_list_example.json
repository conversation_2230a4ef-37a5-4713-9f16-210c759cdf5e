{"source": {"source_type": "confluence", "source_config": {"name": "Engineering Confluence", "url": "https://confluence.example.com", "spaces": ["ENG", "TECH"], "username": "mock_user", "password": "mock_password"}, "perimeter_code": "engineering", "load_date": "20241220120000"}, "documents": [{"id": "doc1", "title": "Technical Documentation", "url": "https://confluence.example.com/pages/doc1", "space": "ENG", "content_type": "page", "last_modified": "2024-12-20T10:00:00Z", "size": 2048, "metadata": {"author": "<EMAIL>", "tags": ["documentation", "technical"]}}, {"id": "doc2", "title": "API Guidelines", "url": "https://confluence.example.com/pages/doc2", "space": "ENG", "content_type": "page", "last_modified": "2024-12-19T15:30:00Z", "size": 1024, "metadata": {"author": "<EMAIL>", "tags": ["api", "guidelines"]}}]}