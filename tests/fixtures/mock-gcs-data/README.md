# Données de Test Fictives (Mock) pour GCS

Ce répertoire contient des exemples de données fictives (mock data) pour les tests GCS. Ces fichiers sont commités dans le dépôt en tant qu'exemples de référence et de documentation.
Ces données sont codées en dure danas `src/kbotloadscheduler/gcs/mock_gcs_config.py`

## Fichiers

### Exemples de Type de Source
- `getlist_confluence.json` - Exemple de type de source **Confluence**
- `getlist_sharepoint.json` - Exemple de type de source **SharePoint**
- `getlist_googledrive.json` - Exemple de type de source **Google Drive**
- `document_list_example.json` - Exemple de structure de liste de documents

## Configurations par Type de Source

Chaque type de source possède des paramètres de configuration différents :

### Confluence
```json
{
  "sourceType": "confluence",
  "confluence_url": "https://espace.agir.orange.com/",
  "space_key": "VODCASTV",
  "labels": "ravenne",
  "child_page_depth": "15",
  "include_attachements": "true"
}
```

### SharePoint
```json
{
  "sourceType": "sharepoint",
  "sharepoint_url": "https://company.sharepoint.com/sites/knowledge",
  "site_id": "abc123-def456-ghi789",
  "drive_id": "b!xyz789",
  "folder_path": "/Shared Documents/Knowledge Base",
  "include_subfolders": "true",
  "file_types": "docx,xlsx,pptx,pdf"
}
```

## Mapping des Buckets Fictifs

Lorsque `USE_MOCK=true`, les buckets suivants sont créés :
- `gs://mock-bucket-ebotman/test-data/getlist.json` (Confluence)
- `gs://mock-bucket-mktsearch/test-data/getlist.json` (SharePoint)
- `gs://mock-bucket-devtools/test-data/getlist.json` (Google Drive)

## Utilisation

1. Démarrer le serveur avec la simulation GCS : `make start-mock`
2. Tester les différents types de source :
   ```bash
   # Tester Confluence
   curl -X POST http://localhost:8092/loader/list/ebotman \
     -H "Content-Type: application/json" \
     -d '{"get_list_file": "gs://mock-bucket-ebotman/test-data/getlist.json"}'

   # Tester SharePoint
   curl -X POST http://localhost:8092/loader/list/mktsearch \
     -H "Content-Type: application/json" \
     -d '{"get_list_file": "gs://mock-bucket-mktsearch/test-data/getlist.json"}'

   # Tester Google Drive
   curl -X POST http://localhost:8092/loader/list/devtools \
     -H "Content-Type: application/json" \
     -d '{"get_list_file": "gs://mock-bucket-devtools/test-data/getlist.json"}'
   ```

## Note

Ce sont des **fichiers d'exemple commités** pour la documentation. Les données de test temporaires doivent être placées dans `debug-tests/mock-gcs-data/` (ignoré par git).

## Comment Mettre à Jour ou Créer des Types de Source

### Mettre à jour un Type de Source Existant

Si le format `getlist.json` a changé pour un type de source existant (ex: Confluence) :

1.  **Mettre à jour le fichier d'exemple** : Modifiez le fichier `getlist_*.json` correspondant dans ce répertoire.
2.  **Mettre à jour la configuration fictive** : Modifiez `src/kbotloadscheduler/gcs/mock_gcs_config.py` :
    ```python
    # Mettre à jour le dictionnaire source_type_configs
    source_type_configs = {
        "confluence": {
            "sourceType": "confluence",
            "confluence_url": "https://espace.agir.orange.com/",
            "space_key": "VODCASTV",
            "labels": "ravenne",
            "child_page_depth": "15",
            "include_attachements": "true",
            # Ajoutez les nouveaux champs ici
            "new_field": "new_value"
        },
        # ...code existant...
    }
    ```
3.  **Mettre à jour cette documentation** : Ajoutez les nouveaux paramètres de configuration dans la section correspondante ci-dessus.
4.  **Tester les changements** : Lancez `make start-mock` et `make test-mock-endpoint` pour vérifier.

### Créer un Nouveau Type de Source

Pour ajouter la prise en charge d'un nouveau type de source (ex: "onedrive") :

1.  **Créer un fichier d'exemple** : Ajoutez `getlist_onedrive.json` avec la structure getlist complète :
    ```json
    {
      "id": 1079611287,
      "code": "testonedrive99999",
      "label": "Source de Test OneDrive",
      "src_type": "onedrive",
      "configuration": "{\"sourceCurrentLabel\":\"Source de Test OneDrive\",\"sourceType\":\"onedrive\",\"onedrive_url\":\"https://company-my.sharepoint.com/\",\"folder_path\":\"/Documents/Knowledge\",\"include_subfolders\":\"true\"}",
      "last_load_time": 1750069757,
      "next_load_time": 1750156157,
      "load_interval": 24,
      "force_embedding": false,
      "domain_id": 1,
      "domain_code": "TestDomain"
    }
    ```

2.  **Ajouter à la configuration fictive** : Mettez à jour `src/kbotloadscheduler/gcs/mock_gcs_config.py` :
    ```python
    # Ajouter à source_type_configs
    source_type_configs = {
        # ...configurations existantes...
        "onedrive": {
            "sourceType": "onedrive",
            "onedrive_url": "https://company-my.sharepoint.com/",
            "folder_path": "/Documents/Knowledge",
            "include_subfolders": "true",
            "file_types": "docx,xlsx,pptx,pdf"
        }
    }

    # Ajouter à perimeter_configs
    perimeter_configs = {
        # ...configurations existantes...
        "research": {
            "code": "testonedrive99999",
            "label": "OneDrive Recherche",
            "domain_code": "Research",
            "src_type": "onedrive",
            "id": 1079611287
        }
    }
    ```

3.  **Mettre à jour la documentation** : Ajoutez le nouveau type de source à ce README :
    ```markdown
    ### OneDrive
    ```json
    {
      "sourceType": "onedrive",
      "onedrive_url": "https://company-my.sharepoint.com/",
      "folder_path": "/Documents/Knowledge",
      "include_subfolders": "true",
      "file_types": "docx,xlsx,pptx,pdf"
    }
    ```

4.  **Mettre à jour le Makefile** : Ajoutez une commande de test pour le nouveau type de source dans la cible `test-mock-endpoint`.
5.  **Tester le nouveau type de source** :
    ```bash
    make start-mock
    curl -X POST http://localhost:8092/loader/list/research \
      -H "Content-Type: application/json" \
      -d '{"get_list_file": "gs://mock-bucket-research/test-data/getlist.json"}'
    ```

### Notes sur la Structure de Configuration

La configuration de chaque type de source est stockée sous forme de chaîne JSON dans le champ `configuration` de la structure getlist. La configuration doit inclure :

-   `sourceCurrentLabel`: Label lisible par un humain pour la source.
-   `sourceType`: L'identifiant technique du type de source.
-   **Champs spécifiques à la source** : Paramètres uniques à ce type de source (URL, identifiants, filtres, etc.).

### Bonnes Pratiques

1.  **Nomenclature cohérente** : Utilisez `getlist_{type_de_source}.json` pour les fichiers d'exemple.
2.  **Données réalistes** : Utilisez des URL et des valeurs de configuration réalistes dans les exemples.
3.  **ID et codes uniques** : Assurez-vous que chaque exemple de type de source a un `id` et un `code` uniques.
4.  **Tests approfondis** : Testez toujours les nouvelles configurations avec des tests fictifs (mock) et d'intégration.
5.  **Documenter les paramètres** : Expliquez le rôle de chaque paramètre de configuration.