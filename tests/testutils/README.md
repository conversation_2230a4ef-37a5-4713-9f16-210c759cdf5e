# Test Utilities Documentation

## 📋 Vue d'Ensemble

Ce répertoire contient les utilitaires de test et les classes mock utilisées dans les tests du projet
kbot-load-scheduler. Ces outils permettent d'isoler les tests des dépendances externes et de créer des environnements de
test reproductibles.

## 📁 Structure des Fichiers

```
tests/testutils/
├── README.md                 # Cette documentation
├── mock_confluence.py        # Mocks pour l'API Confluence
├── mock_gcs.py              # Mocks pour Google Cloud Storage
├── mock_datetime.py         # Mocks pour les fonctions de date/heure
└── testutils.py             # Utilitaires généraux de test
```

## 🔧 Modules Mock

### 1. **mock_confluence.py**

Contient les mocks pour simuler l'API Confluence et ses composants.

#### Classes Principales

##### `MockConfluence`

Mock principal pour simuler une instance Confluence complète.

```python
from tests.testutils.mock_confluence import MockConfluence

mock_confluence = MockConfluence()
mock_confluence.add_space("DOCS", "Documentation")
mock_confluence.add_content("DOCS", "123", "Test Page", "page", "Content")
```

##### `MockConfluenceAPI` ⭐ **Nouveau**

Mock spécialisé pour les tests end-to-end du ConfluenceLoader.

```python
from tests.testutils.mock_confluence import MockConfluenceAPI

mock_api = MockConfluenceAPI()

# Configuration des pages mockées
pages = [
    {
        "id": "page1",
        "title": "Test Page 1",
        "type": "page",
        "space": {"key": "TEST", "name": "Test Space"},
        "body": {"storage": {"value": "<p>Test content</p>"}},
        "_links": {"webui": "/pages/viewpage.action?pageId=page1"}
    }
]
mock_api.set_mock_pages(pages)

# Configuration des attachments
attachments = [
    {
        "id": "att1",
        "title": "document.pdf",
        "extensions": {"mediaType": "application/pdf"},
        "_links": {"download": "/download/attachments/page1/document.pdf"}
    }
]
mock_api.set_mock_attachments("page1", attachments)
```

**Méthodes Disponibles** :

- `set_mock_pages(pages)` : Configure les pages mockées
- `set_mock_attachments(page_id, attachments)` : Configure les attachments pour une page
- `set_mock_attachment_content(content)` : Configure le contenu des attachments
- `get_mock_pages()` : Récupère les pages mockées
- `get_mock_attachments(page_id)` : Récupère les attachments d'une page
- `get_mock_attachment_content()` : Récupère le contenu des attachments

### 2. **mock_gcs.py**

Contient les mocks pour simuler Google Cloud Storage et ses composants.

#### Classes Principales

##### `MockGcs`

Mock original pour les tests GCS existants.

##### `MockGCSClient` ⭐ **Nouveau**

Mock du client Google Cloud Storage compatible avec l'interface `google.cloud.storage.Client`.

```python
from tests.testutils.mock_gcs import MockGCSClient

client = MockGCSClient()
bucket = client.bucket("test-bucket")
blob = bucket.blob("test-file.txt")
blob.upload_from_string("Hello, World!")
```

**Méthodes Disponibles** :

- `bucket(bucket_name)` : Récupère ou crée un bucket mocké
- `get_bucket(bucket_name)` : Alias pour `bucket()`
- `list_blobs(bucket_name, prefix="")` : Liste les blobs dans un bucket

##### `MockGCSBucket` ⭐ **Nouveau**

Mock d'un bucket Google Cloud Storage.

```python
bucket = MockGCSBucket("test-bucket")
assert bucket.exists() == True

blob = bucket.blob("file.txt")
existing_blob = bucket.get_blob("file.txt")
```

**Méthodes Disponibles** :

- `exists()` : Vérifie si le bucket existe
- `blob(blob_name)` : Crée ou récupère un blob
- `get_blob(blob_name)` : Récupère un blob existant

##### `MockGCSBlob` ⭐ **Nouveau**

Mock d'un blob Google Cloud Storage.

```python
blob = MockGCSBlob("file.txt", bucket)
blob.upload_from_string("Content")
assert blob.exists() == True

content = blob.download_as_bytes()
text = blob.download_as_string()
```

**Méthodes Disponibles** :

- `exists()` : Vérifie si le blob existe
- `upload_from_string(content, content_type=None)` : Upload du contenu
- `download_as_bytes()` : Télécharge le contenu comme bytes
- `download_as_string()` : Télécharge le contenu comme string
- `delete()` : Supprime le blob

#### Fonctions Utilitaires

```python
from tests.testutils.mock_gcs import mock_gcs_bucket, mock_gcs_blob

# Création rapide d'un bucket
bucket = mock_gcs_bucket("my-bucket")

# Création rapide d'un blob
blob = mock_gcs_blob("file.txt", "my-bucket")
```

### 3. **mock_datetime.py**

Contient les mocks pour les fonctions de date et heure.

### 4. **testutils.py**

Contient les utilitaires généraux de test.

## 🎯 Utilisation dans les Tests

### Tests End-to-End Confluence

```python
import pytest
from unittest.mock import patch
from tests.testutils.mock_confluence import MockConfluenceAPI
from tests.testutils.mock_gcs import MockGCSClient

@pytest.fixture
def mock_confluence_client():
    """Fixture pour simuler le client Confluence."""
    mock_api = MockConfluenceAPI()
    
    with patch("kbotloadscheduler.loader.confluence.client.ConfluenceClient.search_content") as mock_search:
        async def mock_search_content(search_criteria):
            # Logique de mock
            return mock_api.get_mock_pages()
        
        mock_search.side_effect = mock_search_content
        yield mock_api

@pytest.fixture
def mock_gcs():
    """Fixture pour simuler Google Cloud Storage."""
    with patch("google.cloud.storage.Client") as mock_client:
        mock_client.return_value = MockGCSClient()
        yield mock_client
```

### Tests Unitaires GCS

```python
from tests.testutils.mock_gcs import MockGcs

def test_gcs_operations(mocker):
    mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
    mock_gcs.add_bucket("gs://test-bucket")
    
    # Votre logique de test ici
```

## 🔧 Configuration des Mocks

### Données de Test Réalistes

Les mocks sont conçus pour utiliser des données qui reflètent la structure réelle des APIs :

```python
# Structure de page Confluence réaliste
page_data = {
    "id": "123456",
    "title": "API Documentation",
    "type": "page",
    "space": {
        "key": "DOCS",
        "name": "Documentation"
    },
    "body": {
        "storage": {
            "value": "<p>Complete API documentation</p>"
        }
    },
    "version": {
        "number": 1,
        "when": "2024-01-15T10:30:00.000Z"
    },
    "_links": {
        "webui": "/spaces/DOCS/pages/123456/API+Documentation"
    },
    "metadata": {
        "labels": {
            "results": [
                {"name": "api"},
                {"name": "documentation"}
            ]
        }
    }
}
```

### État Configurable

Les mocks permettent de configurer différents états pour tester divers scénarios :

```python
# Scénario : Bucket qui n'existe pas
bucket = MockGCSBucket("non-existent-bucket")
bucket._exists = False
assert bucket.exists() == False

# Scénario : Blob avec contenu spécifique
blob = MockGCSBlob("config.json", bucket)
blob.upload_from_string('{"setting": "value"}', "application/json")
```

## 🚀 Bonnes Pratiques

### 1. **Isolation Complète**

- Tous les mocks évitent les appels réseau réels
- Les dépendances externes sont complètement isolées
- Les tests sont reproductibles et rapides

### 2. **Interface Cohérente**

- Les mocks implémentent la même interface que les vraies classes
- Les méthodes retournent des types de données cohérents
- Les erreurs sont simulées de manière réaliste

### 3. **Données de Test Réalistes**

- Utilisation de structures de données qui reflètent la réalité
- IDs, timestamps et métadonnées cohérents
- Cas d'usage variés (succès, erreurs, cas limites)

### 4. **Configuration Flexible**

- Mocks configurables pour différents scénarios
- État modifiable pendant les tests
- Support de multiples cas d'usage

## 🔄 Maintenance

### Synchronisation avec les APIs Réelles

Les mocks doivent être maintenus en synchronisation avec les APIs réelles :

1. **Confluence API** : Vérifier les changements dans l'API REST Confluence
2. **Google Cloud Storage** : Suivre les mises à jour de la bibliothèque `google-cloud-storage`
3. **Interfaces Internes** : Maintenir la compatibilité avec les changements du code de production

### Tests des Mocks

Les mocks eux-mêmes doivent être testés pour garantir leur fiabilité :

```python
def test_mock_confluence_api():
    """Test que MockConfluenceAPI fonctionne correctement."""
    mock_api = MockConfluenceAPI()
    
    # Test de configuration
    pages = [{"id": "test", "title": "Test"}]
    mock_api.set_mock_pages(pages)
    
    # Test de récupération
    retrieved_pages = mock_api.get_mock_pages()
    assert len(retrieved_pages) == 1
    assert retrieved_pages[0]["id"] == "test"
```

## 📈 Évolutions Futures

### Améliorations Prévues

1. **Mocks Plus Sophistiqués** : Simulation d'erreurs réseau, timeouts, etc.
2. **Générateurs de Données** : Création automatique de données de test variées
3. **Validation Automatique** : Vérification de la cohérence avec les APIs réelles
4. **Performance** : Optimisation des mocks pour des tests plus rapides

### Nouveaux Mocks

- **SharePoint API** : Pour les tests du SharepointLoader
- **APIs Kbot** : Pour les tests des services backend
- **Base de Données** : Pour les tests d'intégration avec persistance

Cette documentation sera mise à jour au fur et à mesure de l'évolution des utilitaires de test.
