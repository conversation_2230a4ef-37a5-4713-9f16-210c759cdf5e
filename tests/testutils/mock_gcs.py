# Mock gcs client to get bucket and blobs


class MockGcs:

    def __init__(self, mocker, storage):  # noqa: C901
        self.mocker = mocker
        # storage is the path to the module used inside the tested class
        # (points to the imported google.cloud.storage class)
        # usually it is <test_module>.storage as the import looks like
        # from google.cloud import storage
        self.storage = storage
        self.buckets = {}
        self.blobs = {}

        self.my_storage_mock = mocker.patch(storage)

        def client_mock():
            my_client = mocker.patch(storage + ".client.Client")

            def get_bucket(bucket_name):
                return self.return_bucket(bucket_name)

            mocker.patch.object(my_client, "get_bucket", get_bucket)

            def list_blobs(bucket_or_name, prefix="", delimiter="", end_offset="", max_results=-1):
                blob_list = []
                bucket_prefix = bucket_or_name + "|" + prefix
                nb_result = 0
                # prefixes are a bit strange : they are attached to the iterator when delimiter is set
                prefixes = set()
                for key in self.blobs.keys():
                    end_key = ""
                    if len(end_offset) > 0:
                        end_key = key[-len(end_offset):]
                    if key.startswith(bucket_prefix) and end_key == end_offset:
                        if delimiter != "":
                            realkey = key.split(delimiter)[0].split('|')[1]
                            prefixes.add(realkey)
                        blob_list.append(self.return_blob_with_key(key))
                        nb_result += 1
                        if delimiter == '' and 0 < max_results <= nb_result:
                            break
                mock_iter = self.mocker.patch("google.api_core.page_iterator.HTTPIterator",)
                self.mocker.patch.object(mock_iter, "prefixes", prefixes)

                def iter_mock_def(_self):
                    return iter(blob_list).__iter__()

                self.mocker.patch.object(mock_iter, "__iter__", iter_mock_def)

                return mock_iter

            self.mocker.patch.object(my_client, "list_blobs", list_blobs)
            return my_client

        mocker.patch.object(self.my_storage_mock, "Client", client_mock)

        def blob_mock(bucket, name):
            return self.return_blob(bucket.name, name)

        mocker.patch.object(self.my_storage_mock, "Blob", blob_mock)
        mocker.patch(storage, self.my_storage_mock)

    def add_bucket(self, bucket_name):
        self.buckets[bucket_name] = bucket_name

    def return_bucket(self, bucket_name):
        bucket = self.mocker.MagicMock()
        if bucket_name not in self.buckets:
            self.add_bucket(bucket_name)

        self.mocker.patch.object(bucket, "name", bucket_name)

        def get_blob(blob_name):
            return self.return_blob(bucket_name, blob_name, True)

        self.mocker.patch.object(bucket, "get_blob", get_blob)
        self.mocker.patch.object(bucket, "blob", get_blob)

        def copy_blob(source_blob, destination_bucket, blob_name):
            content_blob = source_blob.download_as_string()
            self.add_blob(destination_bucket.name, blob_name, True, content_blob, len(content_blob))
            return self.return_blob(destination_bucket.name, blob_name, True)

        self.mocker.patch.object(bucket, "copy_blob", copy_blob)

        return bucket

    def add_blob(self, bucket_name, blob_name, do_exist, blob_content, blob_size=0, extra={}):
        self.blobs[bucket_name+"|"+blob_name] = \
            {'exists': do_exist, 'content': blob_content, 'size': blob_size, 'extra': extra}

    def return_blob(self, bucket_name, blob_name, get_blob_call=False):
        key = bucket_name + "|" + blob_name
        if get_blob_call and key not in self.blobs:
            return None

        blob = self.mocker.MagicMock()
        self.mocker.patch.object(blob, "name", blob_name)
        self.mocker.patch.object(blob, "bucket", self.return_bucket(bucket_name))
        blob_extra = []
        if key in self.blobs:
            blob_extra = self.blobs[key].get('extra', {})
        for extra_key in blob_extra:
            self.mocker.patch.object(blob, extra_key, blob_extra.get(extra_key, ''))

        self.add_blob_exists(key, blob)
        self.add_blob_delete(key, blob)
        self.add_blob_download_as_bytes(key, blob)
        self.add_blob_return_size(key, blob)
        self.add_blob_download_as_string(key, blob)
        self.add_blob_upload_from_string(key, bucket_name, blob_name, blob)
        self.add_blob_open(key, blob)

        return blob

    def add_blob_exists(self, key, blob):
        def exists(client):
            return self.blobs[key].get('exists', False) if key is not None and key in self.blobs else False
        self.mocker.patch.object(blob, "exists", exists)

    def add_blob_delete(self, key, blob):
        def delete():
            if key in self.blobs:
                self.blobs.pop(key)

        self.mocker.patch.object(blob, "delete", delete)

    def add_blob_download_as_bytes(self, key, blob):
        def download_as_bytes():
            return self.blobs[key].get("content", "").encode("utf-8")

        self.mocker.patch.object(blob, "download_as_bytes", download_as_bytes)

    def add_blob_return_size(self, key, blob):
        def return_size():
            if key not in self.blobs:
                return None
            return self.blobs[key].get("size", 0)

        self.mocker.patch.object(blob, "size", return_size())

    def add_blob_download_as_string(self, key, blob):
        def download_as_string():
            return self.blobs[key].get("content", "")

        self.mocker.patch.object(blob, "download_as_string", download_as_string)

    def add_blob_upload_from_string(self, key, bucket_name, blob_name, blob):
        def upload_from_string(content):
            if key not in self.blobs:
                self.add_blob(bucket_name, blob_name, do_exist=True, blob_content=content)
            else:
                # Update existing blob content
                self.blobs[key]["content"] = content

        self.mocker.patch.object(blob, "upload_from_string", upload_from_string)

    def return_blob_with_key(self, key):
        bucket_name, blob_name = key.split("|", 2)
        return self.return_blob(bucket_name, blob_name)

    def add_blob_open(self, key, blob):
        def blob_open(mode):
            generator = self.mocker.MagicMock()

            def enter(times):
                file_handler = self.mocker.MagicMock()

                def write(content):
                    if key not in self.blobs:
                        self.blobs[key] = {}
                    self.blobs[key]["content"] = content
                    self.blobs[key]["exists"] = True

                self.mocker.patch.object(file_handler, "write", write)

                return file_handler

            self.mocker.patch.object(generator, "__enter__", enter)

            return generator

        self.mocker.patch.object(blob, "open", blob_open)


class MockGCSClient:
    """Mock simple du client Google Cloud Storage pour les tests"""

    def __init__(self):
        self.buckets = {}
        self.blobs = {}

    def bucket(self, bucket_name):
        """Récupère un bucket mocké (méthode principale utilisée par GCSStorage)"""
        if bucket_name not in self.buckets:
            self.buckets[bucket_name] = MockGCSBucket(bucket_name)
        return self.buckets[bucket_name]

    def get_bucket(self, bucket_name):
        """Récupère un bucket mocké (alias pour compatibilité)"""
        return self.bucket(bucket_name)

    def list_blobs(self, bucket_name, prefix=""):
        """Liste les blobs dans un bucket"""
        bucket_key = f"{bucket_name}|{prefix}"
        return [blob for key, blob in self.blobs.items() if key.startswith(bucket_key)]


class MockGCSBucket:
    """Mock d'un bucket Google Cloud Storage"""

    def __init__(self, name):
        self.name = name
        self.blobs = {}
        self._exists = True  # Par défaut, le bucket existe

    def exists(self):
        """Vérifie si le bucket existe"""
        return self._exists

    def blob(self, blob_name):
        """Crée ou récupère un blob"""
        if blob_name not in self.blobs:
            self.blobs[blob_name] = MockGCSBlob(blob_name, self)
        return self.blobs[blob_name]

    def get_blob(self, blob_name):
        """Récupère un blob existant"""
        return self.blobs.get(blob_name)


class MockGCSBlob:
    """Mock d'un blob Google Cloud Storage"""

    def __init__(self, name, bucket):
        self.name = name
        self.bucket = bucket
        self.content = b""
        self._exists = False

    def exists(self):
        """Vérifie si le blob existe"""
        return self._exists

    def upload_from_string(self, content, content_type=None):
        """Upload du contenu depuis une string"""
        if isinstance(content, str):
            self.content = content.encode("utf-8")
        else:
            self.content = content
        self._exists = True

    def download_as_bytes(self):
        """Télécharge le contenu comme bytes"""
        return self.content

    def download_as_string(self):
        """Télécharge le contenu comme string"""
        return self.content.decode("utf-8") if isinstance(self.content, bytes) else self.content

    def delete(self):
        """Supprime le blob"""
        self._exists = False
        if self.name in self.bucket.blobs:
            del self.bucket.blobs[self.name]


def mock_gcs_bucket(bucket_name="test-bucket"):
    """Fonction utilitaire pour créer un bucket mocké"""
    return MockGCSBucket(bucket_name)


def mock_gcs_blob(blob_name="test-blob", bucket_name="test-bucket"):
    """Fonction utilitaire pour créer un blob mocké"""
    bucket = mock_gcs_bucket(bucket_name)
    return bucket.blob(blob_name)
