import pytest
from kbotloadscheduler.loader.confluence.config.confluence_config import ConfluenceConfig
from kbotloadscheduler.loader.confluence.config.config_validator import ConfigValidator
from kbotloadscheduler.exceptions.confluence_exceptions import ConfluenceConfigurationError

def make_valid_config(**overrides):
    from kbotloadscheduler.loader.confluence.config.confluence_config import (
        AuthConfig, BasicConfig, AttachmentConfig, FilteringConfig, PerformanceConfig, FileProcessingConfig, FutureConfig
    )
    # Map overrides to the correct sub-configs
    auth = AuthConfig(
        confluence_auth_mode=overrides.get("confluence_auth_mode", "global")
    )
    basic = BasicConfig(
        spaces=overrides.get("spaces", ["SPACE"]),
        export_format=overrides.get("export_format", "markdown"),
        max_results=overrides.get("max_results", 10),
        child_page_depth=overrides.get("child_page_depth", 1),
    )
    filtering = FilteringConfig(
        last_modified_days=overrides.get("last_modified_days", 1)
    )
    performance = PerformanceConfig(
        max_parallel_workers=overrides.get("max_parallel_workers", 1),
        cache_ttl_minutes=overrides.get("cache_ttl_minutes", 1),
        circuit_breaker_threshold=overrides.get("circuit_breaker_threshold", 1),
        retry_attempts=overrides.get("retry_attempts", 1),
        retry_delay_seconds=overrides.get("retry_delay_seconds", 2),
    )
    attachments = AttachmentConfig(
        attachment_filter_mode=overrides.get("attachment_filter_mode", "all_current")
    )
    file_processing = FileProcessingConfig(
        max_filename_length=overrides.get("max_filename_length", 25),
        duplicate_filename_strategy=overrides.get("duplicate_filename_strategy", "append_id")
    )
    future = FutureConfig(
        max_child_pages_in_metadata=overrides.get("max_child_pages_in_metadata", 1)
    )
    return ConfluenceConfig(
        auth=auth,
        basic=basic,
        attachments=attachments,
        filtering=filtering,
        performance=performance,
        file_processing=file_processing,
        future=future,
    )

def test_retry_delay_seconds_accepts_int():
    config = make_valid_config(retry_delay_seconds=5)
    validator = ConfigValidator()
    validator._validate_retry_settings(config.performance)  # Should not raise

def test_retry_delay_seconds_rejects_float():
    config = make_valid_config(retry_delay_seconds=2.5)
    validator = ConfigValidator()
    with pytest.raises(ConfluenceConfigurationError):
        validator._validate_retry_settings(config.performance)

def test_retry_delay_seconds_rejects_str():
    config = make_valid_config(retry_delay_seconds="3")
    validator = ConfigValidator()
    with pytest.raises(ConfluenceConfigurationError):
        validator._validate_retry_settings(config.performance)
