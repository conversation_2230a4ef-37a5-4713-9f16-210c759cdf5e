"""Tests for Confluence configuration file processing.

This module tests the configuration validation and factory methods
for the Confluence loader, ensuring proper error handling and
configuration processing.
"""

import unittest
import json

from kbotloadscheduler.loader.confluence.config.config_factory import ConfluenceConfigFactory
from kbotloadscheduler.loader.confluence.config.confluence_config import ConfluenceConfig
from kbotloadscheduler.loader.confluence.client.confluence_client import ConfluenceClient
from kbotloadscheduler.bean.beans import SourceBean
from kbotloadscheduler.exceptions.confluence_exceptions import ConfluenceConfigurationError


class TestFileProcessingConfig(unittest.TestCase):
    """Test cases for Confluence configuration file processing."""

    def setUp(self):
        """Set up test fixtures."""
        self.source = SourceBean(
            id=1,
            code="test_source",
            label="Test Source",
            src_type="confluence",
            configuration="{}",
            last_load_time=0,
            load_interval=6,
            domain_code="test_domain",
            perimeter_code="test_perimeter"
        )

    # --- Configuration Factory Error Tests ---

    def test_missing_spaces_raises_error(self):
        """Test that missing spaces configuration raises ConfluenceConfigurationError."""
        self.source.configuration = json.dumps({})

        with self.assertRaises(ConfluenceConfigurationError) as context:
            ConfluenceConfigFactory.create_from_source(self.source)

        self.assertIn(
             "At least one Confluence space must be specified via the 'spaces' (or legacy 'space_key') configuration.",
            str(context.exception)
        )

    def test_invalid_max_parallel_workers_type_raises_error(self):
        """Test that non-integer max_parallel_workers raises ConfluenceConfigurationError."""
        self.source.configuration = json.dumps({
            "spaces": ["SPACE"],
            "max_parallel_workers": "quatre"
        })

        with self.assertRaises(ConfluenceConfigurationError) as context:
            ConfluenceConfigFactory.create_from_source(self.source)

        self.assertIn(
            "max_parallel_workers must be an integer, but got type: str.",
            str(context.exception)
        )

    def test_negative_child_page_depth_raises_error(self):
        """Test that negative child_page_depth raises ConfluenceConfigurationError."""
        self.source.configuration = json.dumps({
            "spaces": ["SPACE"],
            "child_page_depth": -2
        })

        with self.assertRaises(ConfluenceConfigurationError) as context:
            ConfluenceConfigFactory.create_from_source(self.source)

        self.assertIn(
            "child_page_depth must be a non-negative integer (>= 0), but got: -2.",
            str(context.exception)
        )

    # --- Configuration Factory Success Tests ---

    def test_custom_file_processing_config_creation(self):
        """Test creating configuration with custom file processing settings."""
        self.source.configuration = json.dumps({
            "spaces": ["SPACE"],
            "temp_extensions": [".tmp", ".old"],
            "html_indicators": ["<html>", "<body>"],
            "max_filename_length": 100,
            "default_timeout": 60
        })

        config = ConfluenceConfigFactory.create_from_source(self.source)

        # Verify custom values are applied
        self.assertEqual(config.file_processing.temp_extensions, [".tmp", ".old"])
        self.assertEqual(config.file_processing.html_indicators, [b"<html>", b"<body>"])
        self.assertEqual(config.file_processing.max_filename_length, 100)
        self.assertEqual(config.file_processing.default_timeout, 60)

    def test_partial_custom_config_with_defaults(self):
        """Test creating configuration with partial custom settings and defaults."""
        self.source.configuration = json.dumps({
            "spaces": ["SPACE"],
            "default_timeout": 45
        })

        config = ConfluenceConfigFactory.create_from_source(self.source)

        # Verify custom values
        self.assertEqual(config.file_processing.default_timeout, 45)

        # Verify defaults are preserved
        self.assertEqual(config.file_processing.temp_extensions, [".tmp", ".temp", ".bak", ".backup"])
        self.assertEqual(config.file_processing.html_indicators, [b"<!doctype html", b"<html", b"<head>", b"<body>", b"text/html"])
        self.assertEqual(config.file_processing.max_filename_length, 255)

    # --- Default Configuration Tests ---

    def test_default_file_processing_config_values(self):
        """Test default values for file processing configuration."""
        config = ConfluenceConfig()

        # Verify default values
        self.assertEqual(config.file_processing.temp_extensions, [".tmp", ".temp", ".bak", ".backup"])
        self.assertEqual(config.file_processing.html_indicators, [b"<!doctype html", b"<html", b"<head>", b"<body>", b"text/html"])
        self.assertEqual(config.file_processing.max_filename_length, 255)
        self.assertEqual(config.file_processing.default_timeout, 30)

    def test_is_temp_file_method(self):
        """Test the is_temp_file method functionality."""
        from kbotloadscheduler.loader.confluence.config import is_temp_file
        config = ConfluenceConfig()

        # Test temporary files
        self.assertTrue(is_temp_file(config, "document.tmp"))
        self.assertTrue(is_temp_file(config, "backup.bak"))
        self.assertTrue(is_temp_file(config, "file.TEMP"))  # Case insensitive

        # Test normal files
        self.assertFalse(is_temp_file(config, "document.pdf"))
        self.assertFalse(is_temp_file(config, "image.png"))

    def test_contains_html_indicators_method(self):
        """Test the contains_html_indicators method functionality."""
        from kbotloadscheduler.loader.confluence.config import contains_html_indicators
        config = ConfluenceConfig()

        # Test HTML content
        self.assertTrue(contains_html_indicators(config, b"<!DOCTYPE html><html>"))
        self.assertTrue(contains_html_indicators(config, b"<HTML><HEAD>"))  # Case insensitive
        self.assertTrue(contains_html_indicators(config, b"text/html charset=utf-8"))

        # Test non-HTML content
        self.assertFalse(contains_html_indicators(config, b"%PDF-1.4"))
        self.assertFalse(contains_html_indicators(config, b"Simple text content"))

    def test_is_filename_too_long_method(self):
        """Test the is_filename_too_long method functionality."""
        from kbotloadscheduler.loader.confluence.config import is_filename_too_long
        config = ConfluenceConfig()

        # Normal filename
        self.assertFalse(is_filename_too_long(config, "document.pdf"))

        # Filename too long
        long_filename = "a" * 300 + ".pdf"
        self.assertTrue(is_filename_too_long(config, long_filename))

        # Filename at limit (255 chars)
        limit_filename = "a" * 251 + ".pdf"
        self.assertFalse(is_filename_too_long(config, limit_filename))

    # --- Client Integration Tests ---

    def test_confluence_client_with_custom_config(self):
        """Test Confluence client integration with custom configuration."""
        from unittest.mock import patch

        self.source.configuration = json.dumps({
            "spaces": ["SPACE"],
            "temp_extensions": [".tmp", ".test"],
            "max_filename_length": 100
        })

        config = ConfluenceConfigFactory.create_from_source(self.source)

        # Create client with configuration
        from kbotloadscheduler.loader.confluence.client.confluence_credentials import ConfluenceCredentials
        credentials = ConfluenceCredentials(
            url="https://test.atlassian.net",
            username="test",
            api_token="test"
        )
        with patch('kbotloadscheduler.loader.confluence.client.confluence_client.requests.Session'):
            client = ConfluenceClient(
                credentials,
                config
            )

            # Verify client uses the configuration
            self.assertEqual(client.config, config)
            from kbotloadscheduler.loader.confluence.config import is_temp_file
            self.assertTrue(is_temp_file(client.config, "document.test"))
            self.assertFalse(is_temp_file(client.config, "document.pdf"))
            self.assertEqual(client.config.file_processing.max_filename_length, 100)

    def test_confluence_client_with_default_config(self):
        """Test that Confluence client creates default config when none provided."""
        from unittest.mock import patch

        from kbotloadscheduler.loader.confluence.client.confluence_credentials import ConfluenceCredentials
        from kbotloadscheduler.loader.confluence.config.confluence_config import ConfluenceConfig
        credentials = ConfluenceCredentials(
            url="https://test.atlassian.net",
            username="test",
            api_token="test"
        )
        config = ConfluenceConfig()
        with patch('kbotloadscheduler.loader.confluence.client.confluence_client.requests.Session'):
            client = ConfluenceClient(
                credentials,
                config
            )

            # Verify client has default configuration
            self.assertIsNotNone(client.config)
            self.assertIsInstance(client.config, ConfluenceConfig)


if __name__ == '__main__':
    unittest.main()