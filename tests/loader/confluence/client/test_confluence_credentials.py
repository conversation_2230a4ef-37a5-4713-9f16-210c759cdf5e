"""
Tests unitaires pour ConfluenceCredentials.

Ce module contient des tests complets pour la classe ConfluenceCredentials,
couvrant la création, la validation, et la génération d'en-têtes d'authentification.
"""

import base64
import os
from unittest.mock import patch

import pytest

from src.kbotloadscheduler.loader.confluence.client.confluence_credentials import (
    ConfluenceCredentials,
)


class TestConfluenceCredentials:
    """Tests pour la classe ConfluenceCredentials."""

    def test_init_with_pat_token(self):
        """Test l'initialisation avec un PAT token."""
        credentials = ConfluenceCredentials(
            url="https://test-instance.atlassian.net",
            pat_token="fake_pat_token",
            cloud=True,
        )

        assert credentials.url == "https://test-instance.atlassian.net"
        assert credentials.pat_token == "fake_pat_token"
        assert credentials.username is None
        assert credentials.api_token is None
        assert credentials.cloud is True

    def test_init_with_username_api_token(self):
        """Test l'initialisation avec username et API token."""
        credentials = ConfluenceCredentials(
            url="https://test-instance.atlassian.net",
            username="<EMAIL>",
            api_token="fake_api_token",
            cloud=True,
        )

        assert credentials.url == "https://test-instance.atlassian.net"
        assert credentials.username == "<EMAIL>"
        assert credentials.api_token == "fake_api_token"
        assert credentials.pat_token is None
        assert credentials.cloud is True

    def test_init_with_username_api_token_server(self):
        """Test l'initialisation avec username et API token pour serveur."""
        credentials = ConfluenceCredentials(
            url="https://confluence.company.com",
            username="testuser",
            api_token="testtoken",
            cloud=False,
        )

        assert credentials.url == "https://confluence.company.com"
        assert credentials.username == "testuser"
        assert credentials.api_token == "testtoken"
        assert credentials.cloud is False

    def test_init_minimal(self):
        """Test l'initialisation avec paramètres minimaux."""
        credentials = ConfluenceCredentials(url="https://test.atlassian.net")

        assert credentials.url == "https://test.atlassian.net"
        assert credentials.pat_token is None
        assert credentials.username is None
        assert credentials.api_token is None
        assert credentials.cloud is True  # Valeur par défaut


class TestConfluenceCredentialsValidation:
    """Tests pour la validation des credentials."""

    def test_is_valid_with_pat_token(self):
        """Test la validation avec PAT token."""
        credentials = ConfluenceCredentials(
            url="https://test.atlassian.net",
            pat_token="valid_token"
        )
        assert credentials.is_valid() is True

    def test_is_valid_with_username_api_token(self):
        """Test la validation avec username et API token."""
        credentials = ConfluenceCredentials(
            url="https://test.atlassian.net",
            username="<EMAIL>",
            api_token="valid_token"
        )
        assert credentials.is_valid() is True

    def test_is_valid_with_username_api_token_server(self):
        """Test la validation avec username et API token pour serveur."""
        credentials = ConfluenceCredentials(
            url="https://confluence.company.com",
            username="testuser",
            api_token="testtoken",
            cloud=False
        )
        assert credentials.is_valid() is True

    def test_is_valid_missing_url(self):
        """Test la validation sans URL."""
        credentials = ConfluenceCredentials(url="")
        assert credentials.is_valid() is False

    def test_is_valid_no_auth_method(self):
        """Test la validation sans méthode d'authentification."""
        credentials = ConfluenceCredentials(url="https://test.atlassian.net")
        assert credentials.is_valid() is False

    def test_is_valid_incomplete_basic_auth(self):
        """Test la validation avec authentification basique incomplète."""
        # Username sans API token
        credentials = ConfluenceCredentials(
            url="https://test.atlassian.net",
            username="<EMAIL>"
        )
        assert credentials.is_valid() is False

        # API token sans username
        credentials = ConfluenceCredentials(
            url="https://test.atlassian.net",
            api_token="token"
        )
        assert credentials.is_valid() is False


class TestConfluenceCredentialsAuthHeaders:
    """Tests pour la génération des en-têtes d'authentification."""

    def test_get_auth_headers_pat_token(self):
        """Test la génération d'en-têtes avec PAT token."""
        credentials = ConfluenceCredentials(
            url="https://test.atlassian.net",
            pat_token="test_token"
        )

        headers = credentials.get_auth_headers()

        assert "Authorization" in headers
        assert headers["Authorization"] == "Bearer test_token"

    def test_get_auth_headers_basic_auth(self):
        """Test la génération d'en-têtes avec authentification basique."""
        credentials = ConfluenceCredentials(
            url="https://test.atlassian.net",
            username="<EMAIL>",
            api_token="api_token"
        )

        headers = credentials.get_auth_headers()

        # Vérifier l'en-tête Authorization
        assert "Authorization" in headers
        auth_header = headers["Authorization"]
        assert auth_header.startswith("Basic ")

        # Décoder et vérifier les credentials
        encoded_credentials = auth_header.split(" ")[1]
        decoded_credentials = base64.b64decode(encoded_credentials).decode("utf-8")
        assert decoded_credentials == "<EMAIL>:api_token"

    def test_get_auth_headers_invalid_credentials(self):
        """Test la génération d'en-têtes avec credentials invalides."""
        credentials = ConfluenceCredentials(url="https://test.atlassian.net")

        headers = credentials.get_auth_headers()

        # Doit retourner un dictionnaire vide
        assert headers == {}


class TestConfluenceCredentialsFactoryMethods:
    """Tests pour les méthodes factory."""

    @patch.dict(os.environ, {
        "CONFLUENCE_URL": "https://env.atlassian.net",
        "CONFLUENCE_PAT_TOKEN": "env_pat_token"
    })
    def test_from_env_with_pat_token(self):
        """Test la création depuis les variables d'environnement avec PAT token."""
        credentials = ConfluenceCredentials.from_env()

        assert credentials.url == "https://env.atlassian.net"
        assert credentials.pat_token == "env_pat_token"
        assert credentials.cloud is True

    @patch.dict(os.environ, {
        "CONFLUENCE_URL": "https://env.atlassian.net",
        "CONFLUENCE_USERNAME": "<EMAIL>",
        "CONFLUENCE_API_TOKEN": "env_api_token"
    })
    def test_from_env_with_username_api_token(self):
        """Test la création depuis les variables d'environnement avec username/API token."""
        credentials = ConfluenceCredentials.from_env()

        assert credentials.url == "https://env.atlassian.net"
        assert credentials.username == "<EMAIL>"
        assert credentials.api_token == "env_api_token"
        assert credentials.cloud is True

    @patch.dict(os.environ, {
        "CONFLUENCE_URL": "https://confluence.company.com",
        "CONFLUENCE_USERNAME": "env_user",
        "CONFLUENCE_API_TOKEN": "env_api_token",
        "CONFLUENCE_CLOUD": "false"
    })
    def test_from_env_with_username_api_token_server(self):
        """Test la création depuis les variables d'environnement pour serveur."""
        credentials = ConfluenceCredentials.from_env()

        assert credentials.url == "https://confluence.company.com"
        assert credentials.username == "env_user"
        assert credentials.api_token == "env_api_token"
        assert credentials.cloud is False

    @patch.dict(os.environ, {}, clear=True)
    def test_from_env_missing_url(self):
        """Test la création depuis les variables d'environnement sans URL."""
        credentials = ConfluenceCredentials.from_env()
        # from_env() retourne toujours un objet, même avec URL vide
        assert credentials.url == ""
        assert not credentials.is_valid()

    def test_from_secret_dict_pat_token(self):
        """Test la création depuis un dictionnaire de secrets avec PAT token."""
        secret_dict = {
            "confluence_url": "https://secret.atlassian.net",
            "pat_token": "secret_pat_token",
            "cloud": True
        }

        credentials = ConfluenceCredentials.from_secret_dict(secret_dict)

        assert credentials.url == "https://secret.atlassian.net"
        assert credentials.pat_token == "secret_pat_token"
        assert credentials.cloud is True

    def test_from_secret_dict_username_api_token(self):
        """Test la création depuis un dictionnaire de secrets avec username/API token."""
        secret_dict = {
            "confluence_url": "https://secret.atlassian.net",
            "username": "<EMAIL>",
            "api_token": "secret_api_token",
            "cloud": True
        }

        credentials = ConfluenceCredentials.from_secret_dict(secret_dict)

        assert credentials.url == "https://secret.atlassian.net"
        assert credentials.username == "<EMAIL>"
        assert credentials.api_token == "secret_api_token"
        assert credentials.cloud is True  # Valeur par défaut

    def test_from_secret_dict_empty(self):
        """Test la création depuis un dictionnaire de secrets vide."""
        secret_dict = {"confluence_url": "https://test.atlassian.net", "cloud": True}
        credentials = ConfluenceCredentials.from_secret_dict(secret_dict)

        assert credentials.url == "https://test.atlassian.net"
        assert credentials.pat_token is None
        assert credentials.username is None
        assert credentials.api_token is None


class TestConfluenceCredentialsEdgeCases:
    """Tests pour les cas limites."""

    def test_url_normalization(self):
        """Test la normalisation de l'URL."""
        # URL avec slash final
        credentials = ConfluenceCredentials(
            url="https://test.atlassian.net/",
            pat_token="token"
        )
        assert credentials.url == "https://test.atlassian.net/"

        # URL sans slash final
        credentials = ConfluenceCredentials(
            url="https://test.atlassian.net",
            pat_token="token"
        )
        assert credentials.url == "https://test.atlassian.net"

    def test_cloud_parameter_types(self):
        """Test les différents types pour le paramètre cloud."""
        # Boolean True
        credentials = ConfluenceCredentials(
            url="https://test.atlassian.net",
            pat_token="token",
            cloud=True
        )
        assert credentials.cloud is True

        # Boolean False
        credentials = ConfluenceCredentials(
            url="https://test.atlassian.net",
            pat_token="token",
            cloud=False
        )
        assert credentials.cloud is False

        # String "true" - la dataclass ne fait pas de conversion automatique
        credentials = ConfluenceCredentials(
            url="https://test.atlassian.net",
            pat_token="token",
            cloud="true"
        )
        assert credentials.cloud == "true"

        # String "false" - la dataclass ne fait pas de conversion automatique
        credentials = ConfluenceCredentials(
            url="https://test.atlassian.net",
            pat_token="token",
            cloud="false"
        )
        assert credentials.cloud == "false"

    def test_repr_method(self):
        """Test la représentation string des credentials."""
        credentials = ConfluenceCredentials(
            url="https://test.atlassian.net",
            pat_token="secret_token",
            cloud=True
        )

        repr_str = repr(credentials)

        # Vérifier que les informations sensibles ne sont pas exposées
        assert "secret_token" not in repr_str
        assert "https://test.atlassian.net" in repr_str
        assert "cloud=True" in repr_str
