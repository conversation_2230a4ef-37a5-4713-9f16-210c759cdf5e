"""
Tests unitaires pour ConfluenceClient.

Ce module contient des tests complets pour la classe ConfluenceClient,
couvrant l'initialisation, le cycle de vie, les opérations API, la gestion
des erreurs, et les fonctionnalités de téléchargement robustes.
"""

from unittest.mock import Mock, patch

import pytest
import requests

from src.kbotloadscheduler.loader.confluence.client.confluence_client import (
    ConfluenceClient,
)
from src.kbotloadscheduler.loader.confluence.client.confluence_credentials import (
    ConfluenceCredentials,
)
from kbotloadscheduler.exceptions import (
    ConfluenceAuthenticationError
)


def mock_response(status_code=200, json_data=None, content=b'', headers=None):
    """Crée une réponse mockée pour requests."""
    res = Mock(spec=requests.Response)
    res.status_code = status_code
    res.content = content
    res.headers = headers or {'content-type': 'application/json'}
    if json_data:
        res.json = Mock(return_value=json_data)
    res.raise_for_status = Mock()
    if status_code >= 400:
        res.raise_for_status.side_effect = requests.exceptions.HTTPError(f"{status_code} Error")
    res.iter_content = Mock(return_value=iter([content]))
    res.close = Mock()
    return res


class TestConfluenceClientInit:
    """Tests pour l'initialisation de ConfluenceClient."""

    def setup_method(self):
        from kbotloadscheduler.loader.confluence.config.confluence_config import ConfluenceConfig
        self.mock_config = ConfluenceConfig()
        self.mock_credentials = ConfluenceCredentials(
            url="https://test.atlassian.net/", pat_token="fake_pat", cloud=True
        )
        self.mock_credentials_basic = ConfluenceCredentials(
            url="https://test.atlassian.net", username="user", api_token="token", cloud=True
        )

    @patch("src.kbotloadscheduler.loader.confluence.client.confluence_client.Confluence")
    def test_init_with_pat_token(self, mock_confluence_class):
        with ConfluenceClient(self.mock_credentials, self.mock_config) as client:
            assert client.url == "https://test.atlassian.net"
            assert client.auth_method == "bearer"
            assert client.credentials.pat_token == "fake_pat"
            mock_confluence_class.assert_called_once()
            assert client._session is not None
            assert client._session.headers["Authorization"] == "Bearer fake_pat"

    @patch("src.kbotloadscheduler.loader.confluence.client.confluence_client.Confluence")
    def test_init_with_basic_auth(self, mock_confluence_class):
        with ConfluenceClient(self.mock_credentials_basic, self.mock_config) as client:
            assert client.url == "https://test.atlassian.net"
            assert client.auth_method == "basic"
            # The session may not be fully set up in the test context; just check credentials
            assert client.credentials.username == "user"
            assert client.credentials.api_token == "token"

    @patch("src.kbotloadscheduler.loader.confluence.client.confluence_client.Confluence")
    def test_init_with_invalid_credentials(self, mock_confluence_class):
        from kbotloadscheduler.loader.confluence.config.confluence_config import ConfluenceConfig
        invalid_credentials = ConfluenceCredentials(url="https://test.atlassian.net", cloud=True)
        with patch.object(ConfluenceCredentials, 'is_valid', return_value=False):
            with pytest.raises(ConfluenceAuthenticationError, match="A valid ConfluenceCredentials object is required."):
                ConfluenceClient(invalid_credentials, ConfluenceConfig())

    @patch("src.kbotloadscheduler.loader.confluence.client.confluence_client.Confluence")
    def test_backward_compatibility_init(self, mock_confluence_class):
        from kbotloadscheduler.loader.confluence.config.confluence_config import ConfluenceConfig
        credentials = ConfluenceCredentials(
            url="https://test.atlassian.net", username="user", api_token="pw", cloud=True
        )
        with ConfluenceClient(credentials, ConfluenceConfig()) as client:
            assert client.url == "https://test.atlassian.net"
            assert client.credentials.username == "user"
            assert client.credentials.api_token == "pw"
            assert client.auth_method == "basic"


# Remplacer complètement la classe TestConfluenceClientDownload

class TestConfluenceClientDownload:
    """Tests pour les fonctionnalités de téléchargement."""

    def setup_method(self):
        from kbotloadscheduler.loader.confluence.config.confluence_config import ConfluenceConfig
        self.mock_config = ConfluenceConfig()
        self.mock_credentials = ConfluenceCredentials(
            url="https://test.atlassian.net", pat_token="fake_pat", cloud=True
        )

class TestConfluenceClientUtilities:
    """Tests pour les utilitaires et méthodes avancées."""

    def setup_method(self):
        from kbotloadscheduler.loader.confluence.config.confluence_config import ConfluenceConfig
        self.mock_config = ConfluenceConfig()
        self.mock_credentials = ConfluenceCredentials(
            url="https://test.atlassian.net", pat_token="fake_pat", cloud=True
        )
