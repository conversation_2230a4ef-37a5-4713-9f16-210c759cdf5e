# Tests Confluence - Organisation

## 📋 **Vue d'ensemble**

Cette suite de tests pour Confluence suit une structure modulaire qui reflète l'architecture du code source :

- **Tests unitaires** : Components individuels organisés par module
- **Tests d'intégration** : Fonctionnalité complète du loader
- **Structure miroir** : Organisation identique au code source

## 📁 **Structure des fichiers**

La structure des tests reflète exactement celle du code source :

```
tests/loader/confluence/
├── client/                    # Tests pour src/.../client/
│   ├── test_confluence_client.py
│   └── test_confluence_credentials.py
├── config/                    # Tests pour src/.../config/
├── processors/                # Tests pour src/.../processors/
│   ├── test_drawio_detector.py
│   ├── test_drawio_extractor.py
│   ├── test_drawio_integration.py
│   └── test_drawio_markdown_generator.py
├── search/                    # Tests pour src/.../search/
├── space/                     # Tests pour src/.../space/
└── utils/                     # Tests pour src/.../utils/
```

### 🎯 **Tests par module**

#### `client/` ✅ **CLIENT API**

- **`test_confluence_client.py`** : Tests du client principal Confluence
  - Authentification (PAT token, Basic Auth)
  - Opérations API (pages, espaces, recherche)
  - Téléchargement de pièces jointes
  - Gestion d'erreurs et validation
- **`test_confluence_credentials.py`** : Tests de gestion des identifiants
  - Validation des credentials
  - Génération d'en-têtes d'authentification
  - Méthodes factory (env, secrets)

#### `processors/` ✅ **TRAITEMENT DE CONTENU**

- **`test_drawio_detector.py`** : Détection des diagrammes Draw.io
- **`test_drawio_extractor.py`** : Extraction de métadonnées Draw.io
- **`test_drawio_integration.py`** : Tests d'intégration Draw.io
- **`test_drawio_markdown_generator.py`** : Génération Markdown

#### `config/` 🚧 **CONFIGURATION** (à développer)

- Tests de parsing et validation de configuration
- Conversion de paramètres
- Gestion des paramètres Confluence

#### `search/` 🚧 **RECHERCHE** (à développer)

- Tests de recherche CQL
- Filtres de recherche
- Optimisation des requêtes

#### `space/` 🚧 **ESPACES** (à développer)

- Traitement des espaces
- Gestion des permissions
- Métadonnées d'espace

#### `utils/` 🚧 **UTILITAIRES** (à développer)

- Cache management
- Circuit breaker
- Métriques et retry

## 🚀 **Commandes d'exécution**

### **Tests complets**

```bash
# Tous les tests Confluence
pytest tests/loader/confluence/ -v

# Tests avec couverture
pytest tests/loader/confluence/ --cov=src/kbotloadscheduler/loader/confluence --cov-report=html
```

### **Tests par module**

```bash
# Tests du client
pytest tests/loader/confluence/client/ -v

# Tests des processors
pytest tests/loader/confluence/processors/ -v

# Tests spécifiques
pytest tests/loader/confluence/client/test_confluence_client.py -v
pytest tests/loader/confluence/client/test_confluence_credentials.py -v
```

### **Tests individuels**

```bash
# Client Confluence
python -m pytest tests/loader/confluence/client/test_confluence_client.py -v

# Credentials
python -m pytest tests/loader/confluence/client/test_confluence_credentials.py -v

# Processors Draw.io
python -m pytest tests/loader/confluence/processors/test_drawio_detector.py -v
python -m pytest tests/loader/confluence/processors/test_drawio_extractor.py -v
```

## 🎯 **Guide d'utilisation rapide**

### **Tests de développement standard**

→ `pytest tests/loader/confluence/client/ -v`

### **Tests complets avec couverture**

→ `pytest tests/loader/confluence/ --cov=src/kbotloadscheduler/loader/confluence --cov-report=html`

### **Tests spécialisés selon le module**

→ `pytest tests/loader/confluence/client/test_confluence_client.py -v`
→ `pytest tests/loader/confluence/processors/ -v`

## 🎯 **Statut actuel**

| Module/Fichier                                    | Statut  | Type          | Description                    |
|---------------------------------------------------|---------|---------------|--------------------------------|
| **client/**                                       | ✅ READY | Module        | Tests du client API            |
| `client/test_confluence_client.py`                | ✅ READY | Unit          | Tests du client principal      |
| `client/test_confluence_credentials.py`           | ✅ READY | Unit          | Tests d'authentification      |
| **processors/**                                   | ✅ READY | Module        | Tests des processors           |
| `processors/test_drawio_detector.py`              | ✅ READY | Unit          | Détection Draw.io              |
| `processors/test_drawio_extractor.py`             | ✅ READY | Unit          | Extraction Draw.io             |
| `processors/test_drawio_integration.py`           | ✅ READY | Integration   | Intégration Draw.io            |
| `processors/test_drawio_markdown_generator.py`    | ✅ READY | Unit          | Génération Markdown            |
| **config/**                                       | 🚧 TODO  | Module        | Tests de configuration         |
| **search/**                                       | 🚧 TODO  | Module        | Tests de recherche             |
| **space/**                                        | 🚧 TODO  | Module        | Tests des espaces              |
| **utils/**                                        | 🚧 TODO  | Module        | Tests des utilitaires          |

## 📊 **Métriques de tests**

### Tests modernisés

- ✅ **Structure modulaire** : Organisation par module comme le code source
- ✅ **Tests focalisés** : Un composant par fichier
- ✅ **Couverture complète** : Client et processors couverts
- ✅ **Mocks appropriés** : Isolation des tests
- ✅ **Architecture cohérente** : Structure miroir du code source

## 🔄 **Workflow recommandé**

1. **Développement quotidien** → Tests du module concerné
2. **Tests spécialisés** → Tests individuels selon le composant
3. **Validation complète** → Tous les tests avant commit
4. **CI/CD Pipeline** → Suite complète des tests

## 🎯 **Avantages de la nouvelle organisation**

- **Cohérence** : Structure identique au code source
- **Maintenabilité** : Facile de trouver les tests correspondants
- **Évolutivité** : Ajout simple de nouveaux modules de tests
- **Clarté** : Organisation logique par fonctionnalité
