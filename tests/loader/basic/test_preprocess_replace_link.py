import pytest

from kbotloadscheduler.loader.basic.preprocess import decode_and_replace_all_links

CURRENT_URL = "https://newbasic.sso.orange.fr/public/content/10068"
BASE_URL = "https://newbasic.sso.francetelecom.fr"
PARAMETERS = [
    ("[Absolute uri](/public/content/10068)", f"[Absolute uri]({BASE_URL}/public/content/10068)"),
    (
        '![](../../contents/Procédurespro/ADCP/acr-media.JPG "picto with relative uri")',
        f'![]({BASE_URL}/contents/Proc%C3%A9durespro/ADCP/acr-media.JPG "picto with relative uri")',
    ),
    (
        '![](../contents/Procédurespro/ADCP/acr-media.JPG "picto with relative uri")',
        # Ici (et sur le cas précédent) je m'attendrais à ce qu'on utilise current_url
        # et donc le résultat serait : f'![]({CURRENT_URL}/public/contents/Proc%C3%A9durespro/ADCP
        #                              /acr-media.JPG "picto with relative uri")',
        f'![]({BASE_URL}/contents/Proc%C3%A9durespro/ADCP/acr-media.JPG "picto with relative uri")',
    ),
    (
        "[Anchor link](#my_anchor)",
        # Avant on avait f"{CURRENT_URL}#my_anchor"
        f"[Anchor link]({CURRENT_URL}#my_anchor)",
    ),
    ("[Bad format ] but ok](/public/content/10068)", f"[Bad format ] but ok]({BASE_URL}/public/content/10068)"),
    (
        r"[Good \[ format \] ok](/public/content/10068)",
        r"[Good \[ format \] ok](" + f"{BASE_URL}/public/content/10068)",
    ),
    (
        "[Grille des Mesures Financières (N1)[.xls]](/contents/Procédure/grille_mesures_finacieres.XLS)",
        "[Grille des Mesures Financières (N1)[.xls]](https://newbasic.sso.francetelecom.fr/contents/"
        "Proc%C3%A9dure/grille_mesures_finacieres.XLS)",
    ),
]


class TestPreprocessReplaceLink:
    """
    Test suite for the preprocess.py module.
    """

    @pytest.mark.parametrize("markdown_text, expected_output", PARAMETERS)
    def test_decode_and_replace_all_links_relative_links(self, markdown_text, expected_output):
        result = decode_and_replace_all_links(markdown_text, CURRENT_URL, BASE_URL)

        print(repr(result))
        print(repr(expected_output), "\n")
        assert result.strip() == expected_output.strip()
