import os
import re

from data.sharepoint_repo_test_data import SharepointRepoTestData
from kbotloadscheduler.loader.sharepoint.sharepoint_client import SharepointClient


class TestSharepointClient:

    def test_get_base_folders(self, mocker, requests_mock):
        self.define_request_json_response(requests_mock, "folders", SharepointRepoTestData.GET_BASE_FOLDERS)
        sharepoint_client = self.__init_sharepoint_client__(mocker)
        actual_base_folders = sharepoint_client.get_base_folders()
        assert actual_base_folders == [
            {
                "Name": "Documents partages",
                "ServerRelativeUrl": "/sites/DirectionConnaissanceClientsMarch/Documents partages",
                "TimeCreated": "2025-01-19T00:35:46Z",
                "TimeLastModified": "2025-03-05T15:23:37Z",
                "UniqueId": "70af1aeb-6c60-4072-a2be-1411b2ac0055",
                "type": "SP.Folder",
                "uri": "https://orange0.sharepoint.com/sites/DirectionConnaissanceClientsMarch/"
                       "_api/Web/GetFolderByServerRelativePath(decodedurl='/sites/"
                       "DirectionConnaissanceClientsMarch/Documents%20partages')",
            },
            {
                "Name": "SitePages",
                "ServerRelativeUrl": "/sites/DirectionConnaissanceClientsMarch/SitePages",
                "TimeCreated": "2025-01-19T00:35:47Z",
                "TimeLastModified": "2025-03-05T15:16:44Z",
                "UniqueId": "496a8f2c-deb6-48fe-a374-ce63376ac2b5",
                "type": "SP.Folder",
                "uri": "https://orange0.sharepoint.com/sites/DirectionConnaissanceClientsMarch/"
                       "_api/Web/GetFolderByServerRelativePath(decodedurl='/sites/"
                       "DirectionConnaissanceClientsMarch/SitePages')",
            },
        ]

    def test_get_folder_info_by_path(self, mocker, requests_mock):
        relative_path = SharepointRepoTestData.GET_FOLDER_BY_SERVER_ROOT["d"]["ServerRelativeUrl"]
        self.define_request_json_response(
            requests_mock,
            "GetFolderByServerRelativePath\\(decodedurl='.*'\\)\\?\\$select",
            SharepointRepoTestData.GET_FOLDER_BY_SERVER_ROOT,
        )
        sharepoint_client = self.__init_sharepoint_client__(mocker)
        actual_folders_info = sharepoint_client.get_folder_info(
            "ServerRelativeUrl", {"ServerRelativeUrl": relative_path}
        )
        assert actual_folders_info == {
            "Name": "Etudes Baromètres transverses (baro strat, touch point...)",
            "ServerRelativeUrl": "/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/"
                                 "Etudes Baromètres transverses (baro strat, touch point...)",
            "TimeCreated": "2023-07-21T12:21:50Z",
            "TimeLastModified": "2024-07-16T17:37:35Z",
            "UniqueId": "4a5f44fc-1da0-460c-bbcd-f5e9daa213ff",
            "type": "SP.Folder",
            "uri": "https://orange0.sharepoint.com/sites/EquipeIAGDCCM/_api/Web/GetFolderByServerRelativePath("
                   "decodedurl='/sites/EquipeIAGDCCM/Documents%20partages/Test%20Knowledge%20Bot/"
                   "Etudes%20Barom%C3%A8tres%20transverses%20(baro%20strat,%20touch%20point...)')",
        }

    def test_get_folder_info_by_id(self, mocker, requests_mock):
        folder_id = SharepointRepoTestData.GET_FOLDER_BY_SERVER_ROOT["d"]["UniqueId"]
        self.define_request_json_response(
            requests_mock, "GetFolderById\\('.*'\\)\\?\\$select", SharepointRepoTestData.GET_FOLDER_BY_SERVER_ROOT
        )
        sharepoint_client = self.__init_sharepoint_client__(mocker)
        actual_folders_info = sharepoint_client.get_folder_info("UniqueId", {"UniqueId": folder_id})
        assert actual_folders_info == {
            "Name": "Etudes Baromètres transverses (baro strat, touch point...)",
            "ServerRelativeUrl": "/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/"
                                 "Etudes Baromètres transverses (baro strat, touch point...)",
            "TimeCreated": "2023-07-21T12:21:50Z",
            "TimeLastModified": "2024-07-16T17:37:35Z",
            "UniqueId": "4a5f44fc-1da0-460c-bbcd-f5e9daa213ff",
            "type": "SP.Folder",
            "uri": "https://orange0.sharepoint.com/sites/EquipeIAGDCCM/_api/Web/GetFolderByServerRelativePath("
                   "decodedurl='/sites/EquipeIAGDCCM/Documents%20partages/Test%20Knowledge%20Bot/"
                   "Etudes%20Barom%C3%A8tres%20transverses%20(baro%20strat,%20touch%20point...)')",
        }

    def test_get_sub_folders_by_path(self, mocker, requests_mock):
        relative_path = SharepointRepoTestData.GET_FOLDER_BY_SERVER_ROOT["d"]["ServerRelativeUrl"]
        self.define_request_json_response(
            requests_mock,
            "GetFolderByServerRelativePath\\(decodedurl='.*'\\)/folders\\?\\$select",
            SharepointRepoTestData.GET_FOLDER_BY_SERVER_FOLDERS,
        )
        sharepoint_client = self.__init_sharepoint_client__(mocker)
        actual_sub_folders = sharepoint_client.get_sub_folders(
            "ServerRelativeUrl", {"ServerRelativeUrl": relative_path}
        )
        assert actual_sub_folders == [
            {
                "Name": "Documents partages",
                "ServerRelativeUrl": "/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/"
                                     "Etudes Baromètres transverses (baro strat, touch point...)/subfolderA",
                "TimeCreated": "2025-01-19T00:35:46Z",
                "TimeLastModified": "2025-03-05T15:23:37Z",
                "UniqueId": "70af1aeb-6c60-4072-a2be-1411b2ac0055",
                "type": "SP.Folder",
                "uri": "https://orange0.sharepoint.com/sites/EquipeIAGDCCM/_api/Web/GetFolderByServerRelativePath("
                       "decodedurl='/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/"
                       "Etudes Baromètres transverses (baro strat, touch point...)/subfolderA')",
            },
            {
                "Name": "Documents partages",
                "ServerRelativeUrl": "/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/"
                                     "Etudes Baromètres transverses (baro strat, touch point...)/subfolderB",
                "TimeCreated": "2025-01-19T00:35:46Z",
                "TimeLastModified": "2025-03-05T15:23:37Z",
                "UniqueId": "70af1aeb-6c60-4072-a2be-1411b2ac0055",
                "type": "SP.Folder",
                "uri": "https://orange0.sharepoint.com/sites/EquipeIAGDCCM/_api/Web/GetFolderByServerRelativePath("
                       "decodedurl='/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/"
                       "Etudes Baromètres transverses (baro strat, touch point...)/subfolderB')",
            },
        ]

    def test_get_sub_folders_by_id(self, mocker, requests_mock):
        folder_id = SharepointRepoTestData.GET_FOLDER_BY_SERVER_ROOT["d"]["UniqueId"]
        self.define_request_json_response(
            requests_mock,
            "GetFolderById\\('.*'\\)/folders\\?\\$select",
            SharepointRepoTestData.GET_FOLDER_BY_SERVER_FOLDERS,
        )
        sharepoint_client = self.__init_sharepoint_client__(mocker)
        actual_sub_folders = sharepoint_client.get_sub_folders("UniqueId", {"UniqueId": folder_id})
        assert actual_sub_folders == [
            {
                "Name": "Documents partages",
                "ServerRelativeUrl": "/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/"
                                     "Etudes Baromètres transverses (baro strat, touch point...)/subfolderA",
                "TimeCreated": "2025-01-19T00:35:46Z",
                "TimeLastModified": "2025-03-05T15:23:37Z",
                "UniqueId": "70af1aeb-6c60-4072-a2be-1411b2ac0055",
                "type": "SP.Folder",
                "uri": "https://orange0.sharepoint.com/sites/EquipeIAGDCCM/_api/Web/GetFolderByServerRelativePath("
                       "decodedurl='/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/"
                       "Etudes Baromètres transverses (baro strat, touch point...)/subfolderA')",
            },
            {
                "Name": "Documents partages",
                "ServerRelativeUrl": "/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/"
                                     "Etudes Baromètres transverses (baro strat, touch point...)/subfolderB",
                "TimeCreated": "2025-01-19T00:35:46Z",
                "TimeLastModified": "2025-03-05T15:23:37Z",
                "UniqueId": "70af1aeb-6c60-4072-a2be-1411b2ac0055",
                "type": "SP.Folder",
                "uri": "https://orange0.sharepoint.com/sites/EquipeIAGDCCM/_api/Web/GetFolderByServerRelativePath("
                       "decodedurl='/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/"
                       "Etudes Baromètres transverses (baro strat, touch point...)/subfolderB')",
            },
        ]

    def test_get_folders_files_by_path(self, mocker, requests_mock):
        relative_path = SharepointRepoTestData.GET_FOLDER_BY_SERVER_ROOT["d"]["ServerRelativeUrl"]
        self.define_request_json_response(
            requests_mock,
            "GetFolderByServerRelativePath\\(decodedurl='.*'\\)/files\\?\\$select",
            SharepointRepoTestData.GET_SINGLE_SUBFOLDER_FILES,
        )
        sharepoint_client = self.__init_sharepoint_client__(mocker)
        actual_folders_files = sharepoint_client.get_folders_files(
            "ServerRelativeUrl", {"ServerRelativeUrl": relative_path}
        )
        assert actual_folders_files == [
            {
                "Name": "Benchmark Excellence _ 230417.pdf",
                "ServerRelativeUrl": "/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/"
                                     "Etudes Baromètres transverses (baro strat, touch point...)/"
                                     "Benchmark Excellence _ 230417.pdf",
                "TimeCreated": "2023-07-19T16:08:37Z",
                "TimeLastModified": "2023-07-19T16:08:37Z",
                "UniqueId": "af7b286c-027b-4a20-b9b7-02b752b87b53",
                "type": "SP.File",
                "uri": "https://orange0.sharepoint.com/sites/EquipeIAGDCCM/_api/Web/GetFileByServerRelativePath("
                       "decodedurl='/sites/EquipeIAGDCCM/Documents%20partages/Test%20Knowledge%20Bot/"
                       "Etudes%20Barom%C3%A8tres%20transverses%20(baro%20strat,%20touch%20point...)/"
                       "Benchmark%20Excellence%20_%20230417.pdf')",
            },
            {
                "Name": "EtudeMarche2024.pdf",
                "ServerRelativeUrl": "/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/"
                                     "Etudes Baromètres transverses (baro strat, touch point...)/"
                                     "EtudeMarche2024.pdf",
                "TimeCreated": "2025-01-19T16:08:37Z",
                "TimeLastModified": "2025-01-19T16:08:37Z",
                "UniqueId": "b123286c-027b-4a20-b9b7-02b752b87b53",
                "type": "SP.File",
                "uri": "https://orange0.sharepoint.com/sites/EquipeIAGDCCM/_api/Web/GetFileByServerRelativePath("
                       "decodedurl='/sites/EquipeIAGDCCM/Documents%20partages/Test%20Knowledge%20Bot/"
                       "Etudes%20Barom%C3%A8tres%20transverses%20(baro%20strat,%20touch%20point...)/"
                       "EtudeMarche2024.pdf')",
            },
        ]

    def test_get_folders_files_by_id(self, mocker, requests_mock):
        folder_id = SharepointRepoTestData.GET_FOLDER_BY_SERVER_ROOT["d"]["UniqueId"]
        self.define_request_json_response(
            requests_mock,
            "GetFolderById\\('.*'\\)/files\\?\\$select",
            SharepointRepoTestData.GET_SINGLE_SUBFOLDER_FILES,
        )
        sharepoint_client = self.__init_sharepoint_client__(mocker)
        actual_folders_files = sharepoint_client.get_folders_files("UniqueId", {"UniqueId": folder_id})
        assert actual_folders_files == [
            {
                "Name": "Benchmark Excellence _ 230417.pdf",
                "ServerRelativeUrl": "/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/"
                                     "Etudes Baromètres transverses (baro strat, touch point...)/"
                                     "Benchmark Excellence _ 230417.pdf",
                "TimeCreated": "2023-07-19T16:08:37Z",
                "TimeLastModified": "2023-07-19T16:08:37Z",
                "UniqueId": "af7b286c-027b-4a20-b9b7-02b752b87b53",
                "type": "SP.File",
                "uri": "https://orange0.sharepoint.com/sites/EquipeIAGDCCM/_api/Web/GetFileByServerRelativePath("
                       "decodedurl='/sites/EquipeIAGDCCM/Documents%20partages/Test%20Knowledge%20Bot/"
                       "Etudes%20Barom%C3%A8tres%20transverses%20(baro%20strat,%20touch%20point...)/"
                       "Benchmark%20Excellence%20_%20230417.pdf')",
            },
            {
                "Name": "EtudeMarche2024.pdf",
                "ServerRelativeUrl": "/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/"
                                     "Etudes Baromètres transverses (baro strat, touch point...)/"
                                     "EtudeMarche2024.pdf",
                "TimeCreated": "2025-01-19T16:08:37Z",
                "TimeLastModified": "2025-01-19T16:08:37Z",
                "UniqueId": "b123286c-027b-4a20-b9b7-02b752b87b53",
                "type": "SP.File",
                "uri": "https://orange0.sharepoint.com/sites/EquipeIAGDCCM/_api/Web/GetFileByServerRelativePath("
                       "decodedurl='/sites/EquipeIAGDCCM/Documents%20partages/Test%20Knowledge%20Bot/"
                       "Etudes%20Barom%C3%A8tres%20transverses%20(baro%20strat,%20touch%20point...)/"
                       "EtudeMarche2024.pdf')",
            },
        ]

    def test_get_file_info_by_path(self, mocker, requests_mock):
        relative_path = SharepointRepoTestData.GET_FILE_BY_SERVER_INFOS["d"]["ServerRelativeUrl"]
        self.define_request_json_response(
            requests_mock,
            "GetFileByServerRelativePath\\(decodedurl='.*'\\)\\?\\$select",
            SharepointRepoTestData.GET_FILE_BY_SERVER_INFOS,
        )
        sharepoint_client = self.__init_sharepoint_client__(mocker)
        actual_file_info = sharepoint_client.get_file_info("ServerRelativeUrl", {"ServerRelativeUrl": relative_path})
        assert actual_file_info == {
            "Name": "2209- rapport tendances sociétales 2022.pptx",
            "ServerRelativeUrl": "/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/"
                                 "Etudes Baromètres transverses (baro strat, touch point...)/"
                                 "2209- rapport tendances sociétales 2022.pptx",
            "TimeCreated": "2023-07-21T07:55:32Z",
            "TimeLastModified": "2023-09-20T09:13:04Z",
            "UniqueId": "2dcc222b-a7e8-4396-9395-39dc9efce914",
            "type": "SP.File",
            "uri": "https://orange0.sharepoint.com/sites/EquipeIAGDCCM/_api/Web/GetFileByServerRelativePath("
                   "decodedurl='/sites/EquipeIAGDCCM/Documents%20partages/Test%20Knowledge%20Bot/"
                   "Etudes%20Barom%C3%A8tres%20transverses%20(baro%20strat,%20touch%20point...)/"
                   "2209-%20rapport%20tendances%20soci%C3%A9tales%202022.pptx')",
        }

    def test_get_file_info_by_id(self, mocker, requests_mock):
        file_id = SharepointRepoTestData.GET_FILE_BY_SERVER_INFOS["d"]["UniqueId"]
        self.define_request_json_response(
            requests_mock, "GetFileById\\('.*'\\)\\?\\$select", SharepointRepoTestData.GET_FILE_BY_SERVER_INFOS
        )
        sharepoint_client = self.__init_sharepoint_client__(mocker)
        actual_file_info = sharepoint_client.get_file_info("UniqueId", {"UniqueId": file_id})
        assert actual_file_info == {
            "Name": "2209- rapport tendances sociétales 2022.pptx",
            "ServerRelativeUrl": "/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/"
                                 "Etudes Baromètres transverses (baro strat, touch point...)/"
                                 "2209- rapport tendances sociétales 2022.pptx",
            "TimeCreated": "2023-07-21T07:55:32Z",
            "TimeLastModified": "2023-09-20T09:13:04Z",
            "UniqueId": "2dcc222b-a7e8-4396-9395-39dc9efce914",
            "type": "SP.File",
            "uri": "https://orange0.sharepoint.com/sites/EquipeIAGDCCM/_api/Web/GetFileByServerRelativePath("
                   "decodedurl='/sites/EquipeIAGDCCM/Documents%20partages/Test%20Knowledge%20Bot/"
                   "Etudes%20Barom%C3%A8tres%20transverses%20(baro%20strat,%20touch%20point...)/"
                   "2209-%20rapport%20tendances%20soci%C3%A9tales%202022.pptx')",
        }

    def test_get_file_properties_by_path(self, mocker, requests_mock):
        relative_path = SharepointRepoTestData.GET_FILE_BY_SERVER_INFOS["d"]["ServerRelativeUrl"]
        self.define_request_json_response(
            requests_mock,
            "GetFileByServerRelativePath\\(decodedurl='.*'\\)/properties",
            SharepointRepoTestData.GET_FILE_BY_SERVER_PROPERTIES,
        )
        sharepoint_client = self.__init_sharepoint_client__(mocker)
        actual_file_properties = sharepoint_client.get_file_properties(
            "ServerRelativeUrl", {"ServerRelativeUrl": relative_path}
        )
        assert actual_file_properties == {
            "ClassificationContentMarkingFooterText": "Orange Restricted",
            "Slides": 27,
            "type": "SP.PropertyValues",
            "uri": "https://orange0.sharepoint.com/sites/DirectionConnaissanceClientsMarch/"
                   "_api/web/GetFileByServerRelativePath(decodedurl='/sites/DirectionConnaissanceClientsMarch/"
                   "Documents%20partages/Etudes%20Marketing%20Grand%20Public/"
                   "Soci%C3%A9t%C3%A9%20&%20Anticipation%20(Sociovision,%20Tracker%20Actus,"
                   "%20tendances%20soci%C3%A9tales,%20RSE,%20cibles%E2%80%A6)/"
                   "RSE/D%C3%A9cathlon_RSE_#1.pptx')/properties",
            "vti_x005f_aspxpagesnextbsn": "100",
            "vti_x005f_filesize": 9708779,
            "vti_x005f_modifiedby": "i:0#.f|membership|<EMAIL>",
            "vti_x005f_parentid": "{5EEBD535-9AC4-427B-9D6F-ECA0172C25BB}",
            "vti_x005f_previewinvalidtime": "2025-01-24T13:58:01",
            "vti_x005f_principalcount": 3,
            "vti_x005f_rtag": "rt:42978575-C6A0-4F26-BC2B-215EDD3188E4@00000000002",
            "vti_x005f_sourcecontrolversion": "V1.0",
            "vti_x005f_sourcesiteid": "094a9c0d-84aa-4da2-b8a3-9ffc78283511",
            "vti_x005f_timecreated": "2023-10-19T20:12:54",
            "vti_x005f_timelastmodified": "2023-10-19T20:12:54",
        }

    def test_get_file_properties_by_id(self, mocker, requests_mock):
        file_id = SharepointRepoTestData.GET_FILE_BY_SERVER_INFOS["d"]["UniqueId"]
        self.define_request_json_response(
            requests_mock, "GetFileById\\('.*'\\)/properties", SharepointRepoTestData.GET_FILE_BY_SERVER_PROPERTIES
        )
        sharepoint_client = self.__init_sharepoint_client__(mocker)
        actual_file_properties = sharepoint_client.get_file_properties("UniqueId", {"UniqueId": file_id})
        assert actual_file_properties == {
            "ClassificationContentMarkingFooterText": "Orange Restricted",
            "Slides": 27,
            "type": "SP.PropertyValues",
            "uri": "https://orange0.sharepoint.com/sites/DirectionConnaissanceClientsMarch/"
                   "_api/web/GetFileByServerRelativePath(decodedurl='/sites/DirectionConnaissanceClientsMarch/"
                   "Documents%20partages/Etudes%20Marketing%20Grand%20Public/"
                   "Soci%C3%A9t%C3%A9%20&%20Anticipation%20(Sociovision,%20Tracker%20Actus,"
                   "%20tendances%20soci%C3%A9tales,%20RSE,%20cibles%E2%80%A6)/"
                   "RSE/D%C3%A9cathlon_RSE_#1.pptx')/properties",
            "vti_x005f_aspxpagesnextbsn": "100",
            "vti_x005f_filesize": 9708779,
            "vti_x005f_modifiedby": "i:0#.f|membership|<EMAIL>",
            "vti_x005f_parentid": "{5EEBD535-9AC4-427B-9D6F-ECA0172C25BB}",
            "vti_x005f_previewinvalidtime": "2025-01-24T13:58:01",
            "vti_x005f_principalcount": 3,
            "vti_x005f_rtag": "rt:42978575-C6A0-4F26-BC2B-215EDD3188E4@00000000002",
            "vti_x005f_sourcecontrolversion": "V1.0",
            "vti_x005f_sourcesiteid": "094a9c0d-84aa-4da2-b8a3-9ffc78283511",
            "vti_x005f_timecreated": "2023-10-19T20:12:54",
            "vti_x005f_timelastmodified": "2023-10-19T20:12:54",
        }

    def test_get_file_bytes_content_by_path(self, mocker, requests_mock):
        relative_path = SharepointRepoTestData.GET_FILE_BY_SERVER_INFOS["d"]["ServerRelativeUrl"]
        content_of_file = "The content of the file".encode()
        self.define_request_byte_response(
            requests_mock, "GetFileByServerRelativePath\\(decodedurl='.*'\\)/\\$value", content_of_file
        )
        sharepoint_client = self.__init_sharepoint_client__(mocker)
        actual_file_content = sharepoint_client.get_file_bytes_content(
            "ServerRelativeUrl", {"ServerRelativeUrl": relative_path}
        )
        assert actual_file_content == content_of_file

    def test_get_file_bytes_content_by_id(self, mocker, requests_mock):
        file_id = SharepointRepoTestData.GET_FILE_BY_SERVER_INFOS["d"]["UniqueId"]
        content_of_file = "The content of the file".encode()
        self.define_request_byte_response(requests_mock, "GetFileById\\('.*'\\)/\\$value", content_of_file)
        sharepoint_client = self.__init_sharepoint_client__(mocker)
        actual_file_content = sharepoint_client.get_file_bytes_content("UniqueId", {"UniqueId": file_id})
        assert actual_file_content == content_of_file

    @staticmethod
    def __init_sharepoint_client__(mocker):
        def get_token():
            return "fake_token"

        sharepoint_credentials_mock = mocker.patch(
            "kbotloadscheduler.loader.sharepoint.sharepoint_credentials.SharepointCredentials"
        )
        mocker.patch.object(sharepoint_credentials_mock, "get_access_token", get_token)

        sharepoint_url = os.getenv("URL_SERVICE_SHAREPOINT")
        site_name = SharepointRepoTestData.SITE_NAME
        sharepoint_client = SharepointClient(sharepoint_credentials_mock, sharepoint_url, site_name)

        return sharepoint_client

    @staticmethod
    def define_request_json_response(requests_mock, url_to_match, response):
        sharepoint_url = os.getenv("URL_SERVICE_SHAREPOINT")
        site_name = SharepointRepoTestData.SITE_NAME
        base_url = f"{sharepoint_url}/sites/{site_name}"
        full_url = f"{base_url}/_api/web/{url_to_match}"
        print("######################### FULL URL === " + full_url)
        files_url_matcher = re.compile(full_url)
        requests_mock.get(files_url_matcher, json=response)

    @staticmethod
    def define_request_byte_response(requests_mock, url_to_match, response):
        sharepoint_url = os.getenv("URL_SERVICE_SHAREPOINT")
        site_name = SharepointRepoTestData.SITE_NAME
        base_url = f"{sharepoint_url}/sites/{site_name}"
        full_url = f"{base_url}/_api/web/{url_to_match}"
        print("######################### FULL URL === " + full_url)
        files_url_matcher = re.compile(full_url)
        requests_mock.get(files_url_matcher, content=response)
