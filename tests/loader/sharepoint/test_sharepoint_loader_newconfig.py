import json
from datetime import datetime, timezone

from kbotloadscheduler.bean.beans import DocumentBean, SourceBean
from kbotloadscheduler.loader.sharepoint.sharepoint_loader import SharepointLoader
from testutils.mock_gcs import MockGcs


# L'objectif de cette classe est de tester une nouvelle configuration sharepoint.
#
# Dans conf/tests/PERIMETER_CODE-sharepoint-client-config/secret mettre le json client config
# Dans conf/tests/PERIMETER_CODE-sharepoint-client-private-key/secret mettre la private key protégée
#
# Ci-dessous remplir :
#  - PERIMETER_CODE : le code du périmètre (correspond au nom de fichier)
#  - SITE_NAME : le nom du site sharepoint
#  - DIRECTORY : le répertoire sous lequel lister les documents
#  - DIRECTORY_ID : l'id du répertoire sous lequel lister les documents
#  - DOCUMENT : le nom d'un fichier à récupérer dans le répertoire ci-dessus
#  - DOCUMENT_ID : l'id d'un fichier à récupérer dans le répertoire ci-dessus
#
# Pour jouer les tests il faut modifier le prefixe notest_ et le remplacer par test_
#
# Jouer le test test_get_document_list : il échoue, mais cela vous permet de vérifier
# si la liste des documents a bien été retournée
#
# Jouer le test test_get_document : il échoue, mais cela vous permet de vérifier les metadata retournées.
# Il faut en particulier vérifier le champ location : s'il est rempli, c'est qu'on a bien récupéré le document
#
# Les autres tests permettent de vérifier directement le sharepoint_client pour des tests plus précis.
# Pareillement, le test échoue, mais permet de vérifier le contenu de la réponse
#
# !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
# !!! ATTENTION !!! Ne jamais mettre vos modifs de ce fichier ou le secrets dans gitlab !!!
# !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
#


class TestSharepointLoaderNewConfig:
    PERIMETER_CODE = "resmob"
    SITE_NAME = "AnimationDeploiementReseauMobileDTR"
    DIRECTORY = "1_PROCESSUS"
    DIRECTORY_ID = "98393de8-a0e1-4c23-8f99-59d446f92e08"
    DOCUMENT = "Processus_RE-CPR-DEP_Travaux_Bailleurs_TOTEM.pdf"
    DOCUMENT_ID = "cc630bc2-f151-4b00-8b1c-c30cf3520faf"
    CONF_SOURCE = {
        "site_name": SITE_NAME,
        "relative_directory": DIRECTORY,
    }
    SOURCE = {
        "id": 1,
        "code": f"src_{PERIMETER_CODE}",
        "label": f"source {PERIMETER_CODE}",
        "src_type": "sharepoint",
        "configuration": json.dumps(CONF_SOURCE),
        "last_load_time": 1726156483,
        "next_load_time": 1726156483,
        "load_interval": "24",
        "domain_code": f"dom_{PERIMETER_CODE}",
        "perimeter_code": PERIMETER_CODE,
    }

    def notest_get_document_list(self, client):
        config_with_secret = client.app.container.configWithSecret()
        sharepoint_loader = SharepointLoader(config_with_secret)
        actual_document_list = sharepoint_loader.get_document_list(SourceBean(**self.SOURCE))
        assert actual_document_list == {}

    def notest_get_document(self, client, mocker):
        output_path = "gs://document_tmp-dev/shptests"
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket("gs://document_tmp-dev")
        config_with_secret = client.app.container.configWithSecret()
        sharepoint_loader = SharepointLoader(config_with_secret)
        site_name = self.SITE_NAME
        dir_path = self.DIRECTORY
        file_name = self.DOCUMENT
        file_relative_url = f"/sites/{site_name}/{dir_path}/{file_name}"
        document = DocumentBean(
            id=f"domA|srcA1|{self.DOCUMENT_ID}",
            path=f"https://orange0.sharepoint.com{file_relative_url}",
            name=file_relative_url,
            modification_time=datetime(2024, 1, 2, 12, 46, 52, tzinfo=timezone.utc),
        )
        doc_metadata = sharepoint_loader.get_document(SourceBean(**self.SOURCE), document, output_path)

        assert doc_metadata == {}

    def notest_sharepoint_client_get_base_folders(self, client):
        config_with_secret = client.app.container.configWithSecret()
        sharepoint_loader = SharepointLoader(config_with_secret)
        source = SourceBean(**self.SOURCE)
        sharepoint_client = sharepoint_loader.get_sharepoint_client(source)
        base_folder_info = sharepoint_client.get_base_folders()

        assert base_folder_info == {}

    def notest_sharepoint_client_get_folder_info_by_path(self, client):
        config_with_secret = client.app.container.configWithSecret()
        sharepoint_loader = SharepointLoader(config_with_secret)
        source = SourceBean(**self.SOURCE)
        sharepoint_client = sharepoint_loader.get_sharepoint_client(source)
        site_name = self.SITE_NAME
        dir_path = self.DIRECTORY
        folder_relative_url = f"/sites/{site_name}/{dir_path}"
        doc_info = sharepoint_client.get_folder_info_by_path(folder_relative_url)

        assert doc_info == {}

    def notest_sharepoint_client_get_folder_info_by_id(self, client):
        config_with_secret = client.app.container.configWithSecret()
        sharepoint_loader = SharepointLoader(config_with_secret)
        source = SourceBean(**self.SOURCE)
        sharepoint_client = sharepoint_loader.get_sharepoint_client(source)
        folder_id = self.DIRECTORY_ID
        doc_info = sharepoint_client.get_folder_info_by_id(folder_id)

        assert doc_info == {}

    def notest_sharepoint_client_get_sub_folders_by_path(self, client):
        config_with_secret = client.app.container.configWithSecret()
        sharepoint_loader = SharepointLoader(config_with_secret)
        source = SourceBean(**self.SOURCE)
        sharepoint_client = sharepoint_loader.get_sharepoint_client(source)
        site_name = self.SITE_NAME
        dir_path = self.DIRECTORY
        folder_relative_url = f"/sites/{site_name}/{dir_path}"
        sub_folders = sharepoint_client.get_sub_folders_by_path(folder_relative_url)

        assert sub_folders == []

    def notest_sharepoint_client_get_sub_folders_by_id(self, client):
        config_with_secret = client.app.container.configWithSecret()
        sharepoint_loader = SharepointLoader(config_with_secret)
        source = SourceBean(**self.SOURCE)
        sharepoint_client = sharepoint_loader.get_sharepoint_client(source)
        folder_id = self.DIRECTORY_ID
        sub_folders = sharepoint_client.get_sub_folders_by_id(folder_id)

        assert sub_folders == []

    def notest_sharepoint_client_get_folder_files_by_path(self, client):
        config_with_secret = client.app.container.configWithSecret()
        sharepoint_loader = SharepointLoader(config_with_secret)
        source = SourceBean(**self.SOURCE)
        sharepoint_client = sharepoint_loader.get_sharepoint_client(source)
        site_name = self.SITE_NAME
        dir_path = self.DIRECTORY
        folder_relative_url = f"/sites/{site_name}/{dir_path}"
        files_list = sharepoint_client.get_folders_files_by_path(folder_relative_url)

        assert files_list == []

    def notest_sharepoint_client_get_folder_files_by_id(self, client):
        config_with_secret = client.app.container.configWithSecret()
        sharepoint_loader = SharepointLoader(config_with_secret)
        source = SourceBean(**self.SOURCE)
        sharepoint_client = sharepoint_loader.get_sharepoint_client(source)
        folder_id = self.DIRECTORY_ID
        files_list = sharepoint_client.get_folders_files_by_id(folder_id)

        assert files_list == []

    def notest_sharepoint_client_get_file_info_by_path(self, client):
        config_with_secret = client.app.container.configWithSecret()
        sharepoint_loader = SharepointLoader(config_with_secret)
        source = SourceBean(**self.SOURCE)
        sharepoint_client = sharepoint_loader.get_sharepoint_client(source)
        site_name = self.SITE_NAME
        dir_path = self.DIRECTORY
        file_name = self.DOCUMENT
        file_relative_url = f"/sites/{site_name}/{dir_path}/{file_name}"
        doc_info = sharepoint_client.get_file_info_by_path(file_relative_url)

        assert doc_info == {}

    def notest_sharepoint_client_get_file_info_by_id(self, client):
        config_with_secret = client.app.container.configWithSecret()
        sharepoint_loader = SharepointLoader(config_with_secret)
        source = SourceBean(**self.SOURCE)
        sharepoint_client = sharepoint_loader.get_sharepoint_client(source)
        file_id = self.DOCUMENT_ID
        doc_info = sharepoint_client.get_file_info_by_id(file_id)

        assert doc_info == {}

    def notest_sharepoint_client_get_file_properties_by_path(self, client):
        config_with_secret = client.app.container.configWithSecret()
        sharepoint_loader = SharepointLoader(config_with_secret)
        source = SourceBean(**self.SOURCE)
        sharepoint_client = sharepoint_loader.get_sharepoint_client(source)
        site_name = self.SITE_NAME
        dir_path = self.DIRECTORY
        file_name = self.DOCUMENT
        file_relative_url = f"/sites/{site_name}/{dir_path}/{file_name}"
        doc_info = sharepoint_client.get_file_properties_by_path(file_relative_url)

        assert doc_info == {}

    def notest_sharepoint_client_get_file_properties_by_id(self, client):
        config_with_secret = client.app.container.configWithSecret()
        sharepoint_loader = SharepointLoader(config_with_secret)
        source = SourceBean(**self.SOURCE)
        sharepoint_client = sharepoint_loader.get_sharepoint_client(source)
        file_id = self.DOCUMENT_ID
        doc_info = sharepoint_client.get_file_properties_by_id(file_id)

        assert doc_info == {}
