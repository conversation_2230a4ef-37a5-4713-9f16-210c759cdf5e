"""
Test data for Confluence loader tests.
Following the SharePoint pattern for clean, maintainable test data.
"""

import json


class ConfluenceRepoTestData:
    """Test data for Confluence loader tests."""

    # Test configuration
    REPO_BUCKET = "fakebucket"
    SPACE_KEY = "TEST"
    PERIMETER_CODE = "test_perimeter"
    DOMAIN_CODE = "test_domain"
    SOURCE_CODE = "test_confluence"

    # Mock Confluence credentials
    FAKE_CREDENTIALS_CONFIG = {
        "url": "https://test-instance.atlassian.net",
        "pat_token": "fake_pat_token",
        "cloud": False,
    }

    # Mock space content response
    SPACE_CONTENT_RESPONSE = [
        {
            "id": "123456",
            "type": "page",
            "title": "Test Page 1",
            "space": {"key": "TEST"},
            "version": {"when": "2024-01-15T10:30:00.000Z", "number": 1},
            "_links": {
                "webui": "/spaces/TEST/pages/123456/Test+Page+1",
                "self": "https://test-instance.atlassian.net/wiki/rest/api/content/123456",
            },
            "ancestors": [],
            "children": {"attachment": {"results": [], "size": 0}},
        },
        {
            "id": "789012",
            "type": "page",
            "title": "Test Page 2 with Attachment",
            "space": {"key": "TEST"},
            "version": {"when": "2024-01-20T14:15:00.000Z", "number": 2},
            "_links": {
                "webui": "/spaces/TEST/pages/789012/Test+Page+2+with+Attachment",
                "self": "https://test-instance.atlassian.net/wiki/rest/api/content/789012",
            },
            "ancestors": [],
            "children": {
                "attachment": {
                    "results": [
                        {
                            "id": "att123",
                            "title": "test_document.pdf",
                            "mediaType": "application/pdf",
                            "fileSize": 1024000,
                            "version": {"when": "2024-01-20T14:00:00.000Z"},
                            "_links": {"download": "/wiki/download/attachments/789012/test_document.pdf"},
                        }
                    ],
                    "size": 1,
                }
            },
        },
    ]

    # Mock page content with body
    PAGE_CONTENT_RESPONSE = {
        "id": "123456",
        "type": "page",
        "title": "Test Page 1",
        "space": {"key": "TEST"},
        "body": {
            "storage": {
                "value": "<p>This is test page content with <strong>formatting</strong></p>",
                "representation": "storage",
            }
        },
        "version": {"when": "2024-01-15T10:30:00.000Z", "number": 1},
        "_links": {"webui": "/spaces/TEST/pages/123456/Test+Page+1"},
    }

    # Mock attachments response
    ATTACHMENTS_RESPONSE = [
        {
            "id": "att123",
            "title": "test_document.pdf",
            "mediaType": "application/pdf",
            "fileSize": 1024000,
            "version": {"when": "2024-01-20T14:00:00.000Z"},
            "_links": {"download": "/wiki/download/attachments/789012/test_document.pdf"},
        }
    ]

    # Source configuration
    CONF_SOURCE = {
        "space_key": SPACE_KEY,
        "confluence_url": FAKE_CREDENTIALS_CONFIG["url"],
        "max_results": 100,
        "include_attachments": True,
        "content_types": ["page"],
        "last_modified_days": 30,
    }

    SOURCE = {
        "id": 1,
        "code": SOURCE_CODE,
        "label": "Test Confluence Source",
        "src_type": "confluence",
        "configuration": json.dumps(CONF_SOURCE),
        "last_load_time": 1726156483,
        "load_interval": 24,
        "domain_code": DOMAIN_CODE,
        "perimeter_code": PERIMETER_CODE,
        "force_embedding": False,
    }

    # Expected document list results
    EXPECTED_DOCUMENTS = [
        {"id": f"{DOMAIN_CODE}|{SOURCE_CODE}|123456", "name": "Test Page 1", "type": "page"},
        {"id": f"{DOMAIN_CODE}|{SOURCE_CODE}|789012", "name": "Test Page 2 with Attachment", "type": "page"},
        {"id": f"{DOMAIN_CODE}|{SOURCE_CODE}|att123", "name": "test_document.pdf", "type": "attachment"},
    ]

    # Mock attachment content
    ATTACHMENT_CONTENT = b"fake pdf content for testing"
