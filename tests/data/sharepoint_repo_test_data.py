import json
import os
from datetime import datetime


class SharepointResponseLoader:
    ROOT_TEST = os.environ.get("ROOT_TEST", ".")

    @classmethod
    def get_json_from_file(cls, file_path):
        full_file_path = cls.ROOT_TEST + "/data/resources/sharepoint/" + file_path
        with open(full_file_path) as file:
            return json.load(file)


class SharepointRepoTestData:
    REPO_BUCKET = "fakebucket"
    SITE_NAME = "EquipeIAGDCCM"
    PERIMETER_CODE = "perimB"
    DOMAINE_CODE = "domA"
    SOURCE_CODE = "srcA1"
    LOAD_DATE = "202402121630"
    FAKE_CLIENT_CONFIG = """
    {"tenant_id": "tenant", "client_id": "client_id", "certificate_thumbprint": "ct", "password": "pwd"}
    """

    GET_BASE_FOLDERS = SharepointResponseLoader.get_json_from_file("getDocList/base.json")
    GET_FOLDER_BY_SERVER_ROOT = SharepointResponseLoader.get_json_from_file("getDocList/root.json")
    GET_FOLDER_BY_SERVER_FILES = SharepointResponseLoader.get_json_from_file("getDocList/files.json")
    GET_FOLDER_BY_SERVER_FOLDERS = SharepointResponseLoader.get_json_from_file("getDocList/folders.json")
    GET_FOLDER_BY_SERVER_EMPTY_FOLDERS = SharepointResponseLoader.get_json_from_file("getDocList/empty_folders.json")
    GET_SINGLE_SUBFOLDER_FILES = SharepointResponseLoader.get_json_from_file("getDocList/single_subfolders_files.json")
    GET_FILE_BY_SERVER_INFOS = SharepointResponseLoader.get_json_from_file("getDocument/fileByServer.json")
    GET_FILE_BY_SERVER_PROPERTIES = SharepointResponseLoader.get_json_from_file("getDocument/fileProperties.json")

    CONF_SOURCE = {
        "site_name": SITE_NAME,
        "relative_directory": "Documents partages/Test Knowledge Bot",
    }

    SOURCE = {
        "id": 1,
        "code": SOURCE_CODE,
        "label": "source A 1",
        "src_type": "sharepoint",
        "configuration": json.dumps(CONF_SOURCE),
        "last_load_time": 1726156483,
        "load_interval": "24",
        "domain_code": DOMAINE_CODE,
        "perimeter_code": PERIMETER_CODE,
    }
    DATE_A1 = datetime(2024, 9, 12, 12, 36, 24)
    DATE_CREATED_A1 = datetime(2023, 9, 12, 12, 36, 24)

    FIRST_DOCUMENT_IN_DOCUMENT_LIST_ID = (
        "domA|srcA1|/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/"
        "Etudes Baromètres transverses (baro strat, touch point...)/Benchmark Excellence _ 230417.pdf"
    )

    SINGLE_FOLDER_INFO_INPUT = {
        "Name": "Etudes Baromètres transverses (baro strat, touch point...)",
        "ServerRelativeUrl": "/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/"
                             "Etudes Baromètres transverses (baro strat, touch point...)",
        "TimeCreated": "2023-07-21T12:21:50Z",
        "TimeLastModified": "2024-07-16T17:37:35Z",
        "UniqueId": "4a5f44fc-1da0-460c-bbcd-f5e9daa213ff",
        "type": "SP.Folder",
        "uri": "https://orange0.sharepoint.com/sites/EquipeIAGDCCM/_api/Web/GetFolderByServerRelativePath"
               "(decodedurl='/sites/EquipeIAGDCCM/Documents%20partages/Test%20Knowledge%20Bot/"
               "Etudes%20Barom%C3%A8tres%20transverses%20(baro%20strat,%20touch%20point...)')",
    }

    WORK_BUCKET = f"mon_bucket-{PERIMETER_CODE}"

    RELATIVE_OUTPUT = f"{LOAD_DATE}/{DOMAINE_CODE}/{SOURCE_CODE}"
    GET_LIST_FILE = f"{RELATIVE_OUTPUT}/getlist/getlist.json"
    GET_LIST_FILE_DONE = f"{RELATIVE_OUTPUT}/getlist/getlist.done"
    LIST_FILE = f"{RELATIVE_OUTPUT}/list/list.json"
    LIST_FILE_DONE = f"{RELATIVE_OUTPUT}/list/list.done"
    GET_DOC_FILE = f"{RELATIVE_OUTPUT}/getdoc/getdoc.json"
    GET_DOC_FILE_DONE = f"{RELATIVE_OUTPUT}/getdoc/getdoc.done"
    BASE_OUTPUT = f"gs://{WORK_BUCKET}/{RELATIVE_OUTPUT}"
