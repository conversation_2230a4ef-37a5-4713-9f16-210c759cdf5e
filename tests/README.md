# Test Structure Documentation

## Overview

This project follows a clear separation between **unit tests** and **integration tests**.

## Test Organization

```
tests/
├── integration/          # 🚫 NOT run by `make unit-tests`
│   └── confluence/       # External service integration tests
│       ├── test_credentials.py
│       └── other integration tests...
├── gcs/                  # ✅ Unit tests (run by `make unit-tests`)
├── routes/               # ✅ Unit tests (run by `make unit-tests`)
├── service/              # ✅ Unit tests (run by `make unit-tests`)
├── beans/                # ✅ Unit tests (run by `make unit-tests`)
└── loader/               # ✅ Unit tests (run by `make unit-tests`)
```

## Running Tests

### Unit Tests Only (Fast, No External Dependencies)
```bash
make unit-tests
```
- Runs all tests EXCEPT `tests/integration/`
- Uses `--ignore=tests/integration` flag
- Safe for CI/CD pipelines
- No external credentials required

### Integration Tests (Manual, Requires Setup)
```bash
make integration-tests                    # All integration tests
make integration-tests-confluence         # Confluence-specific only
make test-credentials                     # Credential setup verification
```

### All Tests
```bash
make all-tests
```
- Runs both unit and integration tests
- Requires external services and credentials

## Pytest Markers

Integration tests are marked with:
```python
@pytest.mark.integration
@pytest.mark.confluence  # service-specific
```

## Configuration Files

- `pytest.ini` - Default configuration (unit tests)
- `pytest-all.ini` - Configuration for all tests including integration

## Integration Test Requirements

Integration tests require:
1. **Credentials**: Secret files in `conf/etc/secrets/tests/`
2. **External Services**: Confluence, GCS, etc.
3. **Manual Execution**: Not automated in CI/CD

This ensures that:
- ✅ Unit tests run fast and reliably
- ✅ Integration tests validate real-world scenarios
- ✅ CI/CD pipelines aren't blocked by external dependencies
