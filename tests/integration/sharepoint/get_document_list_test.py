#!/usr/bin/env python3
"""
Test Hybride pour SharePoint Loader - Périmètre sharepoint
=========================================================

Ce script teste le SharePoint loader en mode hybride pour le périmètre 'sharepoint' :
- POST /loader/list/sharepoint (récupération de la liste des documents SharePoint)
- POST /loader/document/sharepoint (téléchargement d'un document SharePoint)

Le mode hybride combine :
✅ Mocks GCS/Auth (simplicité de test)
✅ Vraie connexion SharePoint (données réelles)

FONCTIONNALITÉ TESTÉE:
- Équivalent des tests d'intégration Confluence mais pour SharePoint
- Validation des endpoints HTTP avec de vraies données SharePoint
- Note: mktsearch est configuré pour SharePoint dans mock_gcs_config.py

PRÉREQUIS:
1. Serveur démarré en mode hybride: make start-hybrid
2. Credentials SharePoint configurés dans conf/etc/secrets/local/sharepoint-credentials/secret
3. Configuration SharePoint automatique via mock_gcs_config.py pour périmètre 'mktsearch'

WORKFLOW TESTÉ:
1. POST /loader/list/sharepoint → Récupère la liste des documents SharePoint
2. Sélectionne le premier document de la liste
3. POST /loader/document/sharepoint → Télécharge ce document

UTILISATION:
    python tests/integration/sharepoint/get_document_list_test.py

ENDPOINTS TESTÉS:
- POST http://localhost:8092/loader/list/sharepoint
- POST http://localhost:8092/loader/document/sharepoint
"""

import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path

import requests

# Configuration
BASE_URL = "http://localhost:8092"
PERIMETER_CODE = "sharepoint"  # sharepoint est maintenant configuré pour SharePoint dans mock_gcs_config.py


def setup_environment():
    """Configure l'environnement de test pour SharePoint."""
    print("🔧 Configuration de l'environnement SharePoint...")

    # Vérifier que le serveur est bien en mode hybride
    expected_env = {
        "USE_MOCKS": "true",
        "MOCK_SHAREPOINT": "false"  # Spécifique pour SharePoint
    }

    missing_vars = []
    for var, expected in expected_env.items():
        actual = os.environ.get(var)
        if actual != expected:
            missing_vars.append(f"{var}={expected} (actuel: {actual})")

    if missing_vars:
        print("❌ Variables d'environnement incorrectes pour le mode hybride SharePoint:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n💡 Pour corriger, lancez:")
        print("   export USE_MOCKS=true")
        print("   export MOCK_SHAREPOINT=false")
        print("   make start-hybrid")
        return False

    print("   ✅ Variables d'environnement OK pour SharePoint")
    return True


def check_server_health():
    """Vérifie que le serveur est démarré et répond."""
    print("🔍 Vérification de l'état du serveur...")

    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        if response.status_code == 200:
            print("   ✅ Serveur accessible")
            return True
        else:
            print(f"   ❌ Serveur répond avec le code {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("   ❌ Impossible de se connecter au serveur")
        print("   💡 Assurez-vous que le serveur est démarré : make start-hybrid")
        return False
    except requests.exceptions.Timeout:
        print("   ❌ Timeout lors de la connexion au serveur")
        return False


def test_get_sharepoint_document_list():
    """
    Teste POST /loader/list/mktsearch pour SharePoint
    Récupère la liste des documents SharePoint via l'endpoint HTTP
    """
    print(f"📋 Test 1: POST /loader/list/{PERIMETER_CODE} (SharePoint)")
    print(f"   🎯 Récupération de la liste des documents SharePoint")

    url = f"{BASE_URL}/loader/list/{PERIMETER_CODE}"

    # Payload avec le fichier GCS mocké qui contient la configuration SharePoint
    payload = {
        "get_list_file": f"gs://mock-bucket-{PERIMETER_CODE}/test-data/getlist.json"
    }

    headers = {
        "Content-Type": "application/json"
    }

    print(f"   🌐 URL: {url}")
    print(f"   📦 Payload: {json.dumps(payload, indent=2)}")

    try:
        start_time = time.time()
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        end_time = time.time()

        print(f"   ⏱️  Durée: {end_time - start_time:.2f}s")
        print(f"   📊 Status: {response.status_code}")

        if response.status_code == 200:
            documents = response.json()
            print(f"   ✅ Succès! {len(documents)} documents SharePoint trouvés")

            # Afficher quelques exemples de documents
            if documents:
                print("   📄 Premiers documents SharePoint:")
                for i, doc in enumerate(documents[:3]):  # Limiter à 3 pour l'affichage
                    print(f"      {i + 1}. {doc.get('name', 'Sans nom')}")
                    print(f"         ID: {doc.get('id', 'N/A')}")
                    print(f"         Path: {doc.get('path', 'N/A')}")
                    print(f"         Modifié: {doc.get('modification_time', 'N/A')}")

                if len(documents) > 3:
                    print(f"      ... et {len(documents) - 3} autres documents")

            return documents
        else:
            print(f"   ❌ Erreur HTTP {response.status_code}")
            print(f"   📄 Réponse: {response.text[:500]}")
            return None

    except requests.exceptions.Timeout:
        print("   ❌ Timeout - La requête a pris trop de temps")
        print("   💡 Vérifiez la connectivité SharePoint et les credentials")
        return None
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Erreur de requête: {e}")
        return None


def test_get_sharepoint_document(documents):
    """
    Teste POST /loader/document/mktsearch pour SharePoint
    Télécharge un document SharePoint via l'endpoint HTTP
    """
    if not documents:
        print("   ⚠️  Aucun document SharePoint à télécharger (liste vide)")
        return None

    # Sélectionner le premier document pour le test
    first_doc = documents[0]
    doc_id = first_doc.get('id')
    doc_name = first_doc.get('name')

    print(f"\n📄 Test 2: POST /loader/document/{PERIMETER_CODE} (SharePoint)")
    print("   🎯 Téléchargement d'un document SharePoint")
    print(f"   📋 Document sélectionné: {doc_name}")
    print(f"   🆔 ID: {doc_id}")

    url = f"{BASE_URL}/loader/document/{PERIMETER_CODE}"

    # Créer un fichier getdoc temporaire pour ce test
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    temp_dir = Path("/tmp/kbot_sharepoint_hybrid_test")
    temp_dir.mkdir(exist_ok=True)

    getdoc_file = temp_dir / f"getdoc_sharepoint_{timestamp}.json"

    # Structure du fichier getdoc pour SharePoint
    getdoc_data = {
        "source": {
            "id": 2,
            "code": f"{PERIMETER_CODE}_sharepoint_test",
            "label": f"Test SharePoint {PERIMETER_CODE}",
            "src_type": "sharepoint",
            "configuration": json.dumps({
                "site_url": "https://votre-org.sharepoint.com/sites/votre-site",
                "drive_name": "Documents partagés",
                "folder_path": "/",
                "file_extensions": ["pdf", "docx", "xlsx", "pptx"],
                "include_subfolders": True,
                "max_results": 100
            }),
            "perimeter_code": PERIMETER_CODE,
            "domain_code": "test"
        },
        "document": {
            "id": doc_id,
            "name": doc_name,
            "path": first_doc.get('path'),
            "modification_time": first_doc.get('modification_time')
        }
    }

    # Sauvegarder le fichier getdoc
    with open(getdoc_file, 'w', encoding='utf-8') as f:
        json.dump(getdoc_data, f, indent=2, ensure_ascii=False)

    print(f"   📁 Fichier getdoc SharePoint créé: {getdoc_file}")

    # Payload pour l'endpoint
    payload = {
        "document_get_file": str(getdoc_file)
    }

    headers = {
        "Content-Type": "application/json"
    }

    print(f"   🌐 URL: {url}")
    print(f"   📦 Payload: {json.dumps(payload, indent=2)}")

    try:
        start_time = time.time()
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        end_time = time.time()

        print(f"   ⏱️  Durée: {end_time - start_time:.2f}s")
        print(f"   📊 Status: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("   ✅ Document SharePoint téléchargé avec succès!")

            # Analyser le résultat
            document = result.get('document', {})
            metadata = result.get('metadata', {})

            print("   📄 Document SharePoint:")
            print(f"      Nom: {document.get('name', 'N/A')}")
            print(f"      ID: {document.get('id', 'N/A')}")
            print(f"      Taille métadonnées: {len(json.dumps(metadata))} caractères")

            # Sauvegarder le résultat pour inspection
            result_file = temp_dir / f"sharepoint_document_result_{timestamp}.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)

            print(f"   💾 Résultat SharePoint sauvé: {result_file}")

            return result
        else:
            print(f"   ❌ Erreur HTTP {response.status_code}")
            print(f"   📄 Réponse: {response.text[:500]}")
            return None

    except requests.exceptions.Timeout:
        print("   ❌ Timeout - Le téléchargement SharePoint a pris trop de temps")
        return None
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Erreur de requête SharePoint: {e}")
        return None
    finally:
        # Nettoyer le fichier temporaire
        if getdoc_file.exists():
            getdoc_file.unlink()
            print(f"   🧹 Fichier temporaire supprimé: {getdoc_file}")


def main():
    """Point d'entrée principal du test hybride SharePoint."""
    print("=" * 80)
    print("🧪 TEST HYBRIDE SHAREPOINT LOADER - PÉRIMÈTRE MKTSEARCH")
    print("=" * 80)
    print()
    print("🎯 Objectif: Tester les endpoints POST SharePoint en mode hybride")
    print("   • POST /loader/list/mktsearch (récupération liste SharePoint)")
    print("   • POST /loader/document/mktsearch (téléchargement document SharePoint)")
    print()
    print("🔄 Mode hybride: Mocks GCS/Auth + Vraie connexion SharePoint")
    print()

    # Étape 1: Vérifications préliminaires
    if not setup_environment():
        print("\n❌ Échec de la configuration environnement SharePoint")
        return 1

    if not check_server_health():
        print("\n❌ Échec de la vérification serveur")
        return 1

    print()

    # Étape 2: Test de récupération de la liste des documents SharePoint
    documents = test_get_sharepoint_document_list()
    if documents is None:
        print("\n❌ Échec du test de récupération de la liste SharePoint")
        return 1

    if not documents:
        print("\n⚠️  Liste SharePoint vide - impossible de tester le téléchargement")
        print("💡 Vérifiez la configuration SharePoint (site, drive, dossiers, etc.)")
        return 0

    # Étape 3: Test de téléchargement d'un document SharePoint
    result = test_get_sharepoint_document(documents)
    if result is None:
        print("\n❌ Échec du test de téléchargement SharePoint")
        return 1

    print()
    print("=" * 80)
    print("✅ TOUS LES TESTS HYBRIDES SHAREPOINT ONT RÉUSSI!")
    print("=" * 80)
    print()
    print("📊 Résumé:")
    print(f"   • {len(documents)} documents SharePoint listés")
    print("   • 1 document SharePoint téléchargé avec succès")
    print("   • SharePoint loader validé en mode hybride")
    print()
    print("💡 Prochaines étapes:")
    print("   • Adapter la configuration pour vos sites SharePoint")
    print("   • Tester avec différents types de documents (PDF, DOCX, etc.)")
    print("   • Valider le traitement des métadonnées SharePoint")

    return 0


if __name__ == "__main__":
    sys.exit(main())
