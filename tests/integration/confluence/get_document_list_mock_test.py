#!/usr/bin/env python3
"""
Test Mock pour get_document_list() - Version locale sans réseau
==============================================================

Test simplifié qui démontre la récupération de listes de documents sans nécessiter
d'authentification réseau. Utilise des mocks pour simuler le comportement de l'API Confluence.

ALIGNEMENT AVEC LES AUTRES TESTS:
- Structure identique à get_document_list_test.py (mais avec mocks)
- Génère des données compatibles avec get_document_mock_test.py
- Utilise le même répertoire de sortie: tmp/downloads/
- Format de production exact (source.__dict__ + documents[])

UTILISATION:
    python tests/integration/confluence/get_document_list_mock_test.py

SÉQUENCE DE TESTS RECOMMANDÉE:
1. get_document_list_mock_test.py    (Mock - pas de réseau requis)
2. get_document_mock_test.py         (Mock - utilise les données du test 1)
3. get_document_list_test.py         (E2E - réseau requis pour validation)
"""

import json
import os
import sys
from datetime import datetime, timezone
from pathlib import Path
from unittest.mock import MagicMock, patch

# Add src path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "..", "..", ".."))
src_path = os.path.join(project_root, "src")
sys.path.insert(0, src_path)


def setup_environment():
    """Configure environment variables (same as get_document_list_test.py)."""
    os.environ["ENV"] = "tests"
    os.environ["PATH_TO_SECRET_CONFIG"] = "conf/etc/secrets/tests"
    # Enable debug logging to see what's happening
    import logging
    logging.basicConfig(level=logging.DEBUG)


def create_source_bean():
    """Create SourceBean with VODCASTV/ravenne configuration (identical to get_document_list_test.py)."""
    from kbotloadscheduler.bean.beans import SourceBean

    config = {
        "confluence_url": "https://espace.agir.orange.com/",
        "space_key": "VODCASTV",  # Will be transformed to spaces: ["VODCASTV"]
        "labels": "ravenne, rag",  # Will be transformed to labels: ["ravenne", "rag"]
        "include_attachments": True,  # Activer pour tester les attachments
        "max_results": 100,  # Augmenter pour voir plus de résultats
        "export_format": "markdown"
    }

    return SourceBean(
        id=1,
        code="vodcastv_ravenne_test",
        label="VODCASTV Ravenne Test",
        src_type="confluence",
        configuration=json.dumps(config),
        last_load_time=0,
        load_interval=24,
        domain_code="test",
        perimeter_code="tests"
    )


def create_mock_documents():
    """Create realistic mock documents for testing."""
    from kbotloadscheduler.bean.beans import DocumentBean

    base_time = datetime.now(timezone.utc)

    mock_documents = [
        DocumentBean(
            id="test|vodcastv_ravenne_test|1994899263",
            name="Guide Ravenne - Configuration Serveurs",
            path="https://espace.agir.orange.com/pages/viewpage.action?pageId=1994899263",
            modification_time=base_time.replace(hour=14, minute=30, second=52)
        ),
        DocumentBean(
            id="test|vodcastv_ravenne_test|1994899264",
            name="Ravenne - Architecture Technique",
            path="https://espace.agir.orange.com/pages/viewpage.action?pageId=1994899264",
            modification_time=base_time.replace(hour=15, minute=45, second=12)
        ),
        DocumentBean(
            id="test|vodcastv_ravenne_test|att|1994899265|architecture.png",
            name="architecture.png",
            path="https://espace.agir.orange.com/download/attachments/1994899264/architecture.png",
            modification_time=base_time.replace(hour=16, minute=20, second=35)
        ),
        DocumentBean(
            id="test|vodcastv_ravenne_test|**********",
            name="Ravenne - Procédures de Déploiement",
            path="https://espace.agir.orange.com/pages/viewpage.action?pageId=**********",
            modification_time=base_time.replace(hour=10, minute=15, second=8)
        ),
        DocumentBean(
            id="test|vodcastv_ravenne_test|att|1994899267|deployment_guide.pdf",
            name="deployment_guide.pdf",
            path="https://espace.agir.orange.com/download/attachments/**********/deployment_guide.pdf",
            modification_time=base_time.replace(hour=11, minute=30, second=22)
        )
    ]

    return mock_documents


def setup_test_environment():
    """Setup test environment and import required modules."""
    print("1. Configuration de l'environnement...")
    setup_environment()
    print("   ✅ Variables d'environnement configurées")

    print("2. Import des modules...")
    from dependency_injector import providers
    from kbotloadscheduler.secret.secret_manager import ConfigWithSecret
    from kbotloadscheduler.loader.confluence.confluence_loader import ConfluenceLoader
    print("   ✅ Modules importés")

    return providers, ConfigWithSecret, ConfluenceLoader


def setup_mock_configuration(providers, ConfigWithSecret):
    """Setup mock configuration and credentials."""
    print("3. Configuration des credentials (mockée)...")
    config = providers.Configuration()
    config.env.from_env("ENV", "tests")
    config.path_to_secret_config.from_env("PATH_TO_SECRET_CONFIG", "conf/etc/secrets/tests")

    with patch.object(ConfigWithSecret, '__init__', return_value=None):
        config_with_secret = ConfigWithSecret(config=config)
        print("   ✅ Configuration mockée créée")

    return config_with_secret


def setup_mock_loader(ConfluenceLoader, config_with_secret):
    """Setup mock ConfluenceLoader with test data."""
    print("4. Initialisation du ConfluenceLoader (mocké)...")

    mock_documents = create_mock_documents()

    with patch.object(ConfluenceLoader, '__init__', return_value=None):
        loader = ConfluenceLoader(config_with_secret)
        loader.get_document_list = MagicMock(return_value=mock_documents)

        print("   ✅ ConfluenceLoader mocké initialisé")
        print(f"   📊 {len(mock_documents)} documents mockés préparés")

    return loader


def execute_document_list_retrieval(loader, source):
    """Execute the document list retrieval with mocked loader."""
    print("6. Appel de get_document_list() (mocké)...")
    print("   ⏳ Simulation en cours...")

    documents = loader.get_document_list(source)
    print(f"   ✅ {len(documents)} document(s) simulé(s)")

    return documents


def save_documents_to_file(documents, source):
    """Save documents to JSON file in production format."""
    print("7. Sauvegarde locale (simulation format production)...")

    output_dir = Path(project_root) / "tmp" / "downloads"
    output_dir.mkdir(parents=True, exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = output_dir / f"vodcastv_ravenne_list_mock_{timestamp}.json"

    # Convert documents to serializable format
    serialized_documents = []
    for doc in documents:
        serialized_doc = {
            "id": doc.id,
            "name": doc.name,
            "path": doc.path,
            "modification_time": doc.modification_time.isoformat() if doc.modification_time else None
        }
        serialized_documents.append(serialized_doc)

    sorted_serialized_documents = sorted(serialized_documents, key=lambda d: d["id"])

    dict_list = {
        "source": source.__dict__,
        "documents": sorted_serialized_documents
    }

    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(dict_list, f, indent=2, ensure_ascii=False)
        print(f"   ✅ Format production sauvegardé: {output_file}")
        print(f"   📁 Fichier: {output_file.absolute()}")
        print(f"   📊 Documents: {len(documents)} (triés par ID)")
        print("   🔧 Structure: source.__dict__ + documents[]")
    except Exception as save_error:
        print(f"   ❌ Erreur lors de la sauvegarde: {save_error}")

    return output_file


def analyze_document_results(documents):
    """Analyze and display document results."""
    print("8. Analyse des résultats...")

    if not documents:
        print("   ⚠️  Aucun document trouvé")
        print("   💡 Configuration du mock incorrecte")
        return

    print(f"   📊 Documents simulés: {len(documents)}")

    _check_specific_page(documents)
    _analyze_document_types(documents)
    _show_sample_documents(documents)
    _verify_document_id_format(documents)


def _check_specific_page(documents):
    """Check if specific page 1994899263 is found."""
    print("   🔍 Recherche de la page spécifique 1994899263:")
    page_found = False
    for doc in documents:
        if "1994899263" in doc.id:
            page_found = True
            print(f"      ✅ Page 1994899263 trouvée: {doc.name}")
            print(f"         URL: {doc.path}")
            break

    if not page_found:
        print(f"      ❌ Page 1994899263 NOT trouvée dans les {len(documents)} résultats")
        print("      💡 Vérifiez la configuration du mock")


def _analyze_document_types(documents):
    """Analyze and count document types (pages vs attachments)."""
    print("   🔍 Analyse des types de documents:")
    pages_count = 0
    attachments_count = 0

    for doc in documents:
        if "|att" in doc.id:
            attachments_count += 1
            print(f"      📎 Attachment: {doc.name} (ID: {doc.id})")
        else:
            pages_count += 1
            print(f"      📄 Page: {doc.name} (ID: {doc.id})")

    print(f"   📊 Répartition: {pages_count} pages, {attachments_count} attachments")


def _show_sample_documents(documents):
    """Show first few documents as samples."""
    print("   📝 Premiers documents:")
    for i, doc in enumerate(documents[:3]):
        doc_type = "📎 Attachment" if "|att" in doc.id else "📄 Page"
        print(f"      {i + 1}. {doc_type}: {doc.name}")
        print(f"         ID: {doc.id}")
        print(f"         Modifié: {doc.modification_time}")

    if len(documents) > 3:
        print(f"      ... et {len(documents) - 3} autres documents")


def _verify_document_id_format(documents):
    """Verify document ID format."""
    print("   🔍 Vérification format des IDs:")
    for doc in documents[:2]:
        if '|' in doc.id:
            parts = doc.id.split('|')
            print(f"      {doc.name}: {parts[0]}|{parts[1]}|{parts[2]}")
        else:
            print(f"      {doc.name}: {doc.id}")


def print_test_summary(documents, output_file):
    """Print test success summary."""
    print("9. Récapitulatif du test...")
    print("   ✅ Test mocké réussi!")
    print("   📈 Résultats:")
    print("      - get_document_list() mocké exécuté sans erreur")
    print(f"      - {len(documents)} documents simulés")
    print("      - Configuration VODCASTV/ravenne,rag validée")
    print("      - Format production exact sauvegardé")
    print("   🔧 Compatibilité:")
    print("      - Structure identique à TreatmentFileManager.write_document_list()")
    print("      - Peut être lu par TreatmentFileManager.read_document_list()")
    print("      - Compatible avec get_document_mock_test.py")
    print("      - Prêt pour intégration avec DocumentService.compare_document_list()")

    print(f"   📁 Fichier de résultats conservé: {output_file.name}")
    print(f"   💡 Vous pouvez examiner le fichier: {output_file.absolute()}")
    print("   🔄 Ce fichier peut être utilisé par get_document_mock_test.py")


def handle_test_error(e):
    """Handle test errors with debugging information."""
    print(f"   ❌ Erreur: {str(e)}")
    print(f"   🔧 Type d'erreur: {type(e).__name__}")

    print("\n💡 Solutions possibles:")
    print("   - Vérifier les imports des modules à mocker")
    print("   - Vérifier la configuration des mocks")
    print("   - Vérifier que les données mockées sont cohérentes")

    import traceback
    print("\n🔍 Stack trace:")
    traceback.print_exc()


def test_get_document_list_mock():
    """Test get_document_list() with mocked Confluence client."""
    print("🧪 Test Mock: get_document_list() - Simulation VODCASTV/ravenne")
    print("=" * 65)
    print("💡 Ce test simule get_document_list_test.py sans accès réseau")
    print("� Alignement: même structure et format de données")

    try:
        # Setup test environment and dependencies
        providers, ConfigWithSecret, ConfluenceLoader = setup_test_environment()

        # Setup mock configuration
        config_with_secret = setup_mock_configuration(providers, ConfigWithSecret)

        # Setup mock loader
        loader = setup_mock_loader(ConfluenceLoader, config_with_secret)

        # Create source bean
        print("5. Création du SourceBean...")
        source = create_source_bean()
        print("   ✅ SourceBean créé")
        print("   📋 Espace: VODCASTV (via space_key)")
        print("   🏷️  Labels: ravenne, rag (comma-separated)")
        print("   📄 Max résultats: 100")

        # Execute document list retrieval
        documents = execute_document_list_retrieval(loader, source)

        # Save results to file
        output_file = save_documents_to_file(documents, source)

        # Analyze results
        analyze_document_results(documents)

        # Print success summary
        print_test_summary(documents, output_file)

        return True

    except Exception as e:
        handle_test_error(e)
        return False


if __name__ == "__main__":
    print("🚀 Démarrage du test Mock get_document_list()")
    print(f"📁 Répertoire: {os.getcwd()}")
    print(f"🐍 Python: {sys.version}")
    print("🎭 Mode: MOCK (pas d'accès réseau requis)")

    success = test_get_document_list_mock()

    if success:
        print("\n🎉 Test terminé avec succès!")
        print("💡 Vous pouvez maintenant exécuter get_document_mock_test.py")
        exit(0)
    else:
        print("\n❌ Test échoué!")
        exit(1)
