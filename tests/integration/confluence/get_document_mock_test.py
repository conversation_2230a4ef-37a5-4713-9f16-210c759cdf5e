#!/usr/bin/env python3
"""
Test simple pour get_document() - Version locale sans réseau
============================================================

Test simplifié qui démontre le téléchargement de documents sans nécessiter
d'authentification réseau. Utilise des mocks pour simuler le comportement.

PRÉREQUIS:
- Exécuter d'abord: tests/integration/confluence/get_document_list_test.py
- Cela génère le fichier de données requis dans tmp/downloads/

ALIGNEMENT AVEC get_document_list_test.py:
- Utilise le même répertoire de sortie: tmp/downloads/
- Lit les données au format de production exact
- Structure de données compatible (source.__dict__ + documents[])

UTILISATION:
    python tests/integration/confluence/get_document_mock_test.py
"""

import json
import os
import sys
from datetime import datetime
from pathlib import Path

from kbotloadscheduler.bean.beans import SourceBean, DocumentBean

# Add src path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "..", "..", ".."))
src_path = os.path.join(project_root, "src")
sys.path.insert(0, src_path)


def load_document_list():
    """Load document list from the previously generated JSON file."""
    # Look for the most recent file in the correct tmp/downloads directory
    downloads_dir = Path("tmp/downloads")

    if not downloads_dir.exists():
        raise FileNotFoundError(f"Downloads directory not found: {downloads_dir.absolute()}")

    # Find the most recent vodcastv_ravenne_list file
    json_files = list(downloads_dir.glob("vodcastv_ravenne_list_*.json"))

    if not json_files:
        raise FileNotFoundError(f"No vodcastv_ravenne_list files found in: {downloads_dir.absolute()}")

    # Get the most recent file
    json_file = max(json_files, key=lambda f: f.stat().st_mtime)
    print(f"   📁 Utilisation du fichier: {json_file.name}")

    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    return data["source"], data["documents"]


def create_source_bean(source_data):
    """Create SourceBean from JSON data (aligned with get_document_list_test.py format)."""
    return SourceBean(
        id=source_data["id"],
        code=source_data["code"],
        label=source_data["label"],
        src_type=source_data["src_type"],
        configuration=source_data["configuration"],
        last_load_time=source_data["last_load_time"],
        load_interval=source_data["load_interval"],
        domain_code=source_data["domain_code"],
        perimeter_code=source_data["perimeter_code"],
        force_embedding=source_data.get("force_embedding", False)
    )


def create_document_bean(doc_data):
    """Create DocumentBean from JSON data."""
    modification_time = datetime.fromisoformat(doc_data["modification_time"].replace('Z', '+00:00'))

    return DocumentBean(
        id=doc_data["id"],
        name=doc_data["name"],
        path=doc_data["path"],
        modification_time=modification_time
    )


def create_mock_content(document):
    """Create mock content for a document."""
    if "|att" in document.id:
        # Mock binary content for attachment
        return b"Mock image content for " + document.name.encode('utf-8')
    else:
        # Mock HTML content for page
        return f"""
        <html>
        <head><title>{document.name}</title></head>
        <body>
        <h1>{document.name}</h1>
        <p>This is mock content for the Confluence page.</p>
        <p>Document ID: {document.id}</p>
        <p>Modified: {document.modification_time}</p>
        </body>
        </html>
        """.strip()


def validate_data_format(source_data, documents_data):
    """Validate that loaded data has the required format."""
    print("   🔍 Validation du format de données...")
    required_source_fields = ["id", "code", "label", "src_type", "configuration"]
    required_doc_fields = ["id", "name", "path", "modification_time"]

    for field in required_source_fields:
        if field not in source_data:
            raise ValueError(f"Champ source manquant: {field}")

    if documents_data and required_doc_fields:
        first_doc = documents_data[0]
        for field in required_doc_fields:
            if field not in first_doc:
                raise ValueError(f"Champ document manquant: {field}")

    print("   ✅ Format de données validé (compatible get_document_list_test.py)")


def setup_output_directory():
    """Setup output directory for test files."""
    print("3. Préparation du répertoire de sortie...")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = Path("tmp") / "downloads" / f"get_document_mock_test_{timestamp}"
    output_dir.mkdir(parents=True, exist_ok=True)
    print(f"   ✅ Répertoire: {output_dir}")
    return output_dir, timestamp


def save_document_content(document, content, output_dir):
    """Save document content to file and return metadata."""
    if "|att" in document.id:
        # Binary attachment
        safe_filename = "".join(c for c in document.name if c.isalnum() or c in ".-_")
        local_file_path = output_dir / safe_filename

        with open(local_file_path, 'wb') as f:
            f.write(content)

        file_type = "binary"
    else:
        # HTML page
        safe_filename = "".join(c for c in document.name if c.isalnum() or c in " -_")
        safe_filename = safe_filename.replace(" ", "_") + ".html"
        local_file_path = output_dir / safe_filename

        with open(local_file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        file_type = "html"

    print(f"      ✅ Sauvegardé: {safe_filename}")

    metadata = {
        "document_id": document.id,
        "document_name": document.name,
        "location": str(local_file_path),
        "modificationDate": document.modification_time.isoformat(),
        "file_size": local_file_path.stat().st_size,
        "export_format": file_type
    }

    return metadata, file_type


def create_result_entry(document, metadata, success, error=None):
    """Create a result entry for a document."""
    return {
        "document": {
            "id": document.id,
            "name": document.name,
            "path": document.path,
            "type": "attachment" if "|att" in document.id else "page"
        },
        "metadata": metadata,
        "success": success,
        "error": error
    }


def process_documents(document_beans, output_dir):
    """Process all documents and return results and statistics."""
    print("4. Simulation du téléchargement des documents...")
    results = []
    download_stats = {"pages": 0, "attachments": 0, "errors": 0}

    for i, document in enumerate(document_beans, 1):
        doc_type = "📎 Attachment" if "|att" in document.id else "📄 Page"
        print(f"   📥 [{i}/{len(document_beans)}] {doc_type}: {document.name}")

        try:
            # Simulate download
            content = create_mock_content(document)
            metadata, file_type = save_document_content(document, content, output_dir)

            if file_type == "binary":
                download_stats["attachments"] += 1
            else:
                download_stats["pages"] += 1

            result = create_result_entry(document, metadata, True)
            results.append(result)

        except Exception as e:
            download_stats["errors"] += 1
            print(f"      ❌ Erreur: {str(e)}")

            result = create_result_entry(document, None, False, str(e))
            results.append(result)

    return results, download_stats


def save_test_summary(source_data, timestamp, download_stats, results, output_dir):
    """Save test summary to JSON file."""
    print("5. Sauvegarde du rapport...")
    summary = {
        "test_info": {
            "timestamp": timestamp,
            "test_name": "get_document_mock_test",
            "source_code": source_data["code"],
            "source_label": source_data["label"]
        },
        "statistics": download_stats,
        "results": results
    }

    summary_file = output_dir / "download_summary.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)

    print(f"   ✅ Rapport sauvegardé: {summary_file}")


def display_test_results(download_stats, results, output_dir):
    """Display test results and statistics."""
    print("6. Résultats du test...")
    print("   📊 Statistiques:")
    print(f"      - Pages simulées: {download_stats['pages']}")
    print(f"      - Attachments simulés: {download_stats['attachments']}")
    print(f"      - Erreurs: {download_stats['errors']}")
    print(f"      - Total: {len(results)} documents traités")

    # List downloaded files
    downloaded_files = list(output_dir.glob("*"))
    print(f"   📁 {len(downloaded_files)} fichier(s) créés:")
    for file in downloaded_files:
        if file.is_file():
            size = file.stat().st_size
            print(f"      - {file.name} ({size} bytes)")

    success = download_stats["errors"] == 0
    if success:
        print("   🎉 Test simulé réussi!")
    else:
        print(f"   ⚠️  {download_stats['errors']} erreur(s)")

    print(f"   📁 Fichiers conservés dans: {output_dir}")
    return success


def test_get_document_mock():
    """Test get_document() with mocked Confluence client."""
    print("🧪 Test Mock: get_document() - Téléchargement simulé VODCASTV/ravenne")
    print("=" * 70)
    print("💡 Ce test utilise les données générées par get_document_list_test.py")
    print("🔗 Alignement: même format de données et répertoires")

    try:
        # Load document list
        print("1. Chargement de la liste des documents...")
        source_data, documents_data = load_document_list()
        print(f"   ✅ {len(documents_data)} documents chargés depuis le JSON")

        # Validate data format
        validate_data_format(source_data, documents_data)

        # Create beans
        print("2. Création des beans...")
        create_source_bean(source_data)
        document_beans = [create_document_bean(doc_data) for doc_data in documents_data]
        print(f"   ✅ {len(document_beans)} documents à traiter")

        # Setup output directory
        output_dir, timestamp = setup_output_directory()

        # Process documents
        results, download_stats = process_documents(document_beans, output_dir)

        # Save summary
        save_test_summary(source_data, timestamp, download_stats, results, output_dir)

        # Display results
        return display_test_results(download_stats, results, output_dir)

    except Exception as e:
        print(f"   ❌ Erreur: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 Démarrage du test Mock get_document()")
    print(f"📁 Répertoire: {os.getcwd()}")

    success = test_get_document_mock()

    if success:
        print("\n🎉 Test terminé avec succès!")
        exit(0)
    else:
        print("\n❌ Test échoué!")
        exit(1)
