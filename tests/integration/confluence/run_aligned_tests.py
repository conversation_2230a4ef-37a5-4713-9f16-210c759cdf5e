#!/usr/bin/env python3
"""
Script pour exécuter les tests alignés en séquence
==================================================

Exécute les trois tests Confluence alignés pour démontrer leur compatibilité.

SÉQUENCES DISPONIBLES:
Mode MOCK (recommandé, pas de réseau requis):
1. get_document_list_mock_test.py - Simule la liste de documents (sans réseau)
2. get_document_mock_test.py - Simule le téléchargement (sans réseau)

Mode E2E (complet, réseau requis):
1. get_document_list_test.py - Génère la liste de documents (avec réseau)
2. get_document_mock_test.py - Simule le téléchargement (sans réseau)
3. get_document_list_mock_test.py - Validation mock (optionnel)

UTILISATION:
    python tests/integration/confluence/run_aligned_tests.py [--mode=mock|e2e]
"""

import os
import subprocess
import sys
from pathlib import Path


def run_test_script(script_name):
    """Run a test script and return success status."""
    print(f"\n{'=' * 60}")
    print(f"🚀 Exécution de: {script_name}")
    print(f"{'=' * 60}")

    script_path = Path(__file__).parent / script_name

    try:
        result = subprocess.run([
            sys.executable, str(script_path)
        ], capture_output=False, text=True)

        success = result.returncode == 0
        if success:
            print(f"✅ {script_name} terminé avec succès")
        else:
            print(f"❌ {script_name} échoué (code: {result.returncode})")

        return success

    except Exception as e:
        print(f"❌ Erreur lors de l'exécution de {script_name}: {e}")
        return False


def parse_mode_argument():
    """Parse and validate the mode argument from command line."""
    mode = "mock"  # Default to mock mode
    if len(sys.argv) > 1:
        arg = sys.argv[1]
        if arg.startswith("--mode="):
            mode = arg.split("=")[1].lower()
        elif arg in ["mock", "e2e"]:
            mode = arg.lower()

    if mode not in ["mock", "e2e"]:
        print(f"❌ Mode invalide: {mode}")
        print("💡 Modes supportés: mock, e2e")
        return None

    return mode


def get_test_sequence(mode):
    """Get the test sequence for the specified mode."""
    if mode == "mock":
        print("📋 Séquence MOCK (pas de réseau requis):")
        print("   1. get_document_list_mock_test.py (Simulation liste)")
        print("   2. get_document_mock_test.py (Simulation téléchargement)")
        return [
            "get_document_list_mock_test.py",
            "get_document_mock_test.py"
        ]
    elif mode == "e2e":
        print("📋 Séquence E2E (réseau requis):")
        print("   1. get_document_list_test.py (E2E liste)")
        print("   2. get_document_mock_test.py (Mock téléchargement)")
        print("   3. get_document_list_mock_test.py (Validation mock)")
        return [
            "get_document_list_test.py",
            "get_document_mock_test.py",
            "get_document_list_mock_test.py"
        ]


def run_test_sequence(tests, mode):
    """Run the test sequence and return results."""
    results = {}
    overall_success = True

    for i, test in enumerate(tests, 1):
        print(f"\n🔄 Étape {i}/{len(tests)}")
        success = run_test_script(test)
        results[test] = success
        if not success:
            overall_success = False
            if mode == "e2e" and i == 1:
                print("⚠️  Échec du test E2E, les tests suivants peuvent échouer")
            elif mode == "mock" and i == 1:
                print("⚠️  Échec du test mock, le test suivant peut échouer")

    return results, overall_success


def print_summary(results, overall_success, mode):
    """Print the test results summary."""
    print(f"\n{'=' * 70}")
    print(f"📊 RÉSUMÉ DES TESTS ALIGNÉS - MODE {mode.upper()}")
    print(f"{'=' * 70}")

    for i, (test, success) in enumerate(results.items(), 1):
        status = "✅ SUCCÈS" if success else "❌ ÉCHEC"
        print(f"   {i}. {test:<40} {status}")

    print(f"\n🎯 Résultat global: {'✅ TOUS LES TESTS RÉUSSIS' if overall_success else '❌ AU MOINS UN TEST ÉCHOUÉ'}")

    if overall_success:
        print(f"\n💡 Les tests {mode.upper()} sont alignés et fonctionnels:")
        print("   - Même répertoire de sortie (tmp/downloads/)")
        print("   - Format de données compatible")
        print("   - Structures de données identiques")
        if mode == "mock":
            print("   - Aucun réseau requis")
            print("   - Données simulées réalistes")
        else:
            print("   - Validation E2E complète")
            print("   - Les tests mock peuvent utiliser les données E2E")


def main():
    """Run aligned tests in sequence."""
    print("🧪 Tests Alignés - Confluence Loader")
    print("=" * 40)

    # Parse and validate mode
    mode = parse_mode_argument()
    if mode is None:
        return False

    print(f"🎭 Mode sélectionné: {mode.upper()}")

    # Get test sequence
    tests = get_test_sequence(mode)

    print("🔗 Alignement: même format de données et répertoires")
    # Change to the correct directory
    os.chdir(Path(__file__).parent.parent.parent.parent)
    print(f"\n📁 Répertoire de travail: {os.getcwd()}")

    # Run tests
    results, overall_success = run_test_sequence(tests, mode)

    # Print summary
    print_summary(results, overall_success, mode)

    return overall_success


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
