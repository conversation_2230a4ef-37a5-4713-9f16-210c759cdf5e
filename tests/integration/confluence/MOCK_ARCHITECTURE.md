# Mock Architecture Reference - Confluence Tests

## 🎭 Mock Components Overview

This document provides technical details for the mock architecture used in Confluence integration tests.

## Core Mock Classes

### FullMockConfluenceLoader
```python
class FullMockConfluenceLoader:
    """Complete mock replacement for ConfluenceLoader - no network calls"""

    def get_document(self, source, document, output_path):
        """Simulates document download with realistic metadata generation"""
```

**Key Features**:
- Generates realistic metadata for pages and attachments
- Determines appropriate file extensions by document type
- Creates production-compatible file structures
- No external API calls required

**Metadata Generated**:
```json
{
  "document_id": "test|vodcastv_ravenne_test|1994899263",
  "document_name": "Page Title",
  "source_path": "https://espace.agir.orange.com/pages/viewpage.action?pageId=1994899263",
  "location": "gs://kbot-test-bucket-tests/.../document.md",
  "creationDate": "20250618140245",
  "modificationDate": "20250618140245",
  "domain": "test",
  "source": "vodcastv_ravenne_test",
  "source_type": "confluence",
  "document_type": "page|attachment",
  "file_size": 1234,
  "export_format": "markdown|binary"
}
```

### MockTreatmentFileManager
```python
class MockTreatmentFileManager:
    """Mock for TreatmentFileManager - simulates GCS without actual cloud access"""

    def build_directory_name(self, perimeter, load_date, domain_code, source_code, docs_type):
        """Generates valid GCS URIs without real cloud access"""
```

**Key Features**:
- Parses getdoc.json filenames correctly
- Generates valid GCS URIs for testing
- Saves metadata files locally
- Maintains production file lifecycle patterns

## Mock Data Patterns

### Document Types
- **Pages**: `.md` files with HTML content and page metadata
- **Attachments**: Binary files (`.png`, `.pdf`, etc.) with file metadata

### File Extensions by Type
```python
type_extensions = {
    "image/png": ".png",
    "image/jpeg": ".jpg",
    "application/pdf": ".pdf",
    "text/plain": ".txt",
    "application/vnd.ms-excel": ".xls"
}
```

### Generated Content
- **Page Content**: Realistic HTML with document structure
- **Binary Content**: Appropriate file sizes and mock binary data
- **Metadata**: Complete production-format metadata files

## Integration Points

### LoaderService Integration
The mocks integrate seamlessly with `LoaderService.get_document()`:

```python
# Real production code unchanged
loader_manager = LoaderManager(confluence=mock_confluence_loader)
loader_service = LoaderService(loader_manager=loader_manager,
                              treatment_file_manager=mock_file_manager)

# Works identically to production
loader_service.get_document(source, document, output_path)
```

### Test Validation
- **0 Network Calls**: All external dependencies mocked
- **Production Compatibility**: Output format identical to real API
- **Performance**: Fast execution for CI/CD pipelines
- **Deterministic**: Consistent results across test runs

## Usage in Tests

### `loader_service_get_document_test.py`
```python
@patch('src.services.LoaderManager')
@patch('src.utils.TreatmentFileManager')
def test_loader_service_workflow(mock_treatment_manager, mock_loader_manager):
    # Setup mocks
    mock_loader_manager.return_value.get_loader.return_value = FullMockConfluenceLoader()
    mock_treatment_manager.return_value = MockTreatmentFileManager()

    # Test runs without external dependencies
    result = loader_service.get_document(source, document, output_path)
```

### Production Workflow Testing
The mock architecture allows testing of:
- ✅ Complete LoaderService workflow (Step 3)
- ✅ getdoc.json → .metadata.json transformation
- ✅ File lifecycle management
- ✅ Error handling and validation
- ✅ Production data format compatibility

## Benefits

### Development
- **Fast Iteration**: No network delays
- **No Authentication**: No secret management required
- **Deterministic**: Consistent test outcomes
- **Offline Development**: Works without internet

### CI/CD
- **Reliable**: No external service dependencies
- **Fast**: Quick execution in automated pipelines
- **Secure**: No secrets required in build environment
- **Scalable**: Parallel execution without API rate limits

## Mock vs E2E Strategy

| Aspect | Mock Tests | E2E Tests |
|--------|------------|-----------|
| **Speed** | Fast (< 1s) | Slow (10-30s) |
| **Dependencies** | None | Network + Auth |
| **Reliability** | 100% | Depends on external services |
| **Data** | Controlled | Real but variable |
| **Use Case** | Development + CI | Pre-deployment validation |

---

**Implementation Files**:
- `loader_service_get_document_test.py` - Primary mock implementation
- `get_document_list_mock_test.py` - Document list mock
- `get_document_mock_test.py` - Document download mock

**Last Updated**: June 18, 2025
