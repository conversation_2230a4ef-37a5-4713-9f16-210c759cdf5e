# Confluence Integration Tests - Documentation Index

## 📚 Documentation Navigation

### 🎯 Start Here
- **[README_CONSOLIDATED.md](./README_CONSOLIDATED.md)** - Complete guide and quick start
- **[MOCK_ARCHITECTURE.md](./MOCK_ARCHITECTURE.md)** - Technical mock implementation details

### 🧪 Test Files (Current Architecture)
```
tests/integration/confluence/
├── 🎭 Mock Tests
│   ├── get_document_list_mock_test.py     # Generate mock document lists
│   └── get_document_mock_test.py          # Simulate document downloads
├── 🌐 E2E Tests
│   └── get_document_list_test.py          # Real API document lists
├── 🔧 Production Tests
│   ├── loader_service_get_document_test.py # LoaderService workflow (Step 3)
│   └── production_workflow_test.py        # SharePoint compatibility
├── 🎬 Orchestration
│   └── run_aligned_tests.py               # Test suite runner
└── 📋 Legacy (Evaluate for Removal)
    └── get_document_test.py               # Standalone E2E document downloads
```

## 🚀 Quick Commands

### Daily Development
```bash
# Mock tests (fast, no network)
python tests/integration/confluence/run_aligned_tests.py --mode=mock
```

### Pre-deployment Validation
```bash
# E2E tests (requires network + auth)
python tests/integration/confluence/run_aligned_tests.py --mode=e2e

# Production workflow tests
python tests/integration/confluence/loader_service_get_document_test.py
```

## 📋 Documentation Status

### ✅ Current (Keep)
- `README_CONSOLIDATED.md` - Master documentation (NEW)
- `MOCK_ARCHITECTURE.md` - Mock technical reference (NEW)
- `README.md` - Simple navigation pointer (NEW)
- `INDEX.md` - Documentation index (NEW)

### ⚠️ Legacy Test Files (Consider Removal)
- `get_document_test.py` - Standalone E2E document downloads (not part of aligned architecture)

## 🏗️ Architecture Summary

The test suite uses an **Aligned Architecture** with three key principles:

1. **🎭 Mock-First Development**: Fast, network-free testing for daily development
2. **🌐 E2E Validation**: Real API testing for deployment confidence
3. **🔧 Production Compatibility**: Tests validate real production workflows

## 📞 Getting Help

1. **Quick Start**: Read `README_CONSOLIDATED.md`
2. **Mock Details**: Check `MOCK_ARCHITECTURE.md`
3. **Test Issues**: Run individual test files with debug logging
4. **Production Issues**: Check `loader_service_get_document_test.py` results

---

**Last Updated**: June 18, 2025
**Documentation Version**: 2.0 (Consolidated)
**Test Architecture**: Aligned Mock + E2E
