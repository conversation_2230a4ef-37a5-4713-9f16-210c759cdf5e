#!/usr/bin/env python3
"""
Test E2E pour get_document() - Téléchargement des documents VODCASTV/ravenne
===========================================================================

Test end-to-end pour valider le loader Confluence avec téléchargement des documents:
- Lit la liste des documents depuis tmp/downloads/vodcastv_ravenne_list_*.json
- Teste get_document() pour chaque document (pages et attachments)
- Sauvegarde les résultats en local dans tmp/downloads/

FONCTIONNALITÉS TESTÉES:
- get_document() pour les pages Confluence (export markdown)
- get_document() pour les attachments (images PNG)
- Gestion des métadonnées de document
- Format de sortie compatible avec la production

STRUCTURE DES DOCUMENTS TESTÉS:
- 1 page: "Etude - Création des profils génétiques OTT" (ID: 1994899263)
- 3 attachments: images PNG (IDs: att2005951698, att2006357553, att2021218478)

PRÉREQUIS:
- PAT token dans: conf/etc/secrets/tests/confluence-credentials/secret
- Accès réseau à https://espace.agir.orange.com
- Fichier tmp/downloads/vodcastv_ravenne_list_*.json existant

UTILISATION:
    python tests/integration/confluence/get_document_test.py
"""

import json
import os
import sys
from datetime import datetime
from pathlib import Path

# Import required modules at the top to avoid hanging during test execution
from dependency_injector import providers

from kbotloadscheduler.bean.beans import SourceBean, DocumentBean
from kbotloadscheduler.loader.confluence.confluence_loader import ConfluenceLoader
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret

# Add src path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "..", "..", ".."))
src_path = os.path.join(project_root, "src")
sys.path.insert(0, src_path)


def setup_environment():
    """Configure environment variables."""
    os.environ["ENV"] = "tests"
    os.environ["PATH_TO_SECRET_CONFIG"] = "conf/etc/secrets/tests"
    # Disable debug logging to avoid hangs
    import logging
    logging.basicConfig(level=logging.INFO)


def load_document_list():
    """Load document list from the most recent generated JSON file."""
    downloads_dir = Path(project_root) / "tmp" / "downloads"
    pattern = "vodcastv_ravenne_list_*.json"
    json_files = list(downloads_dir.glob(pattern))

    if not json_files:
        raise FileNotFoundError(f"No document list files found matching pattern: {pattern}")

    # Get the most recent file
    json_file = max(json_files, key=lambda p: p.stat().st_mtime)
    print(f"   📁 Using most recent file: {json_file.name}")

    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    return data["source"], data["documents"]


def create_source_bean(source_data):
    """Create SourceBean from JSON data."""
    return SourceBean(
        id=source_data["id"],
        code=source_data["code"],
        label=source_data["label"],
        src_type=source_data["src_type"],
        configuration=source_data["configuration"],
        last_load_time=source_data["last_load_time"],
        load_interval=source_data["load_interval"],
        domain_code=source_data["domain_code"],
        perimeter_code=source_data["perimeter_code"],
        force_embedding=source_data.get("force_embedding", False)
    )


def create_document_bean(doc_data):
    """Create DocumentBean from JSON data."""
    # Parse ISO format datetime
    modification_time = datetime.fromisoformat(doc_data["modification_time"].replace('Z', '+00:00'))

    return DocumentBean(
        id=doc_data["id"],
        name=doc_data["name"],
        path=doc_data["path"],
        modification_time=modification_time
    )


def setup_test_environment():
    """Setup environment and load initial data."""
    print("1. Configuration de l'environnement...")
    setup_environment()
    print("   ✅ Variables d'environnement configurées")

    print("2. Chargement de la liste des documents...")
    source_data, documents_data = load_document_list()
    print(f"   ✅ {len(documents_data)} documents chargés depuis le JSON")
    print(f"   📁 Source: {source_data['code']} ({source_data['label']})")

    return source_data, documents_data


def setup_confluence_loader():
    """Setup configuration and initialize ConfluenceLoader."""
    print("3. Configuration des credentials...")
    config = providers.Configuration()
    config.env.from_env("ENV", "tests")
    config.path_to_secret_config.from_env("PATH_TO_SECRET_CONFIG", "conf/etc/secrets/tests")

    config_with_secret = ConfigWithSecret(config=config)
    print("   ✅ Configuration créée")

    print("4. Initialisation du ConfluenceLoader...")
    loader = ConfluenceLoader(config_with_secret)
    print("   ✅ ConfluenceLoader initialisé")

    return loader


def create_beans_and_output_dir(source_data, documents_data):
    """Create beans and setup output directory."""
    print("5. Création des beans...")
    source_bean = create_source_bean(source_data)
    document_beans = [create_document_bean(doc_data) for doc_data in documents_data]
    print("   ✅ Beans créés")
    print(f"   📊 {len(document_beans)} documents à télécharger")

    print("6. Préparation du répertoire de sortie...")
    output_base_dir = Path(project_root) / "tmp" / "downloads"
    output_base_dir.mkdir(parents=True, exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = output_base_dir / f"get_document_test_{timestamp}"
    output_dir.mkdir(exist_ok=True)
    print(f"   ✅ Répertoire de sortie: {output_dir}")

    return source_bean, document_beans, output_dir, timestamp


def download_attachment(confluence_client, document, output_dir):
    """Download a Confluence attachment."""
    print("      📎 Téléchargement de l'attachment...")

    import re
    page_id_match = re.search(r'/download/attachments/(\d+)/', document.path)
    if not page_id_match:
        raise Exception(f"Cannot extract page ID from attachment path: {document.path}")

    page_id = page_id_match.group(1)
    filename = document.name

    print(f"      🔍 Page ID: {page_id}, Filename: {filename}")

    result = confluence_client.confluence.download_attachments_from_page(
        page_id=page_id,
        filename=filename,
        to_memory=True
    )

    if not result or filename not in result:
        raise Exception(f"Attachment {filename} not found in download result")

    attachment_content = result[filename].getvalue()
    print(f"      📊 Downloaded {len(attachment_content)} bytes")

    file_name = document.name
    safe_filename = "".join(c for c in file_name if c.isalnum() or c in ".-_")
    local_file_path = output_dir / safe_filename

    with open(local_file_path, 'wb') as f:
        f.write(attachment_content)

    print(f"      ✅ Attachment sauvegardé: {safe_filename}")

    return {
        "document_id": document.id,
        "document_name": document.name,
        "location": str(local_file_path),
        "modificationDate": document.modification_time.isoformat(),
        "file_size": len(attachment_content),
        "export_format": "binary"
    }


def download_page(confluence_client, document, output_dir, config, item_id):
    """Download a Confluence page."""
    print("      📄 Téléchargement de la page...")

    page_content = confluence_client.get_page_content(item_id)

    if config.content_export_format == "markdown":
        from markdownify import markdownify
        content = markdownify(page_content["body"]["storage"]["value"])
        file_extension = ".md"
    else:
        content = page_content["body"]["storage"]["value"]
        file_extension = ".html"

    safe_filename = "".join(c for c in document.name if c.isalnum() or c in " -_")
    safe_filename = safe_filename.replace(" ", "_") + file_extension
    local_file_path = output_dir / safe_filename

    with open(local_file_path, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"      ✅ Page sauvegardée: {safe_filename}")

    return {
        "document_id": document.id,
        "document_name": document.name,
        "location": str(local_file_path),
        "modificationDate": document.modification_time.isoformat(),
        "title": page_content.get("title", document.name),
        "export_format": config.content_export_format
    }


def download_single_document(loader, source_bean, document, output_dir, download_stats):
    """Download a single document (page, attachment, or Draw.io diagram)."""
    # Déterminer le type de document
    if "|att" in document.id:
        doc_type = "📎 Attachment"
        download_stats["attachments"] += 1
    elif "_macro_" in document.id or "_att_" in document.id or "_embedded_" in document.id:
        doc_type = "🎨 Draw.io Diagram"
        download_stats.setdefault("drawio_diagrams", 0)
        download_stats["drawio_diagrams"] += 1
    else:
        doc_type = "📄 Page"
        download_stats.setdefault("pages", 0)
        download_stats["pages"] += 1

    print(f"      {doc_type}: {document.name}")
    print(f"      ID: {document.id}")

    from kbotloadscheduler.loader.confluence.utils.document_id_utils import DocumentIdFormatter
    parsed_id = DocumentIdFormatter.parse_document_id(document.id)
    item_id = parsed_id["confluence_id"]

    from kbotloadscheduler.loader.confluence.confluence_loader import ConfluenceConfig
    config = ConfluenceConfig.from_source(source_bean)
    confluence_client = loader.get_confluence_client(source_bean, config)

    if "|att" in document.id:
        metadata = download_attachment(confluence_client, document, output_dir)
    else:
        metadata = download_page(confluence_client, document, output_dir, config, item_id)

    return metadata


def create_result_record(document, metadata, success, error=None):
    """Create a result record for a document download."""
    return {
        "document": {
            "id": document.id,
            "name": document.name,
            "path": document.path,
            "type": "attachment" if "|att" in document.id else "page"
        },
        "metadata": metadata,
        "success": success,
        "error": error
    }


def print_metadata(metadata):
    """Print metadata information."""
    if metadata:
        print("      📋 Métadonnées:")
        print(f"         - document_id: {metadata.get('document_id', 'N/A')}")
        print(f"         - location: {metadata.get('location', 'N/A')}")
        print(f"         - modificationDate: {metadata.get('modificationDate', 'N/A')}")


def download_all_documents(loader, source_bean, document_beans, output_dir):
    """Download all documents and return results."""
    print("7. Téléchargement des documents...")
    results = []
    download_stats = {"pages": 0, "attachments": 0, "drawio_diagrams": 0, "errors": 0}

    for i, document in enumerate(document_beans, 1):
        print(f"   📥 [{i}/{len(document_beans)}]", end=" ")

        try:
            metadata = download_single_document(loader, source_bean, document, output_dir, download_stats)
            result = create_result_record(document, metadata, True)
            results.append(result)
            print_metadata(metadata)

        except Exception as e:
            download_stats["errors"] += 1
            print(f"      ❌ Erreur: {str(e)}")
            result = create_result_record(document, None, False, str(e))
            results.append(result)

    return results, download_stats


def save_results_summary(output_dir, timestamp, source_data, download_stats, results):
    """Save the test results summary to a JSON file."""
    print("9. Sauvegarde du rapport de téléchargement...")

    summary = {
        "test_info": {
            "timestamp": timestamp,
            "test_name": "get_document_test",
            "source_code": source_data["code"],
            "source_label": source_data["label"]
        },
        "statistics": download_stats,
        "results": results
    }

    summary_file = output_dir / "download_summary.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)

    print(f"   ✅ Rapport sauvegardé: {summary_file}")
    print(f"   📁 Répertoire complet: {output_dir.absolute()}")


def analyze_and_report_results(results, download_stats, output_dir):
    """Analyze and report the download results."""
    print("10. Analyse des résultats...")
    print("   📊 Statistiques de téléchargement:")
    print(f"      - Pages téléchargées: {download_stats['pages']}")
    print(f"      - Attachments téléchargés: {download_stats['attachments']}")
    print(f"      - Diagrammes Draw.io téléchargés: {download_stats['drawio_diagrams']}")
    print(f"      - Erreurs: {download_stats['errors']}")
    print(f"      - Total: {len(results)} documents traités")

    successful_downloads = [r for r in results if r["success"]]
    failed_downloads = [r for r in results if not r["success"]]

    if failed_downloads:
        print(f"   ⚠️  {len(failed_downloads)} échec(s) de téléchargement:")
        for failure in failed_downloads:
            print(f"      - {failure['document']['name']}: {failure['error']}")

    if successful_downloads:
        print(f"   ✅ {len(successful_downloads)} téléchargement(s) réussi(s)")
        print("   📝 Premiers téléchargements réussis:")
        for i, success in enumerate(successful_downloads[:3]):
            doc = success['document']
            meta = success['metadata']
            print(f"      {i + 1}. {doc['type'].title()}: {doc['name']}")
            print(f"         Location: {meta.get('location', 'N/A') if meta else 'N/A'}")

    print("11. Fichiers téléchargés...")
    downloaded_files = list(output_dir.glob("*"))
    if downloaded_files:
        print(f"   📁 {len(downloaded_files)} fichier(s) dans {output_dir}:")
        for file in downloaded_files:
            if file.is_file():
                size = file.stat().st_size
                print(f"      - {file.name} ({size} bytes)")
    else:
        print(f"   ⚠️  Aucun fichier trouvé dans {output_dir}")

    print("12. Récapitulatif du test...")
    success_rate = len(successful_downloads) / len(results) * 100 if results else 0

    print("   📈 Résultats:")
    print(f"      - get_document() testé sur {len(results)} documents")
    print(f"      - Taux de réussite: {success_rate:.1f}%")
    print(f"      - Pages téléchargées: {download_stats['pages']}")
    print(f"      - Attachments téléchargés: {download_stats['attachments']}")

    if download_stats["errors"] == 0:
        print("   🎉 Tous les téléchargements ont réussi!")
    else:
        print(f"   ⚠️  {download_stats['errors']} erreur(s) de téléchargement")

    print(f"   📁 Résultats conservés dans: {output_dir}")
    print(f"   💡 Examinez les fichiers: {output_dir.absolute()}")

    return download_stats["errors"] == 0


def handle_test_error(e):
    """Handle and report test errors with troubleshooting suggestions."""
    print(f"   ❌ Erreur générale: {str(e)}")
    print(f"   🔧 Type d'erreur: {type(e).__name__}")

    print("\n💡 Solutions possibles:")
    if "FileNotFoundError" in str(type(e)):
        print("   - Générer d'abord la liste avec: python tests/integration/confluence/get_document_list_test.py")
        print("   - Vérifier que le fichier JSON existe dans tmp/downloads/")
    elif "authentication" in str(e).lower() or "401" in str(e):
        print("   - Vérifier le PAT token dans conf/etc/secrets/tests/confluence-credentials/secret")
        print("   - Vérifier que le token n'a pas expiré")
    elif "connection" in str(e).lower() or "network" in str(e).lower():
        print("   - Vérifier la connectivité réseau vers https://espace.agir.orange.com")
        print("   - Vérifier si un proxy est nécessaire")
    else:
        print("   - Vérifier les logs pour plus de détails")
        print("   - Essayer avec une configuration plus simple")

    import traceback
    print("\n🔍 Stack trace:")
    traceback.print_exc()


def test_get_document():
    """Test get_document() for all documents from the list."""
    print("🧪 Test E2E: get_document() - Téléchargement VODCASTV/ravenne")
    print("=" * 70)

    try:
        source_data, documents_data = setup_test_environment()
        loader = setup_confluence_loader()
        source_bean, document_beans, output_dir, timestamp = create_beans_and_output_dir(source_data, documents_data)

        results, download_stats = download_all_documents(loader, source_bean, document_beans, output_dir)
        save_results_summary(output_dir, timestamp, source_data, download_stats, results)

        return analyze_and_report_results(results, download_stats, output_dir)

    except Exception as e:
        handle_test_error(e)
        return False


if __name__ == "__main__":
    print("🚀 Démarrage du test E2E get_document()")
    print(f"📁 Répertoire: {os.getcwd()}")
    print(f"🐍 Python: {sys.version}")

    success = test_get_document()

    if success:
        print("\n🎉 Test terminé avec succès!")
        exit(0)
    else:
        print("\n❌ Test échoué!")
        exit(1)
