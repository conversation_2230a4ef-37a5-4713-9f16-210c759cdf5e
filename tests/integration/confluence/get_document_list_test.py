#!/usr/bin/env python3
"""
Test E2E simple pour get_document_list() - VODCASTV/ravenne
=========================================================

Test end-to-end pour valider le loader Confluence avec des critères spécifiques:
- Espace: VODCASTV
- Labels: "ravenne"

SIMULATION FORMAT PRODUCTION:
Le script sauvegarde le résultat dans le format EXACT utilisé par
TreatmentFileManager.write_document_list() en production:

```json
{
  "source": {
    "id": 1,
    "code": "vodcastv_ravenne_test",
    "label": "VODCASTV Ravenne Test",
    "src_type": "confluence",
    "configuration": "{\"spaces\":\"VODCASTV\",\"labels\":\"ravenne, rag\"}",
    "last_load_time": 0,
    "load_interval": 24,
    "domain_code": "test",
    "perimeter_code": "tests"
  },
  "documents": [
    {
      "id": "test|vodcastv_ravenne_test|page123",
      "name": "Titre de la page",
      "path": "https://espace.agir.orange.com/...",
      "modification_time": "2024-06-19T14:30:52Z"
    }
  ]
}
```

Cette structure est identique à celle utilisée par:
- LoaderService.get_document_list()
- TreatmentFileManager.write_document_list()
- DocumentService.compare_document_list()

PRÉREQUIS:
- PAT token dans: conf/etc/secrets/tests/confluence-credentials/secret
- Accès réseau à https://espace.agir.orange.com
- Pages avec label "ravenne" et "rag" dans l'espace VODCASTV

UTILISATION:
    python tests/integration/confluence/get_document_list_test.py
"""

import json
import os
import sys
from pathlib import Path

# Add src path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "..", "..", ".."))
src_path = os.path.join(project_root, "src")
sys.path.insert(0, src_path)


def setup_environment():
    """Configure environment variables."""
    os.environ["ENV"] = "tests"
    os.environ["PATH_TO_SECRET_CONFIG"] = "conf/etc/secrets/tests"
    # Also set project-level path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.abspath(os.path.join(current_dir, "..", "..", ".."))
    full_secret_path = os.path.join(project_root, "conf", "etc", "secrets", "tests")
    os.environ["PATH_TO_SECRET_CONFIG"] = full_secret_path

    print(f"   🔧 ENV: {os.environ.get('ENV')}")
    print(f"   🔧 PATH_TO_SECRET_CONFIG: {os.environ.get('PATH_TO_SECRET_CONFIG')}")

    # Enable debug logging to see what's happening
    import logging
    logging.basicConfig(level=logging.DEBUG)


def create_source_bean():
    """Create SourceBean with VODCASTV/ravenne configuration."""
    from kbotloadscheduler.bean.beans import SourceBean

    config = {
        "confluence_url": "https://espace.agir.orange.com/",
        "space_key": "VODCASTV",  # Will be transformed to spaces: ["VODCASTV"]
        "labels": "ravenne, rag",  # Will be transformed to labels: ["ravenne", "rag"]
        "include_attachments": True,  # Activer pour tester les attachments
        "max_results": 100,  # Augmenter pour voir plus de résultats
        "export_format": "markdown"
    }

    return SourceBean(
        id=1,
        code="vodcastv_ravenne_test",
        label="VODCASTV Ravenne Test",
        src_type="confluence",
        configuration=json.dumps(config),
        last_load_time=0,
        load_interval=24,
        domain_code="test",
        perimeter_code="tests"
    )


def _setup_confluence_loader():
    """Setup and return a configured ConfluenceLoader instance."""
    print("2. Import des modules...")
    from dependency_injector import providers
    from kbotloadscheduler.secret.secret_manager import ConfigWithSecret
    from kbotloadscheduler.loader.confluence.confluence_loader import ConfluenceLoader
    print("   ✅ Modules importés")

    print("3. Configuration des credentials...")
    config = providers.Configuration()
    config.env.from_env("ENV", "tests")
    config.path_to_secret_config.from_env("PATH_TO_SECRET_CONFIG", "conf/etc/secrets/tests")

    config_with_secret = ConfigWithSecret(config=config)
    print("   ✅ Configuration créée")

    print("4. Initialisation du ConfluenceLoader...")
    loader = ConfluenceLoader(config_with_secret)
    print("   ✅ ConfluenceLoader initialisé")

    return loader


def _save_documents_to_file(documents, source):
    """Save documents to a local JSON file in production format."""
    print("7. Sauvegarde locale (simulation format production)...")

    # Create output directory in project's tmp folder
    output_dir = Path(project_root) / "tmp" / "downloads"
    output_dir.mkdir(parents=True, exist_ok=True)

    # Generate filename with timestamp
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = output_dir / f"vodcastv_ravenne_list_{timestamp}.json"

    # Convert documents to serializable format
    serialized_documents = []
    for doc in documents:
        serialized_doc = {
            "id": doc.id,
            "name": doc.name,
            "path": doc.path,
            "modification_time": doc.modification_time.isoformat() if doc.modification_time else None
        }
        serialized_documents.append(serialized_doc)

    # Sort documents by ID
    sorted_serialized_documents = sorted(serialized_documents, key=lambda d: d["id"])

    # Create production format
    dict_list = {
        "source": source.__dict__,
        "documents": sorted_serialized_documents
    }

    # Save to local JSON file
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(dict_list, f, indent=2, ensure_ascii=False)
        print(f"   ✅ Format production sauvegardé: {output_file}")
        print(f"   📁 Fichier: {output_file.absolute()}")
        print(f"   📊 Documents: {len(documents)} (triés par ID)")
        print("   🔧 Structure: source.__dict__ + documents[]")
        return output_file
    except Exception as save_error:
        print(f"   ❌ Erreur lors de la sauvegarde: {save_error}")
        return None


def _check_specific_page(documents):
    """Check if the specific page 1994899263 is found in documents."""
    print("   🔍 Recherche de la page spécifique 1994899263:")
    page_found = False
    for doc in documents:
        if "1994899263" in doc.id:
            page_found = True
            print(f"      ✅ Page 1994899263 trouvée: {doc.name}")
            print(f"         URL: {doc.path}")
            break

    if not page_found:
        print(f"      ❌ Page 1994899263 NOT trouvée dans les {len(documents)} résultats")
        print("      💡 Vérifiez que cette page a bien le label 'ravenne' ou 'rag'")


def _analyze_document_types(documents):
    """Analyze and display document types (pages vs attachments)."""
    print("   🔍 Analyse des types de documents:")
    pages_count = 0
    attachments_count = 0

    for doc in documents:
        # Check if it's an attachment (ID contains "att")
        if "|att" in doc.id:
            attachments_count += 1
            print(f"      📎 Attachment: {doc.name} (ID: {doc.id})")
        else:
            pages_count += 1
            print(f"      📄 Page: {doc.name} (ID: {doc.id})")

    print(f"   📊 Répartition: {pages_count} pages, {attachments_count} attachments")


def _display_sample_documents(documents):
    """Display first few documents for review."""
    print("   📝 Premiers documents:")
    for i, doc in enumerate(documents[:3]):
        doc_type = "📎 Attachment" if "|att" in doc.id else "📄 Page"
        print(f"      {i + 1}. {doc_type}: {doc.name}")
        print(f"         ID: {doc.id}")
        print(f"         Modifié: {doc.modification_time}")

    if len(documents) > 3:
        print(f"      ... et {len(documents) - 3} autres documents")


def _verify_document_ids(documents):
    """Verify document IDs format."""
    print("   🔍 Vérification format des IDs:")
    for doc in documents[:2]:
        if '|' in doc.id:
            parts = doc.id.split('|')
            print(f"      {doc.name}: {parts[0]}|{parts[1]}|{parts[2]}")
        else:
            print(f"      {doc.name}: {doc.id}")


def _analyze_results(documents):
    """Analyze and display test results."""
    print("8. Analyse des résultats...")

    if not documents:
        print("   ⚠️  Aucun document trouvé")
        print("   💡 Vérifiez que:")
        print("      - L'espace VODCASTV existe")
        print("      - Des pages ont le label 'ravenne' ou 'rag'")
        print("      - Vous avez les permissions nécessaires")
    else:
        print(f"   📊 Documents trouvés: {len(documents)}")
        _check_specific_page(documents)
        _analyze_document_types(documents)
        _display_sample_documents(documents)
        _verify_document_ids(documents)


def _display_success_summary(documents, output_file):
    """Display test success summary."""
    print("9. Récapitulatif du test...")
    print("   ✅ Test réussi!")
    print("   📈 Résultats:")
    print("      - get_document_list() exécuté sans erreur")
    print(f"      - {len(documents)} documents récupérés")
    print("      - Configuration VODCASTV/ravenne,rag validée")
    print("      - Format production exact sauvegardé")
    print("   🔧 Compatibilité:")
    print("      - Structure identique à TreatmentFileManager.write_document_list()")
    print("      - Peut être lu par TreatmentFileManager.read_document_list()")
    print("      - Prêt pour intégration avec DocumentService.compare_document_list()")

    if output_file:
        print(f"   📁 Fichier de résultats conservé: {output_file.name}")
        print(f"   💡 Vous pouvez examiner le fichier: {output_file.absolute()}")


def _handle_error_troubleshooting(e):
    """Handle error troubleshooting and display helpful messages."""
    print(f"   ❌ Erreur: {str(e)}")
    print(f"   🔧 Type d'erreur: {type(e).__name__}")

    # Common error troubleshooting
    print("\n💡 Solutions possibles:")
    error_str = str(e).lower()
    if "authentication" in error_str or "401" in str(e):
        print("   - Vérifier le PAT token dans conf/etc/secrets/tests/confluence-credentials/secret")
        print("   - Vérifier que le token n'a pas expiré")
    elif "404" in str(e) or "space" in error_str:
        print("   - Vérifier que l'espace VODCASTV existe")
        print("   - Vérifier les permissions d'accès à l'espace")
    elif "connection" in error_str or "network" in error_str:
        print("   - Vérifier la connectivité réseau vers https://espace.agir.orange.com")
        print("   - Vérifier si un proxy est nécessaire")
    else:
        print("   - Vérifier les logs pour plus de détails")
        print("   - Essayer avec une configuration plus simple")

    import traceback
    print("\n🔍 Stack trace:")
    traceback.print_exc()


def test_get_document_list():
    """Test get_document_list() with VODCASTV space and ravenne labels."""
    print("🧪 Test E2E: get_document_list() - VODCASTV/ravenne")
    print("=" * 60)

    try:
        # Step 1: Setup environment
        print("1. Configuration de l'environnement...")
        setup_environment()
        print("   ✅ Variables d'environnement configurées")

        # Step 2-4: Setup loader
        loader = _setup_confluence_loader()

        # Step 5: Create SourceBean with test configuration
        print("5. Création du SourceBean...")
        source = create_source_bean()
        print("   ✅ SourceBean créé")
        print("   📋 Espace: VODCASTV (via space_key)")
        print("   🏷️  Labels: ravenne, rag (comma-separated)")
        print("   📄 Max résultats: 100")

        # Step 6: Call get_document_list()
        print("6. Appel de get_document_list()...")
        print("   ⏳ Recherche en cours...")

        documents = loader.get_document_list(source)
        print(f"   ✅ {len(documents)} document(s) trouvé(s)")

        # Step 7: Save results
        output_file = _save_documents_to_file(documents, source)

        # Step 8: Analyze results
        _analyze_results(documents)

        # Step 9: Display success summary
        _display_success_summary(documents, output_file)

        return True

    except Exception as e:
        _handle_error_troubleshooting(e)
        return False


if __name__ == "__main__":
    print("🚀 Démarrage du test E2E get_document_list()")
    print(f"📁 Répertoire: {os.getcwd()}")
    print(f"🐍 Python: {sys.version}")

    success = test_get_document_list()

    if success:
        print("\n🎉 Test terminé avec succès!")
        exit(0)
    else:
        print("\n❌ Test échoué!")
        exit(1)
