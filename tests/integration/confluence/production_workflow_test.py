#!/usr/bin/env python3
"""
Test de Workflow de Production - Validation End-to-End de LoaderService.get_document()
====================================================================================

APERÇU :
Ce test valide le workflow de production complet Étape 3 en utilisant des formats de données réels
des étapes de workflow précédentes. Il s'assure que LoaderService.get_document() fonctionne correctement
avec les données de production et génère des métadonnées compatibles SharePoint.

WORKFLOW DE PRODUCTION TESTÉ :
Étape 1: POST /loader/list/{perimeter} → list.json (✅ Prérequis)
Étape 2: POST /document/compare/{perimeter} → getdoc.json (🔄 Simulé ici)
Étape 3: POST /loader/document/{perimeter} → .metadata.json (🧪 TESTÉ ICI)

EXIGENCES :
1. Fichier d'entrée : tmp/download/vodcastv_ravenne_list_20250617_161017.json
   - Doit être généré en exécutant d'abord les tests de l'Étape 1
   - Contient des données réelles de source et de document Confluence

   Exemple :
   ````
    {
    "source": {
        "id": 1,
        "code": "vodcastv_ravenne_test",
        "label": "VODCASTV Ravenne Test",
        "src_type": "confluence",
        "configuration": "{\"spaces\": [\"VODCASTV\"], \"labels\": [\"ravenne\"], \"include_attachments\": true, \"max_results\": 100, \"export_format\": \"markdown\"}",
        "last_load_time": 0,
        "load_interval": 24,
        "domain_code": "test",
        "perimeter_code": "tests",
        "force_embedding": false
    },
    "documents": [
        {
        "id": "test|vodcastv_ravenne_test|1994899263",
        "name": "Etude - Création des profils génétiques OTT",
        "path": "https://espace.agir.orange.com/pages/viewpage.action?pageId=1994899263",
        "modification_time": "2025-06-18T11:42:25.438429+00:00"
        },
        {
        "id": "test|vodcastv_ravenne_test|att2005951698",
        "name": "image-2024-11-22_18-2-28.png",
        "path": "https://espace.agir.orange.com/download/attachments/1994899263/image-2024-11-22_18-2-28.png",
        "modification_time": "2025-06-18T11:42:25.618508+00:00"
        },
        {
        "id": "test|vodcastv_ravenne_test|att2006357553",
        "name": "image-2024-11-18_11-53-14.png",
        "path": "https://espace.agir.orange.com/download/attachments/1994899263/image-2024-11-18_11-53-14.png",
        "modification_time": "2025-06-18T11:42:25.618521+00:00"
        },
        {
        "id": "test|vodcastv_ravenne_test|att2021218478",
        "name": "image-2024-11-28_13-55-18.png",
        "path": "https://espace.agir.orange.com/download/attachments/1994899263/image-2024-11-28_13-55-18.png",
        "modification_time": "2025-06-18T11:42:25.618477+00:00"
        }
    ]
    }
 ```

2. Environnement Python :
   - Toutes les dépendances du projet installées
   - Chemin source correctement configuré pour les imports

3. Permissions :
   - Accès en écriture au répertoire downloads/ pour la sortie de test
   - Aucun accès réseau requis (utilise des mocks)

CE QUE CE TEST VALIDE :
✅ Workflow de production LoaderService.get_document()
✅ Compatibilité exacte du format de fichier de production (list.json → getdoc.json → metadata.json)
✅ Exigences des champs de métadonnées SharePoint
✅ Conventions de nommage de production
✅ Tests basés sur des mocks (aucune dépendance externe)
✅ Gestion d'erreurs et rapports

VALEUR UNIQUE vs AUTRES TESTS :
- tests/service/test_loader_service.py: Tests unitaires pour LoaderService
- tests/integration/confluence/loader_service_get_document_test.py: Intégration LoaderService générique
- CE TEST: Validation du workflow de production avec formats de fichiers réels et compatibilité SharePoint

SORTIE :
- downloads/production_workflow_test_{timestamp}/: Répertoire des résultats de test
- getdoc_production/: Fichiers getdoc.json générés (simulation Étape 2)
- metadata_output/: Fichiers metadata.json générés (sortie Étape 3)
- production_workflow_test_summary.json: Rapport de test complet

UTILISATION :
    # Prérequis : Exécuter l'Étape 1 pour générer les données d'entrée
    python tests/integration/confluence/get_document_list_test.py

    # Puis exécuter ce test
    python tests/integration/confluence/production_workflow_test.py

INTÉGRATION :
Ce test fait partie de la suite de Validation de Production et doit être exécuté
avant le déploiement pour valider la compatibilité complète du workflow.
"""

import json
import os
import sys
from datetime import datetime
from pathlib import Path
from unittest.mock import MagicMock, patch

from kbotloadscheduler.bean.beans import SourceBean, DocumentBean
from kbotloadscheduler.service.loader_service import LoaderService

# Add src path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "..", "..", ".."))
src_path = os.path.join(project_root, "src")
sys.path.insert(0, src_path)


def load_document_list():
    """
    Charger la liste de documents depuis la sortie de l'Étape 1.

    EXIGENCES :
    - Le fichier doit exister : tmp/download/vodcastv_ravenne_list_20250617_161017.json
    - Le fichier doit être généré en exécutant d'abord get_document_list_test.py
    - Le fichier doit contenir du JSON valide avec les clés 'source' et 'documents'

    Returns:
        tuple: (source_data, documents_data) depuis le fichier JSON

    Raises:
        FileNotFoundError: Si le fichier d'entrée requis n'existe pas
    """
    json_file = Path("tmp/download/vodcastv_ravenne_list_20250617_161017.json")

    if not json_file.exists():
        raise FileNotFoundError(f"List.json non trouvé : {json_file.absolute()}")

    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    return data["source"], data["documents"]


def create_production_getdoc_files(output_dir, source_data, documents_data, max_docs=3):
    """
    Créer des fichiers getdoc.json exactement comme l'Étape 2 de production les générerait.

    Cela simule la sortie du service de comparaison de documents que LoaderService.get_document()
    attend en entrée. Chaque fichier getdoc.json contient un SourceBean et DocumentBean
    dans le format exact utilisé en production.

    Args:
        output_dir (Path): Répertoire pour créer les fichiers getdoc
        source_data (dict): Configuration source de l'Étape 1
        documents_data (list): Liste de documents de l'Étape 1
        max_docs (int): Nombre maximum de documents à traiter (défaut : 3)

    Returns:
        list: Liste des dictionnaires d'info de fichiers getdoc avec métadonnées pour les tests

    Format:
        Chaque getdoc.json contient :
        {
          "source": SourceBean.__dict__,
          "document": DocumentBean.to_serializable_dict()
        }
    """
    print(f"   📄 Création de {min(max_docs, len(documents_data))} fichiers getdoc.json (format production)...")

    getdoc_dir = output_dir / "getdoc_production"
    getdoc_dir.mkdir(exist_ok=True)

    # Créer SourceBean (format production exact)
    source_bean = SourceBean(
        id=source_data["id"],
        code=source_data["code"],
        label=source_data["label"],
        src_type=source_data["src_type"],
        configuration=source_data["configuration"],
        last_load_time=source_data["last_load_time"],
        load_interval=source_data["load_interval"],
        domain_code=source_data["domain_code"],
        perimeter_code=source_data["perimeter_code"],
        force_embedding=source_data.get("force_embedding", False)
    )

    getdoc_files = []

    # Sélectionner un mélange de documents pour les tests
    selected_docs = documents_data[:max_docs]

    for doc_data in selected_docs:
        # Créer DocumentBean (format production exact)
        modification_time = datetime.fromisoformat(doc_data["modification_time"].replace('Z', '+00:00'))
        document_bean = DocumentBean(
            id=doc_data["id"],
            name=doc_data["name"],
            path=doc_data["path"],
            modification_time=modification_time
        )

        # Créer le contenu getdoc.json (FORMAT PRODUCTION EXACT)
        getdoc_content = {
            "source": source_bean.__dict__,
            "document": document_bean.to_serializable_dict()
        }

        # Générer le nom de fichier (nommage production exact)
        doc_type = "att" if "|att" in doc_data["id"] else "page"
        doc_id = doc_data["id"].split("|")[-1]
        timestamp = "20250617163000"  # Timestamp fixe pour la cohérence
        filename = f"{timestamp}_{source_data['domain_code']}_{source_data['code']}_getdoc_{doc_type}_{doc_id}.json"
        file_path = getdoc_dir / filename

        # Sauvegarder getdoc.json
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(getdoc_content, f, indent=2, ensure_ascii=False)

        getdoc_files.append({
            "file_path": str(file_path),
            "filename": filename,
            "source_bean": source_bean,
            "document_bean": document_bean,
            "document_type": doc_type
        })

        doc_type_emoji = "📎" if doc_type == "att" else "📄"
        print(f"      {doc_type_emoji} {filename}")

    print(f"   ✅ {len(getdoc_files)} fichiers getdoc.json créés")
    return getdoc_files


def mock_download_response(document_bean):
    """
    Créer une réponse de téléchargement mock réaliste pour les tests LoaderService.

    Simule la réponse de ConfluenceLoader.get_document() sans
    nécessiter d'appels API Confluence réels ou d'accès réseau.

    Args:
        document_bean (DocumentBean): Document pour créer la réponse mock

    Returns:
        dict: Réponse mock avec contenu/emplacement et métadonnées

    Formats de réponse :
        - Pages : Contenu HTML avec structure réaliste
        - Pièces jointes : Contenu binaire avec emplacement GCS uniquement
    """
    if "|att" in document_bean.id:
        # Pièce jointe binaire - retourner uniquement l'emplacement, pas le contenu
        return {
            "location": f"gs://mock-bucket/attachments/{document_bean.id}",
            "file_size": 2048,
            "export_format": "binary",
            "content_type": "application/octet-stream"
        }
    else:
        # Page HTML
        html_content = f"""<!DOCTYPE html>
<html lang="fr">
<head>
    <title>{document_bean.name}</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>{document_bean.name}</h1>
    <div id="content">
        <p>Contenu mock pour document Confluence.</p>
        <p><strong>ID :</strong> {document_bean.id}</p>
        <p><strong>Chemin :</strong> {document_bean.path}</p>
        <div class="section">
            <h2>Section de test</h2>
            <p>Contenu simulé pour les tests...</p>
        </div>
    </div>
</body>
</html>"""
        return {
            "content": html_content,
            "content_type": "text/html; charset=utf-8",
            "location": f"gs://mock-bucket/pages/{document_bean.id}.html",
            "file_size": len(html_content.encode('utf-8')),
            "export_format": "html"
        }


def test_loader_service_production_workflow():
    """
    Tester LoaderService.get_document() avec le format et workflow de production exact.

    WORKFLOW TESTÉ :
    1. Charger le list.json réel de l'Étape 1 (tmp/download/)
    2. Générer des fichiers getdoc.json au format production (simulation Étape 2)
    3. Appeler LoaderService.get_document() pour chaque document (Étape 3)
    4. Valider la génération de métadonnées compatibles SharePoint
    5. Sauvegarder les fichiers metadata.json comme le ferait la production
    6. Générer un rapport de test complet

    CRITÈRES DE VALIDATION :
    ✅ Tous les champs de métadonnées SharePoint requis présents
    ✅ Conventions de nommage de fichiers correctes utilisées
    ✅ Format de métadonnées conforme aux exigences de production
    ✅ Gestion d'erreurs fonctionne correctement
    ✅ Opérations de fichiers se terminent avec succès

    STRATÉGIE DE MOCKING :
    - TreatmentFileManager : Opérations de fichiers et écriture de métadonnées
    - LoaderManager : Instanciation du loader Confluence
    - ConfluenceLoader : Téléchargement de documents sans appels réseau

    Returns:
        bool: True si tous les tests passent, False en cas d'échecs

    Effets de bord :
        - Crée un répertoire de sortie de test avec les résultats
        - Génère des fichiers metadata.json pour validation
        - Sauvegarde un rapport de résumé de test complet
    """
    print("🧪 Test LoaderService.get_document() - Format production exact")
    print("=" * 75)

    try:
        # Étape 1 : Charger les données source
        print("1. Chargement des données source...")
        source_data, documents_data = load_document_list()
        print(f"   ✅ {len(documents_data)} documents disponibles")
        print(f"   📁 Source : {source_data['code']} ({source_data['label']})")

        # Étape 2 : Configurer l'environnement de test
        print("2. Configuration de l'environnement...")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        test_dir = Path("downloads") / f"production_workflow_test_{timestamp}"
        test_dir.mkdir(exist_ok=True)

        metadata_output_dir = test_dir / "metadata_output"
        metadata_output_dir.mkdir(exist_ok=True)

        print(f"   ✅ Répertoire de test : {test_dir}")

        # Étape 3 : Créer les fichiers getdoc.json de production
        print("3. Création des fichiers getdoc.json (simulation étape 2)...")
        getdoc_files = create_production_getdoc_files(test_dir, source_data, documents_data, max_docs=3)

        # Étape 4 : Tester les appels LoaderService.get_document()
        print("4. Test LoaderService.get_document() (étape 3)...")

        results = []

        # Mocker les dépendances
        with patch('kbotloadscheduler.service.loader_service.TreatmentFileManager') as mock_tfm, \
                patch('kbotloadscheduler.service.loader_service.LoaderManager') as mock_lm, \
                patch('kbotloadscheduler.loader.confluence.confluence_loader.ConfluenceLoader'):

            # Configurer le mock TreatmentFileManager
            mock_tfm_instance = MagicMock()
            mock_tfm.return_value = mock_tfm_instance
            mock_tfm_instance.get_tmp_path.return_value = str(metadata_output_dir)
            mock_tfm_instance.extract_info_from_file_name.return_value = ["20250617163000", "test",
                                                                          "vodcastv_ravenne_test", "getdoc", "1234"]
            mock_tfm_instance.build_directory_name.return_value = str(metadata_output_dir)

            # Mocker write_document_metadata pour retourner les métadonnées qu'on lui passe
            def mock_write_metadata(metadata_dict):
                return metadata_dict

            mock_tfm_instance.write_document_metadata.side_effect = mock_write_metadata

            # Configurer le mock LoaderManager
            mock_lm_instance = MagicMock()
            mock_lm.return_value = mock_lm_instance

            # Configurer LoaderService avec des dépendances mockées
            loader_service = LoaderService(mock_lm_instance, mock_tfm_instance)

            for i, getdoc_info in enumerate(getdoc_files, 1):
                doc_type_emoji = "📎" if getdoc_info["document_type"] == "att" else "📄"
                print(f"   📥 [{i}/{len(getdoc_files)}] {doc_type_emoji} {getdoc_info['document_bean'].name}")

                try:
                    # Configurer les mocks pour ce document
                    mock_tfm_instance.read_document_get_file.return_value = {
                        "source": getdoc_info["source_bean"],
                        "document": getdoc_info["document_bean"]
                    }

                    # Configurer le mock du loader Confluence
                    mock_loader_instance = MagicMock()
                    mock_lm_instance.get_loader.return_value = mock_loader_instance

                    # Mocker la réponse de téléchargement
                    mock_response = mock_download_response(getdoc_info["document_bean"])
                    mock_loader_instance.get_document.return_value = mock_response

                    # APPEL RÉEL à LoaderService.get_document() avec format production
                    result = loader_service.get_document(
                        "test",  # perimeter_code
                        getdoc_info["filename"]  # document_get_file
                    )

                    metadata = result.get("metadata", {})

                    print(f"      ✅ Métadonnées générées ({len(metadata)} champs)")

                    # Valider le format des métadonnées (compatibilité SharePoint)
                    required_fields = [
                        "domain_code", "source_code", "source_type", "source_conf",
                        "document_id", "document_name", "location", "modificationDate"
                    ]

                    missing_fields = [field for field in required_fields if field not in metadata]
                    if missing_fields:
                        print(f"      ⚠️  Champs manquants : {missing_fields}")
                    else:
                        print("      ✅ Format de métadonnées valide")

                    # Sauvegarder metadata.json (comme le fait la production)
                    doc_id_safe = getdoc_info["document_bean"].id.replace("|", "_")
                    metadata_filename = f"{doc_id_safe}.metadata.json"
                    metadata_file_path = metadata_output_dir / metadata_filename

                    with open(metadata_file_path, 'w', encoding='utf-8') as f:
                        json.dump(metadata, f, indent=2, ensure_ascii=False)

                    print(f"      📋 Sauvegardé : {metadata_filename}")

                    # Stocker le résultat
                    result = {
                        "getdoc_file": getdoc_info["filename"],
                        "document_id": getdoc_info["document_bean"].id,
                        "document_name": getdoc_info["document_bean"].name,
                        "document_type": getdoc_info["document_type"],
                        "metadata_file": metadata_filename,
                        "metadata": metadata,
                        "success": True,
                        "error": None
                    }
                    results.append(result)

                except Exception as e:
                    print(f"      ❌ Erreur : {str(e)}")

                    result = {
                        "getdoc_file": getdoc_info["filename"],
                        "document_id": getdoc_info["document_bean"].id,
                        "document_name": getdoc_info["document_bean"].name,
                        "document_type": getdoc_info["document_type"],
                        "metadata_file": None,
                        "metadata": None,
                        "success": False,
                        "error": str(e)
                    }
                    results.append(result)

        # Étape 5 : Analyser les résultats
        print("5. Analyse des résultats...")

        successful = [r for r in results if r["success"]]
        failed = [r for r in results if not r["success"]]

        print("   📊 Statistiques :")
        print(f"      - Appels réussis : {len(successful)}")
        print(f"      - Appels échoués : {len(failed)}")
        print(f"      - Taux de réussite : {len(successful) / len(results) * 100:.1f}%")

        # Étape 6 : Valider le format de sortie
        if successful:
            print("6. Validation du format de sortie...")

            sample_metadata = successful[0]["metadata"]
            print(f"   📋 Exemple de métadonnées (document : {successful[0]['document_name']}) :")

            for key, value in sample_metadata.items():
                print(f"      - {key}: {value}")

            # Vérifier la compatibilité SharePoint
            sharepoint_fields = [
                "domain_code", "source_code", "source_type", "source_conf",
                "document_id", "document_name", "location", "modificationDate",
                "export_format"
            ]

            compatible = all(field in sample_metadata for field in sharepoint_fields)
            print(f"   ✅ Compatible SharePoint : {compatible}")

            # Compter les fichiers de métadonnées
            metadata_files = list(metadata_output_dir.glob("*.metadata.json"))
            print(f"   📁 Fichiers de métadonnées créés : {len(metadata_files)}")

        # Étape 7 : Sauvegarder le résumé de test
        print("7. Sauvegarde du résumé de test...")

        test_summary = {
            "test_info": {
                "timestamp": timestamp,
                "test_name": "production_workflow_test",
                "workflow_step": "3",
                "description": "Test LoaderService.get_document() format production",
                "source": {
                    "code": source_data["code"],
                    "label": source_data["label"],
                    "domain": source_data["domain_code"]
                }
            },
            "input": {
                "getdoc_files_tested": len(getdoc_files),
                "document_types": {
                    "pages": len([r for r in results if r["document_type"] == "page"]),
                    "attachments": len([r for r in results if r["document_type"] == "att"])
                }
            },
            "output": {
                "successful_calls": len(successful),
                "failed_calls": len(failed),
                "metadata_files_created": len(list(metadata_output_dir.glob("*.metadata.json")))
            },
            "validation": {
                "sharepoint_compatible": len(successful) > 0,
                "all_required_fields_present": len(failed) == 0
            },
            "results": results
        }

        summary_file = test_dir / "production_workflow_test_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(test_summary, f, indent=2, ensure_ascii=False)

        print(f"   ✅ Résumé sauvegardé : {summary_file}")

        # Étape 8 : Évaluation finale
        print("8. Évaluation finale...")

        if len(successful) == len(results):
            print("   🎉 TEST DE WORKFLOW DE PRODUCTION RÉUSSI !")
            print("   ✅ LoaderService.get_document() fonctionne parfaitement")
            print("   ✅ Format getdoc.json → metadata.json validé")
            print("   ✅ Compatibilité SharePoint confirmée")
            print("   ✅ Étape 3 du workflow prête pour la production")
        elif len(successful) > 0:
            print("   ⚠️  Test PARTIELLEMENT RÉUSSI :")
            print(f"   📊 {len(successful)}/{len(results)} appels fonctionnent")
            if failed:
                print("   ❌ Erreurs à résoudre :")
                for fail in failed:
                    print(f"      - {fail['document_name']}: {fail['error']}")
        else:
            print("   ❌ Test ÉCHOUÉ : Aucun appel n'a fonctionné")

        print(f"   📁 Résultats complets : {test_dir}")

        return len(successful) == len(results)

    except Exception as e:
        print(f"   ❌ Erreur générale : {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 Test de Workflow de Production - Validation LoaderService.get_document()")
    print("=" * 80)
    print("📋 VÉRIFICATION DES EXIGENCES :")
    print("   1. Fichier d'entrée : tmp/download/vodcastv_ravenne_list_20250617_161017.json")
    print("   2. Exécuter get_document_list_test.py d'abord pour générer les données d'entrée")
    print("   3. Permissions d'écriture pour le répertoire downloads/")
    print("   4. Aucun accès réseau requis (utilise des mocks)")
    print("=" * 80)
    print(f"📁 Répertoire de travail : {os.getcwd()}")
    print()

    success = test_loader_service_production_workflow()

    print("\n" + "=" * 80)
    if success:
        print("🎉 TEST DE WORKFLOW DE PRODUCTION RÉUSSI !")
        print("✅ LoaderService.get_document() validé pour usage en production")
        print("✅ Compatibilité des métadonnées SharePoint confirmée")
        print("✅ Workflow de format de fichier de production validé")
        print("🔗 Prêt pour le déploiement en production")
    else:
        print("❌ TEST DE WORKFLOW DE PRODUCTION ÉCHOUÉ !")
        print("🔧 Vérifiez les messages d'erreur ci-dessus et assurez-vous que :")
        print("   - Le fichier d'entrée existe et est valide")
        print("   - Toutes les dépendances sont installées")
        print("   - Les permissions de fichiers sont correctes")
        print("🔄 Relancez après avoir corrigé les problèmes")
    print("=" * 80)

    exit(0 if success else 1)
