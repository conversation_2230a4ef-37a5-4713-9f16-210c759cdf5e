# Confluence Integration Tests - Complete Guide

## 📋 Overview

This directory contains a comprehensive test suite for Confluence integration, designed with a modern aligned architecture that supports both mock and E2E testing scenarios.

## 🏗️ Test Architecture

### Core Test Suite (Production Ready)
```
tests/integration/confluence/
├── 🎭 Mock Tests (No Network Required)
│   ├── get_document_list_mock_test.py     # Mock document list generation
│   └── get_document_mock_test.py          # Mock document downloads
├── 🌐 E2E Tests (Network Required)
│   ├── get_document_list_test.py          # Real API document list retrieval
│   └── get_document_test.py               # Real document downloads
├── 🔧 Production Workflow Tests
│   ├── loader_service_get_document_test.py # LoaderService integration (Step 3)
│   └── production_workflow_test.py        # SharePoint compatibility validation
└── 🎬 Orchestration
    └── run_aligned_tests.py               # Test suite orchestrator
```

## 🎯 Quick Start

### Daily Development (Recommended)
```bash
# Fast development cycle - no network required
python tests/integration/confluence/run_aligned_tests.py --mode=mock
```

### Pre-deployment Validation
```bash
# Complete E2E validation with real network calls
python tests/integration/confluence/run_aligned_tests.py --mode=e2e

# Production workflow validation
python tests/integration/confluence/loader_service_get_document_test.py
```

## 📊 Test Categories

### 🎭 Mock Tests
**Purpose**: Fast development and CI/CD testing without external dependencies

**Benefits**:
- ✅ No network required
- ✅ No authentication required
- ✅ Fast and deterministic execution
- ✅ Realistic test data

**Tests**:
- `get_document_list_mock_test.py` - Generates mock document lists with realistic data
- `get_document_mock_test.py` - Simulates document downloads and content generation

### 🌐 E2E Tests
**Purpose**: Full integration validation with real Confluence API

**Requirements**:
- 🔐 PAT token in `conf/etc/secrets/tests/confluence-credentials/secret`
- 🌐 Network access to `https://espace.agir.orange.com`
- 📄 VODCASTV space with "ravenne" labeled pages

**Tests**:
- `get_document_list_test.py` - Real API calls to retrieve document lists
- `get_document_test.py` - Real document downloads and content processing

### 🔧 Production Workflow Tests
**Purpose**: Validate specific production workflows and integrations

**Tests**:
- `loader_service_get_document_test.py` - Tests LoaderService workflow (Step 3)
- `production_workflow_test.py` - SharePoint compatibility and format validation

## 🗂️ Data Format Alignment

All tests use a unified data format for maximum compatibility:

```json
{
  "source": {
    "id": 1,
    "code": "vodcastv_ravenne_test",
    "label": "VODCASTV Ravenne Test",
    "src_type": "confluence",
    "configuration": "{\"spaces\":[\"VODCASTV\"],\"labels\":[\"ravenne\"]}",
    "domain_code": "test",
    "perimeter_code": "tests"
  },
  "documents": [
    {
      "id": "test|vodcastv_ravenne_test|page123",
      "name": "Page Title",
      "path": "https://espace.agir.orange.com/...",
      "modification_time": "2024-06-19T14:30:52Z"
    }
  ]
}
```

## 📁 File Structure

### Input Files
- `tmp/downloads/vodcastv_ravenne_list_*.json` - Document lists (auto-detected)

### Output Files
- `tmp/downloads/vodcastv_ravenne_mock_*.json` - Mock-generated document lists
- `tmp/downloads/loader_service_test_*/` - LoaderService test outputs
- `tmp/downloads/get_document_*/` - Document download outputs

## 🔧 Configuration

### Environment Variables
```bash
ENV=tests
PATH_TO_SECRET_CONFIG=conf/etc/secrets/tests
```

### Confluence Configuration
```json
{
  "spaces": ["VODCASTV"],
  "labels": ["ravenne"],
  "include_attachments": true,
  "max_results": 100,
  "export_format": "markdown"
}
```

## 🎬 Test Orchestration

### Automated Workflow (`run_aligned_tests.py`)

**Mock Mode** (Default):
```bash
python tests/integration/confluence/run_aligned_tests.py --mode=mock
```
1. Generates mock document list
2. Simulates document downloads
3. Validates data format consistency

**E2E Mode**:
```bash
python tests/integration/confluence/run_aligned_tests.py --mode=e2e
```
1. Retrieves real document list from Confluence
2. Simulates document downloads based on real data
3. Validates end-to-end workflow

## 🔍 Test Details

### Mock Tests Architecture

#### `get_document_list_mock_test.py`
- **Purpose**: Generate realistic mock document lists
- **Output**: `vodcastv_ravenne_mock_*.json` with realistic data
- **Features**: Pages + attachments, realistic IDs, timestamps

#### `get_document_mock_test.py`
- **Purpose**: Simulate document downloads
- **Input**: Any `vodcastv_ravenne_list_*.json` file
- **Output**: Mock HTML/binary content, metadata files

### E2E Tests Architecture

#### `get_document_list_test.py`
- **Purpose**: Real Confluence API integration
- **Output**: `vodcastv_ravenne_list_*.json` with real data
- **Features**: Filters by VODCASTV space and "ravenne" labels

#### `get_document_test.py`
- **Purpose**: Real document downloads
- **Input**: Real document list from step 1
- **Output**: Real markdown content, binary attachments

### Production Workflow Tests

#### `loader_service_get_document_test.py`
- **Purpose**: Validate LoaderService integration (Step 3 of production workflow)
- **Features**: Tests getdoc.json → .metadata.json transformation
- **Output**: Production-format metadata files
- **Mock Strategy**: Uses `FullMockConfluenceLoader` for network-free testing

#### `production_workflow_test.py`
- **Purpose**: SharePoint compatibility validation
- **Features**: Tests format compatibility across different systems

## 🧪 Testing Strategies

### Development Workflow
1. **Daily Development**: Use mock tests for fast iteration
2. **Feature Integration**: Run E2E tests to validate real API integration
3. **Pre-deployment**: Run full suite including production workflow tests

### CI/CD Integration
```bash
# Fast CI pipeline (mock only)
python tests/integration/confluence/run_aligned_tests.py --mode=mock

# Full integration pipeline (requires secrets)
python tests/integration/confluence/run_aligned_tests.py --mode=e2e
python tests/integration/confluence/loader_service_get_document_test.py
```

## 🚀 Best Practices

### 1. **Test Isolation**
- Each test can run independently
- Mock tests don't require external dependencies
- E2E tests are self-contained with proper authentication

### 2. **Data Consistency**
- All tests use the same data format
- Files are automatically discovered (no hardcoded paths)
- Output structure is consistent across all tests

### 3. **Error Handling**
- Tests include comprehensive error reporting
- Failed operations are logged with detailed context
- Test results include success/failure statistics

## 🛠️ Troubleshooting

### Mock Tests
- **Import errors**: Check Python path and dependencies
- **Mock failures**: Verify patch targets and MagicMock setup

### E2E Tests
- **Authentication 401**: Verify PAT token in secrets directory
- **Space not found 404**: Verify VODCASTV space access
- **Network errors**: Check connectivity to espace.agir.orange.com

### Production Tests
- **LoaderService errors**: Check TreatmentFileManager configuration
- **File not found**: Ensure previous steps completed successfully

## 📈 Success Metrics

### Test Coverage
- ✅ Document list retrieval (mock + E2E)
- ✅ Document downloads (mock + E2E)
- ✅ LoaderService integration
- ✅ Production workflow validation

### Quality Assurance
- ✅ Data format consistency across all tests
- ✅ Network-free development capability
- ✅ Real API validation capability
- ✅ Production workflow compatibility

## 🔄 Maintenance

### Regular Tasks
- Update mock data to reflect production changes
- Verify E2E tests still connect to correct Confluence spaces
- Update documentation when adding new test scenarios

### Deprecation Strategy
- Legacy tests should be migrated to this aligned architecture
- Redundant tests should be consolidated or removed
- Documentation should be kept current and consolidated

---

**Last Updated**: June 18, 2025
**Architecture Version**: 2.0 (Aligned Tests)
**Compatibility**: Production workflows + Modern CI/CD
