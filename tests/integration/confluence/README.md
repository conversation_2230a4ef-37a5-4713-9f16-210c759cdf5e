# Confluence Integration Tests

## 📋 Documentation

- **[Complete Guide](./README_CONSOLIDATED.md)** - Full documentation and quick start
- **[Documentation Index](./INDEX.md)** - Navigation and file overview
- **[Mock Architecture](./MOCK_ARCHITECTURE.md)** - Technical mock implementation details

## 🚀 Quick Start

### Daily Development (Mock Tests)
```bash
python tests/integration/confluence/run_aligned_tests.py --mode=mock
```

### Pre-deployment Validation (E2E Tests)
```bash
python tests/integration/confluence/run_aligned_tests.py --mode=e2e
```

---

**Architecture**: Mock + E2E Aligned Tests
**Last Updated**: June 18, 2025
