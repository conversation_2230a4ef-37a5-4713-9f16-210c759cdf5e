# env
.kbot-back-env
__pycache__
.idea
Pipfile
Pipfile.lock

# Secrets - NEVER commit real secrets!
conf/etc/secrets/local
conf/etc/secrets/tests/*/secret
conf/etc/secrets/prod/*/secret
**/*-sharepoint-client-confiit
**/*-sharepoint-client-private-key

# Confluence RAG data (legacy location - should use tmp/confluence/rag-data)
.confluence_rag_data/

# Temporary data directory
tmp/

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
coverage_report/*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
flake8_*
bandit_report.csv

# Base sqlite de test
sql_app_test.db
sql_app_test.mktsearch_vdb

#Mac
.DS_Store

# Intellij
.idea/

# Vscode
.vscode/
.github/chatmodes/

# Temporary test files at project root (should be in tests/ directory)
/test_*.py
/*_test.py
/get_*.py
/run_*.py
/examples

debug_*.py
temp_*.py
check_*.py


# Debug tests directory (development/debugging only)
debug-tests/

# ruff
.ruff_cache/
.ruff_lintr

# Reports
reports/

# Archive
*.zip