#!/usr/bin/env python3
"""
Test Cloud Run Confluence Loader with Proper Authentication
============================================================

This script tests the deployed Cloud Run Confluence loader service
using Google Cloud authentication.
"""

import json
import time
import requests
from google.auth import default
from google.auth.transport.requests import Request
from google.oauth2 import service_account

# Configuration
CLOUD_RUN_URL = "https://your-service-name-project-id.a.run.app"
PERIMETER_CODE = "mktsearch"
GCS_GETLIST_FILE = "gs://ofr-ekb-knowledgebot-work-dev-mktsearch-dev/************/CiblesRurales/testconfluence27284/getlist/getlist.json"

def get_authenticated_session():
    """Get an authenticated session for Cloud Run."""
    print("🔐 Authenticating with Google Cloud...")

    try:
        # Try to use default credentials first (if running on GCP or with gcloud auth)
        credentials, project = default()
    except Exception:
        # Use service account key file if available
        credentials = service_account.Credentials.from_service_account_file(
            'confluence-tester-key.json',
            scopes=['https://www.googleapis.com/auth/cloud-platform']
        )

    # Get the ID token for Cloud Run authentication
    auth_req = Request()
    credentials.refresh(auth_req)

    # For Cloud Run, we need an identity token
    from google.oauth2 import id_token
    token = id_token.fetch_id_token(auth_req, CLOUD_RUN_URL)

    session = requests.Session()
    session.headers.update({
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    })

    return session

def test_confluence_list_endpoint():
    """Test the Confluence document list endpoint."""
    print(f"📋 Testing Confluence List Endpoint: {PERIMETER_CODE}")

    session = get_authenticated_session()

    url = f"{CLOUD_RUN_URL}/loader/list/{PERIMETER_CODE}"
    payload = {
        "get_list_file": GCS_GETLIST_FILE
    }

    print(f"🌐 URL: {url}")
    print(f"📦 Payload: {json.dumps(payload, indent=2)}")

    try:
        start_time = time.time()
        response = session.post(url, json=payload, timeout=300)  # 5 minutes timeout
        end_time = time.time()

        print(f"⏱️  Duration: {end_time - start_time:.2f}s")
        print(f"📊 Status: {response.status_code}")

        if response.status_code == 200:
            documents = response.json()
            print(f"✅ Success! Found {len(documents)} documents")

            # Print first few documents
            for i, doc in enumerate(documents[:3]):
                print(f"   📄 Document {i+1}: {doc.get('title', 'Unknown')}")

            return documents
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"   Response: {response.text}")
            return None

    except Exception as e:
        print(f"❌ Exception: {e}")
        return None

def test_confluence_document_endpoint(document_list):
    """Test the Confluence document download endpoint."""
    if not document_list:
        print("❌ No documents to test download")
        return

    print(f"\n📄 Testing Confluence Document Endpoint")

    # Use the first document for testing
    first_doc = document_list[0]
    print(f"🎯 Testing download of: {first_doc.get('title', 'Unknown')}")

    session = get_authenticated_session()

    url = f"{CLOUD_RUN_URL}/loader/document/{PERIMETER_CODE}"

    # Create document request payload
    document_request = {
        "source": {
            "id": 1079611284,
            "code": "testconfluence27284",
            "src_type": "confluence",
            "configuration": json.dumps({
                "confluence_url": "https://espace.agir.orange.com/",
                "space_key": "VODCASTV",
                "labels": "ravenne, rag",
                "include_attachements": "true"
            })
        },
        "document": first_doc
    }

    payload = {
        "document_get_file": f"gs://mock-bucket-{PERIMETER_CODE}/test-doc-request.json"
    }

    print(f"🌐 URL: {url}")
    print(f"📦 Document: {first_doc.get('id', 'Unknown ID')}")

    try:
        start_time = time.time()
        response = session.post(url, json=payload, timeout=300)
        end_time = time.time()

        print(f"⏱️  Duration: {end_time - start_time:.2f}s")
        print(f"📊 Status: {response.status_code}")

        if response.status_code == 200:
            document_data = response.json()
            print(f"✅ Success! Downloaded document metadata")
            print(f"   📊 Content length: {len(document_data.get('content', ''))}")
            return document_data
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"   Response: {response.text}")
            return None

    except Exception as e:
        print(f"❌ Exception: {e}")
        return None

def main():
    """Main test function."""
    print("🚀 Testing Cloud Run Confluence Loader")
    print("=" * 50)

    # Test document list
    documents = test_confluence_list_endpoint()

    if documents:
        print(f"\n✅ List endpoint test: SUCCESS ({len(documents)} documents)")

        # Test document download
        test_confluence_document_endpoint(documents)
    else:
        print("\n❌ List endpoint test: FAILED")

if __name__ == "__main__":
    main()
