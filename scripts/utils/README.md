# Utility Scripts

This directory contains utility scripts for configuration checking and workspace management.

## Files:

### Configuration Utilities
- `check_config.py` - Validates and checks project configuration
- `get_spaces.py` - Utility for retrieving Confluence spaces information

## Usage

```bash
# Check project configuration
python scripts/utils/check_config.py

# Get Confluence spaces info
python scripts/utils/get_spaces.py
```

## Purpose

These utilities help with:
- Configuration validation
- Environment setup verification  
- Confluence workspace exploration
- Development and debugging support

These are maintenance and development tools, not part of the main application.
