#!/bin/bash
# Setup service account for Cloud Run testing

PROJECT_ID="your-project-id"
SERVICE_ACCOUNT_NAME="confluence-loader-tester"
SERVICE_ACCOUNT_EMAIL="${SERVICE_ACCOUNT_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"
CLOUD_RUN_SERVICE="your-cloud-run-service"

echo "🔐 Setting up service account for Cloud Run testing..."

# Create service account
gcloud iam service-accounts create $SERVICE_ACCOUNT_NAME \
    --display-name="Confluence Loader Tester" \
    --description="Service account for testing Confluence loader endpoints"

# Grant Cloud Run Invoker permission
gcloud run services add-iam-policy-binding $CLOUD_RUN_SERVICE \
    --member="serviceAccount:${SERVICE_ACCOUNT_EMAIL}" \
    --role="roles/run.invoker" \
    --region="your-region"

# Grant necessary GCS permissions
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:${SERVICE_ACCOUNT_EMAIL}" \
    --role="roles/storage.objectViewer"

# Create key file for authentication
gcloud iam service-accounts keys create confluence-tester-key.json \
    --iam-account=$SERVICE_ACCOUNT_EMAIL

echo "✅ Service account setup complete!"
echo "🔑 Key file: confluence-tester-key.json"
echo ""
echo "🚀 Now you can use this service account to authenticate API calls to your Cloud Run service."
