#!/bin/bash
# Quick test using curl with gcloud authentication

CLOUD_RUN_URL="https://your-service-name-project-id.a.run.app"
PERIMETER="mktsearch"

echo "🔐 Getting authentication token..."
TOKEN=$(gcloud auth print-identity-token)

if [ -z "$TOKEN" ]; then
    echo "❌ Failed to get authentication token"
    echo "💡 Make sure you're logged in: gcloud auth login"
    exit 1
fi

echo "✅ Token obtained"

echo "📋 Testing Confluence List Endpoint..."

# Test the list endpoint
curl -X POST "${CLOUD_RUN_URL}/loader/list/${PERIMETER}" \
    -H "Authorization: Bearer ${TOKEN}" \
    -H "Content-Type: application/json" \
    -d '{
        "get_list_file": "gs://ofr-ekb-knowledgebot-work-dev-mktsearch-dev/202506190800/CiblesRurales/testconfluence27284/getlist/getlist.json"
    }' \
    --max-time 300 \
    --verbose

echo ""
echo "✅ Test complete!"
echo ""
echo "💡 For document download test, you'll need the document ID from the list response:"
echo "curl -X POST \"${CLOUD_RUN_URL}/loader/document/${PERIMETER}\" \\"
echo "    -H \"Authorization: Bearer \${TOKEN}\" \\"
echo "    -H \"Content-Type: application/json\" \\"
echo "    -d '{\"document_get_file\": \"gs://path/to/getdoc.json\"}'"
