#!/usr/bin/env python3
"""
Script to add deprecation warnings to all legacy properties in ConfluenceConfig.
"""

import re
from pathlib import Path

def add_deprecation_warnings():
    """Add deprecation warnings to all legacy properties."""

    config_file = Path("src/kbotloadscheduler/loader/confluence/config/confluence_config.py")
    content = config_file.read_text(encoding='utf-8')

    # Properties that need to have both getter and setter warnings
    properties_with_setters = [
        'confluence_auth_mode', 'spaces', 'parallel_downloads', 'max_parallel_workers',
        'circuit_breaker_threshold', 'circuit_breaker_timeout_seconds', 'child_page_depth',
        'file_extensions', 'duplicate_filename_strategy', 'extract_drawio_as_documents',
        'include_drawio_png_exports', 'include_attachments', 'attachment_filter_mode',
        'include_child_pages', 'default_timeout', 'export_format', 'max_results',
        'labels', 'exclude_labels', 'last_modified_days', 'custom_cql',
        'cache_ttl_minutes', 'retry_attempts', 'retry_delay_seconds',
        'max_child_pages_in_metadata'
    ]

    # Properties that only have getters
    properties_read_only = [
        'max_filename_length'
    ]

    # Mapping to new property paths
    property_mapping = {
        'confluence_auth_mode': 'auth.confluence_auth_mode',
        'spaces': 'basic.spaces',
        'max_results': 'basic.max_results',
        'export_format': 'basic.export_format',
        'include_child_pages': 'basic.include_child_pages',
        'child_page_depth': 'basic.child_page_depth',
        'include_attachments': 'attachments.include_attachments',
        'file_extensions': 'attachments.file_extensions',
        'attachment_filter_mode': 'attachments.attachment_filter_mode',
        'extract_drawio_as_documents': 'attachments.extract_drawio_as_documents',
        'include_drawio_png_exports': 'attachments.include_drawio_png_exports',
        'labels': 'filtering.labels',
        'exclude_labels': 'filtering.exclude_labels',
        'last_modified_days': 'filtering.last_modified_days',
        'custom_cql': 'filtering.custom_cql',
        'parallel_downloads': 'performance.parallel_downloads',
        'max_parallel_workers': 'performance.max_parallel_workers',
        'cache_ttl_minutes': 'performance.cache_ttl_minutes',
        'retry_attempts': 'performance.retry_attempts',
        'retry_delay_seconds': 'performance.retry_delay_seconds',
        'circuit_breaker_threshold': 'performance.circuit_breaker_threshold',
        'circuit_breaker_timeout_seconds': 'performance.circuit_breaker_timeout_seconds',
        'duplicate_filename_strategy': 'file_processing.duplicate_filename_strategy',
        'default_timeout': 'file_processing.default_timeout',
        'max_filename_length': 'file_processing.max_filename_length',
        'max_child_pages_in_metadata': 'future.max_child_pages_in_metadata',
    }

    # Pattern to find legacy property getters
    getter_pattern = r'(@property\s+def\s+(\w+)\(self\)\s*->\s*[^:]+:\s*"""Legacy property for backward compatibility\.""")'

    # Pattern to find legacy property setters
    setter_pattern = r'(@(\w+)\.setter\s+def\s+\2\(self,\s*value:[^)]+\)\s*->\s*None:\s*"""Legacy property setter for backward compatibility\.""")'

    def replace_getter(match):
        full_match = match.group(1)
        prop_name = match.group(2)

        if prop_name not in property_mapping:
            return full_match

        new_path = property_mapping[prop_name]

        deprecation_text = f'''@property
    def {prop_name}(self) -> {full_match.split('->')[1].split(':')[0].strip()}:
        """Legacy property for backward compatibility.

        .. deprecated:: 1.0.0
            Use config.{new_path} instead.
        """
        warnings.warn(
            f"config.{prop_name} is deprecated. Use config.{new_path} instead.",
            DeprecationWarning,
            stacklevel=2
        )'''

        return deprecation_text

    def replace_setter(match):
        full_match = match.group(1)
        prop_name = match.group(2)

        if prop_name not in property_mapping:
            return full_match

        new_path = property_mapping[prop_name]

        # Extract the parameter type from the original setter
        param_type = full_match.split('value:')[1].split(')')[0].strip()

        deprecation_text = f'''@{prop_name}.setter
    def {prop_name}(self, value: {param_type}) -> None:
        """Legacy property setter for backward compatibility.

        .. deprecated:: 1.0.0
            Use config.{new_path} instead.
        """
        warnings.warn(
            f"config.{prop_name} is deprecated. Use config.{new_path} instead.",
            DeprecationWarning,
            stacklevel=2
        )'''

        return deprecation_text

    # Apply replacements
    content = re.sub(getter_pattern, replace_getter, content, flags=re.MULTILINE | re.DOTALL)
    content = re.sub(setter_pattern, replace_setter, content, flags=re.MULTILINE | re.DOTALL)

    # Write back the content
    config_file.write_text(content, encoding='utf-8')
    print(f"✅ Added deprecation warnings to {config_file}")

if __name__ == "__main__":
    add_deprecation_warnings()
