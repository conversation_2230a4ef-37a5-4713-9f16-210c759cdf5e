#!/usr/bin/env python3
"""
Script de démonstration des améliorations de sécurité pour la création de noms de fichiers sûrs.

Ce script teste la méthode _create_safe_filename améliorée avec différents cas de test
pour démontrer les fonctionnalités de sécurité implémentées.
"""

import sys
import os
from unittest.mock import Mock, patch

# Ajouter le répertoire src au path pour les imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from kbotloadscheduler.loader.confluence.client.confluence_client import ConfluenceClient
from kbotloadscheduler.loader.confluence.client.confluence_credentials import ConfluenceCredentials
from kbotloadscheduler.loader.confluence.config.confluence_config import ConfluenceConfig


def test_security_improvements():
    """Teste les améliorations de sécurité de la méthode _create_safe_filename."""
    
    print("🔒 Test des Améliorations de Sécurité - Création de Noms de Fichiers Sûrs")
    print("=" * 80)
    
    # Configuration de test
    credentials = ConfluenceCredentials(
        url="https://test.atlassian.net",
        pat_token="fake_token",
        cloud=True
    )
    
    config = ConfluenceConfig()
    config.max_filename_length = 50  # Limite courte pour les tests
    
    # Mock Confluence pour éviter les appels réseau
    with patch('kbotloadscheduler.loader.confluence.client.confluence_client.Confluence'):
        client = ConfluenceClient(credentials, config=config)
    
    # Cas de test avec différents niveaux de sécurité
    test_cases = [
        # (titre_original, description)
        ("document.pdf", "Nom normal"),
        ("doc<>:\"/\\|?*.pdf", "Caractères dangereux"),
        ("../../../etc/passwd", "Tentative de traversée de répertoire"),
        ("CON.txt", "Nom réservé Windows"),
        ("prn", "Nom réservé Windows (minuscules)"),
        ("", "Chaîne vide"),
        ("<>:\"/\\|?*", "Seulement des caractères dangereux"),
        ("...", "Seulement des points"),
        (".", "Point seul"),
        ("..", "Double point"),
        ("a" * 100 + ".pdf", "Nom très long"),
        ("  document.pdf  ", "Espaces en début/fin"),
        ("..document.pdf..", "Points en début/fin"),
        ("COM1.exe", "Nom réservé avec extension"),
        ("normal file name.txt", "Nom avec espaces"),
    ]
    
    print(f"{'Titre Original':<30} | {'Résultat Sécurisé':<30} | {'Description'}")
    print("-" * 80)
    
    for original_title, description in test_cases:
        safe_filename = client._create_safe_filename(original_title, "att123")
        
        # Affichage avec troncature pour la lisibilité
        original_display = original_title[:28] + ".." if len(original_title) > 30 else original_title
        safe_display = safe_filename[:28] + ".." if len(safe_filename) > 30 else safe_filename
        
        print(f"{original_display:<30} | {safe_display:<30} | {description}")
    
    print("\n" + "=" * 80)
    print("✅ Tests de Sécurité Terminés")
    
    # Tests spécifiques de validation
    print("\n🔍 Validation des Fonctionnalités de Sécurité:")
    print("-" * 50)
    
    # Test 1: Caractères dangereux supprimés
    result = client._create_safe_filename("test<>file.pdf", "att123")
    assert "<" not in result and ">" not in result
    print("✓ Caractères dangereux supprimés")
    
    # Test 2: Protection contre traversée de répertoire
    result = client._create_safe_filename("../../../etc/passwd", "att123")
    assert ".." not in result
    print("✓ Protection contre traversée de répertoire")
    
    # Test 3: Noms réservés Windows détectés
    result = client._create_safe_filename("CON.txt", "att123")
    assert "att123" in result
    print("✓ Noms réservés Windows détectés")
    
    # Test 4: Limitation de longueur respectée
    long_name = "a" * 100 + ".pdf"
    result = client._create_safe_filename(long_name, "att123")
    assert len(result) <= config.max_filename_length
    print("✓ Limitation de longueur respectée")
    
    # Test 5: Fallback pour entrées invalides
    result = client._create_safe_filename("", "att123")
    assert result == "attachment_att123"
    print("✓ Fallback pour entrées invalides")
    
    # Test 6: Préservation des extensions
    result = client._create_safe_filename("a" * 100 + ".pdf", "att123")
    assert result.endswith(".pdf")
    print("✓ Extensions préservées lors de la troncature")
    
    print("\n🎉 Toutes les validations de sécurité ont réussi !")


if __name__ == "__main__":
    test_security_improvements()
