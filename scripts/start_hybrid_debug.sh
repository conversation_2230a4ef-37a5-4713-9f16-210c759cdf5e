#!/bin/bash

# Script pour démarrer le serveur en mode hybride avec debug
echo "🚀 Démarrage du serveur en mode hybride..."
echo "📁 Répertoire: $(pwd)"
echo "🔑 Secrets: /Users/<USER>/IdeaProjects/kbot-load-scheduler/conf/etc/secrets/local"

# Variables d'environnement du mode hybride
export ENV="local"
export GCP_PROJECT_ID="mock-project"
export PATH_TO_SECRET_CONFIG="/Users/<USER>/IdeaProjects/kbot-load-scheduler/conf/etc/secrets/local"
export KBOT_BACK_API_CLOUD_RUN_URL="http://localhost:8091"
export KBOT_BACK_API_URL="http://localhost:8091"
export KBOT_EMBEDDING_API_URL="http://localhost:8093"
export KBOT_WORK_BUCKET_PREFIX="gs://mock-bucket-[perimeter_code]"
export URL_SERVICE_BASIC="https://mock-basic-api.example.com/api"
export URL_SERVICE_SHAREPOINT="https://mock.sharepoint.com/"
export URL_SERVICE_CONFLUENCE="https://espace.agir.orange.com/"
export SHAREPOINT_IN_DOC_ID_USE_PROPERTY="UniqueId"
export OKAPI_URL_BASIC="https://mock-okapi.example.com/v2/token"
export SERVICE_SCOPE_BASIC="mock-scope:readonly"
export TIMEOUT_BASIC=120
export USE_MOCKS=true
export MOCK_CONFLUENCE=false
export SKIP_EXTERNAL_AUTH=true

echo "✅ Variables d'environnement configurées"
echo "🔍 USE_MOCKS=$USE_MOCKS"
echo "🔍 MOCK_CONFLUENCE=$MOCK_CONFLUENCE"
echo "🔍 URL_SERVICE_CONFLUENCE=$URL_SERVICE_CONFLUENCE"

cd src
echo "📍 Répertoire actuel: $(pwd)"

echo "🚀 Démarrage uvicorn..."
pipenv run uvicorn kbotloadscheduler.main:app --reload --port 8092
