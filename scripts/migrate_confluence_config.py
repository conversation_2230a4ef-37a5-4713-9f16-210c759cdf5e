#!/usr/bin/env python3
"""
Migration script for ConfluenceConfig legacy properties.

This script helps migrate from legacy flat properties to the new nested structure.
"""

import re
import sys
from pathlib import Path

# Mapping of legacy properties to new nested properties
MIGRATION_MAP = {
    # Auth config
    'config.confluence_auth_mode': 'config.auth.confluence_auth_mode',

    # Basic config
    'config.spaces': 'config.basic.spaces',
    'config.max_results': 'config.basic.max_results',
    'config.export_format': 'config.basic.export_format',
    'config.include_child_pages': 'config.basic.include_child_pages',
    'config.child_page_depth': 'config.basic.child_page_depth',

    # Attachment config
    'config.include_attachments': 'config.attachments.include_attachments',
    'config.file_extensions': 'config.attachments.file_extensions',
    'config.attachment_filter_mode': 'config.attachments.attachment_filter_mode',
    'config.extract_drawio_as_documents': 'config.attachments.extract_drawio_as_documents',
    'config.include_drawio_png_exports': 'config.attachments.include_drawio_png_exports',

    # Filtering config
    'config.labels': 'config.filtering.labels',
    'config.exclude_labels': 'config.filtering.exclude_labels',
    'config.last_modified_days': 'config.filtering.last_modified_days',
    'config.custom_cql': 'config.filtering.custom_cql',

    # Performance config
    'config.parallel_downloads': 'config.performance.parallel_downloads',
    'config.max_parallel_workers': 'config.performance.max_parallel_workers',
    'config.cache_ttl_minutes': 'config.performance.cache_ttl_minutes',
    'config.retry_attempts': 'config.performance.retry_attempts',
    'config.retry_delay_seconds': 'config.performance.retry_delay_seconds',
    'config.circuit_breaker_threshold': 'config.performance.circuit_breaker_threshold',
    'config.circuit_breaker_timeout_seconds': 'config.performance.circuit_breaker_timeout_seconds',
    'config.enable_metrics': 'config.performance.enable_metrics',

    # File processing config
    'config.duplicate_filename_strategy': 'config.file_processing.duplicate_filename_strategy',
    'config.default_timeout': 'config.file_processing.default_timeout',

    # Future config
    'config.max_child_pages_in_metadata': 'config.future.max_child_pages_in_metadata',
}


def migrate_file(file_path: Path, dry_run: bool = True) -> tuple[bool, list[str]]:
    """
    Migrate a single Python file from legacy to new config structure.

    Args:
        file_path: Path to the Python file
        dry_run: If True, only report changes without making them

    Returns:
        Tuple of (changed, list of changes made)
    """
    try:
        content = file_path.read_text(encoding='utf-8')
        original_content = content
        changes = []

        for legacy_prop, new_prop in MIGRATION_MAP.items():
            # Use word boundaries to avoid partial matches
            pattern = rf'\b{re.escape(legacy_prop)}\b'

            if re.search(pattern, content):
                content = re.sub(pattern, new_prop, content)
                changes.append(f"{legacy_prop} -> {new_prop}")

        if content != original_content:
            if not dry_run:
                file_path.write_text(content, encoding='utf-8')
            return True, changes

        return False, []

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False, []


def main():
    """Main migration function."""
    import argparse

    parser = argparse.ArgumentParser(description="Migrate ConfluenceConfig legacy properties")
    parser.add_argument("paths", nargs="*", help="Files or directories to migrate")
    parser.add_argument("--dry-run", action="store_true", help="Show changes without applying them")
    parser.add_argument("--include-tests", action="store_true", help="Include test files")

    args = parser.parse_args()

    if not args.paths:
        # Default to current project
        args.paths = ["src/"]

    dry_run = args.dry_run

    if dry_run:
        print("🔍 DRY RUN - No files will be modified")
        print("=" * 50)

    total_files = 0
    changed_files = 0

    for path_str in args.paths:
        path = Path(path_str)

        if path.is_file():
            files = [path]
        elif path.is_dir():
            # Find Python files
            pattern = "**/*.py"
            files = list(path.glob(pattern))

            # Filter out test files unless explicitly requested
            if not args.include_tests:
                files = [f for f in files if "test" not in str(f).lower()]
        else:
            print(f"⚠️  Path not found: {path}")
            continue

        for file_path in files:
            total_files += 1
            changed, changes = migrate_file(file_path, dry_run)

            if changed:
                changed_files += 1
                status = "📝 WOULD CHANGE" if dry_run else "✅ CHANGED"
                print(f"{status}: {file_path}")
                for change in changes:
                    print(f"  - {change}")
                print()

    print("=" * 50)
    print(f"📊 Summary: {changed_files}/{total_files} files {'would be' if dry_run else 'were'} changed")

    if dry_run and changed_files > 0:
        print("\n💡 Run without --dry-run to apply these changes")


if __name__ == "__main__":
    main()
