#!/bin/bash
# Script de nettoyage périodique du projet
# Usage: ./scripts/cleanup.sh

echo "🧹 Nettoyage du projet kbot-load-scheduler"
echo "=============================================="

# Supprimer les fichiers de cache Python
echo "🐍 Nettoyage des caches Python..."
find . -name "*.pyc" -delete 2>/dev/null || true
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# Supprimer les fichiers de coverage temporaires
echo "📊 Nettoyage des fichiers de coverage..."
rm -f .coverage.* 2>/dev/null || true

# Supprimer d'éventuels fichiers de test temporaires à la racine
echo "🧪 Nettoyage des tests temporaires..."
rm -f test_*.py debug_*.py temp_*.py check_*.py get_*.py run_*.py 2>/dev/null || true

# Nettoyer les fichiers DS_Store sur macOS
echo "🍎 Nettoyage des fichiers .DS_Store..."
find . -name ".DS_Store" -delete 2>/dev/null || true

# Déplacer les rapports vers le bon répertoire
echo "📋 Organisation des rapports..."
mkdir -p reports/flake8
mv flake8_report_*.txt reports/flake8/ 2>/dev/null || true

echo "✅ Nettoyage terminé!"
echo "=============================================="
