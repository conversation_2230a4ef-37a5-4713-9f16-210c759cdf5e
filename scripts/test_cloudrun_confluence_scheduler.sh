#!/bin/bash
# Test Confluence Loader using Google Cloud Scheduler
# This script creates a Cloud Scheduler job to test your deployed Cloud Run service

# Configuration - Update these values for your environment
PROJECT_ID="your-project-id"                    # Replace with your GCP project ID
REGION="your-region"                            # Replace with your Cloud Run region (e.g., europe-west1)
SERVICE_NAME="your-cloud-run-service"           # Replace with your Cloud Run service name
PERIMETER="mktsearch"                           # This should match your uploaded getlist.json
SERVICE_ACCOUNT="your-scheduler-service-account@${PROJECT_ID}.iam.gserviceaccount.com"

# Create a Cloud Scheduler job to test the loader endpoints
echo "📅 Creating Cloud Scheduler job to test Confluence loader..."
echo "🔐 Note: Make sure you've configured Secret Manager first!"
echo "   Run: ./scripts/setup_gcp_confluence_secrets.sh"
echo ""

# Check if secret exists
SECRET_NAME="${PERIMETER}-confluence-credentials"
echo "🔍 Checking if secret exists: ${SECRET_NAME}"

if gcloud secrets describe ${SECRET_NAME} --project=${PROJECT_ID} >/dev/null 2>&1; then
    echo "✅ Secret ${SECRET_NAME} exists"
else
    echo "❌ Secret ${SECRET_NAME} not found!"
    echo "💡 Run setup script first: ./scripts/setup_gcp_confluence_secrets.sh"
    exit 1
fi

# Test 1: Document List Endpoint
gcloud scheduler jobs create http confluence-test-list-mktsearch \
    --location=$REGION \
    --schedule="0 10 * * 1" \
    --uri="https://${SERVICE_NAME}-${PROJECT_ID}.a.run.app/loader/list/${PERIMETER}" \
    --http-method=POST \
    --headers="Content-Type=application/json" \
    --message-body='{
        "get_list_file": "gs://ofr-ekb-knowledgebot-work-dev-mktsearch-dev/************/CiblesRurales/testconfluence27284/getlist/getlist.json"
    }' \
    --oidc-service-account-email="${SERVICE_ACCOUNT}"

echo "✅ Scheduler job created!"
echo "🚀 To run the test immediately:"
echo "gcloud scheduler jobs run confluence-test-list-mktsearch --location=$REGION"

# Test 2: Document Download Endpoint (requires list results first)
echo ""
echo "📝 To test document download, first run the list endpoint, then create another job:"
echo "gcloud scheduler jobs create http confluence-test-doc-mktsearch \\"
echo "    --location=$REGION \\"
echo "    --schedule=\"0 11 * * 1\" \\"
echo "    --uri=\"https://${SERVICE_NAME}-${PROJECT_ID}.a.run.app/loader/document/${PERIMETER}\" \\"
echo "    --http-method=POST \\"
echo "    --headers=\"Content-Type=application/json\" \\"
echo "    --message-body='{\"document_get_file\": \"gs://your-bucket/path/to/getdoc.json\"}' \\"
echo "    --oidc-service-account-email=\"${SERVICE_ACCOUNT}\""
