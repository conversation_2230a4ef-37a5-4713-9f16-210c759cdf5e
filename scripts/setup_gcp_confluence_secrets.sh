#!/bin/bash
# Setup Google Secret Manager for Confluence Credentials - mktsearch perimeter

# Configuration
PROJECT_ID="your-project-id"
PERIMETER="mktsearch"
SECRET_NAME="${PERIMETER}-confluence-credentials"
SERVICE_ACCOUNT="your-cloud-run-service-account@${PROJECT_ID}.iam.gserviceaccount.com"

echo "🔐 Setting up Google Secret Manager for Confluence (perimeter: ${PERIMETER})"
echo "=================================================="

# Step 1: Create the Secret Manager secret
echo "📝 Step 1: Creating secret in Secret Manager..."

# Create the JSON credential file locally first
cat > confluence-credentials.json << EOF
{
  "pat_token": "YOUR_CONFLUENCE_PAT_TOKEN_HERE",
  "confluence_url": "https://espace.agir.orange.com",
  "cloud": false,
  "timeout": 60,
  "max_retries": 3,
  "verify_ssl": true
}
EOF

echo "✅ Created template file: confluence-credentials.json"
echo "⚠️  IMPORTANT: Edit confluence-credentials.json with your real PAT token before proceeding!"
echo ""
echo "To get your PAT token:"
echo "1. Go to: https://espace.agir.orange.com"
echo "2. Profile > Personal Access Tokens"
echo "3. Create new token with READ permissions"
echo "4. Copy the token and replace YOUR_CONFLUENCE_PAT_TOKEN_HERE"
echo ""
read -p "Press Enter when you've updated the credentials file..."

# Create the secret in Secret Manager
echo "🔒 Creating secret: ${SECRET_NAME}"
gcloud secrets create ${SECRET_NAME} \
    --data-file=confluence-credentials.json \
    --project=${PROJECT_ID}

if [ $? -eq 0 ]; then
    echo "✅ Secret created successfully!"
else
    echo "❌ Failed to create secret. Check if it already exists or permissions."
    exit 1
fi

# Step 2: Grant access to Cloud Run service account
echo ""
echo "🔑 Step 2: Granting access to Cloud Run service account..."

gcloud secrets add-iam-policy-binding ${SECRET_NAME} \
    --member="serviceAccount:${SERVICE_ACCOUNT}" \
    --role="roles/secretmanager.secretAccessor" \
    --project=${PROJECT_ID}

if [ $? -eq 0 ]; then
    echo "✅ IAM permissions granted!"
else
    echo "❌ Failed to grant IAM permissions. Check service account exists."
    exit 1
fi

# Step 3: Create backup global secret (fallback)
echo ""
echo "🌐 Step 3: Creating global fallback secret..."

GLOBAL_SECRET="confluence-credentials"
gcloud secrets create ${GLOBAL_SECRET} \
    --data-file=confluence-credentials.json \
    --project=${PROJECT_ID} 2>/dev/null

gcloud secrets add-iam-policy-binding ${GLOBAL_SECRET} \
    --member="serviceAccount:${SERVICE_ACCOUNT}" \
    --role="roles/secretmanager.secretAccessor" \
    --project=${PROJECT_ID} 2>/dev/null

echo "✅ Global fallback secret configured"

# Step 4: Verify the setup
echo ""
echo "🔍 Step 4: Verifying secret setup..."

# Test secret access
SECRET_VALUE=$(gcloud secrets versions access latest --secret=${SECRET_NAME} --project=${PROJECT_ID} 2>/dev/null)

if [ $? -eq 0 ] && [[ $SECRET_VALUE == *"pat_token"* ]]; then
    echo "✅ Secret is accessible and contains expected fields"
    echo "📋 Secret content preview:"
    echo "$SECRET_VALUE" | jq -r 'keys[]' | sed 's/^/   - /'
else
    echo "❌ Failed to verify secret access"
    exit 1
fi

# Step 5: Clean up local file
echo ""
echo "🧹 Step 5: Cleaning up..."
rm -f confluence-credentials.json
echo "✅ Local credentials file removed"

# Summary
echo ""
echo "🎉 SECRET MANAGER SETUP COMPLETE!"
echo "================================="
echo "✅ Secret Name: ${SECRET_NAME}"
echo "✅ Perimeter: ${PERIMETER}"
echo "✅ Service Account Access: Granted"
echo "✅ Global Fallback: Available"
echo ""
echo "🚀 Your Cloud Run service can now access Confluence credentials via:"
echo "   - Primary: ${SECRET_NAME}"
echo "   - Fallback: confluence-credentials"
echo ""
echo "📋 Next steps:"
echo "1. Test your Cloud Run deployment"
echo "2. Monitor logs for authentication success"
echo "3. Use the scheduler script to test endpoints"

echo ""
echo "💡 To update the secret later:"
echo "gcloud secrets versions add ${SECRET_NAME} --data-file=new-credentials.json"
