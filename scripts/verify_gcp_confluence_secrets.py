#!/usr/bin/env python3
"""
Verify Google Secret Manager Configuration for Confluence
========================================================

This script verifies that the Secret Manager is properly configured
for the Confluence loader in Cloud Run.
"""

import json
import subprocess
import sys
from typing import Optional, Dict, Any

# Configuration
PROJECT_ID = "your-project-id"  # Replace with your actual project ID
PERIMETER = "mktsearch"
PRIMARY_SECRET = f"{PERIMETER}-confluence-credentials"
FALLBACK_SECRET = "confluence-credentials"

def run_gcloud_command(cmd: list) -> Optional[str]:
    """Run a gcloud command and return the output."""
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {' '.join(cmd)}")
        print(f"   Error: {e.stderr}")
        return None

def get_secret_value(secret_name: str) -> Optional[Dict[Any, Any]]:
    """Get secret value from Secret Manager."""
    print(f"🔍 Checking secret: {secret_name}")

    cmd = [
        "gcloud", "secrets", "versions", "access", "latest",
        "--secret", secret_name,
        "--project", PROJECT_ID
    ]

    secret_value = run_gcloud_command(cmd)
    if not secret_value:
        return None

    try:
        return json.loads(secret_value)
    except json.JSONDecodeError:
        print(f"❌ Invalid JSON in secret: {secret_name}")
        return None

def verify_secret_content(secret_data: Dict[Any, Any], secret_name: str) -> bool:
    """Verify that secret contains required fields."""
    required_fields = ["pat_token"]
    optional_fields = ["confluence_url", "cloud", "timeout", "max_retries", "verify_ssl"]

    missing_required = [field for field in required_fields if field not in secret_data]

    if missing_required:
        print(f"❌ {secret_name}: Missing required fields: {missing_required}")
        return False

    print(f"✅ {secret_name}: All required fields present")

    # Check field values
    pat_token = secret_data.get("pat_token", "")
    if not pat_token or pat_token == "YOUR_CONFLUENCE_PAT_TOKEN_HERE":
        print(f"❌ {secret_name}: PAT token not configured properly")
        return False

    print(f"   📋 PAT token: {pat_token[:10]}... (valid)")

    # Show optional fields
    for field in optional_fields:
        if field in secret_data:
            print(f"   📋 {field}: {secret_data[field]}")

    return True

def check_service_account_access(secret_name: str, service_account: str) -> bool:
    """Check if service account has access to the secret."""
    print(f"🔑 Checking IAM access for: {service_account}")

    cmd = [
        "gcloud", "secrets", "get-iam-policy", secret_name,
        "--project", PROJECT_ID,
        "--format", "json"
    ]

    policy_json = run_gcloud_command(cmd)
    if not policy_json:
        return False

    try:
        policy = json.loads(policy_json)
        bindings = policy.get("bindings", [])

        for binding in bindings:
            if binding.get("role") == "roles/secretmanager.secretAccessor":
                members = binding.get("members", [])
                if f"serviceAccount:{service_account}" in members:
                    print(f"✅ Service account has secretAccessor role")
                    return True

        print(f"❌ Service account missing secretAccessor role")
        return False

    except json.JSONDecodeError:
        print(f"❌ Failed to parse IAM policy")
        return False

def verify_confluence_url_access(confluence_url: str) -> bool:
    """Test if Confluence URL is accessible."""
    print(f"🌐 Testing Confluence URL accessibility: {confluence_url}")

    try:
        import requests
        response = requests.head(confluence_url, timeout=10, allow_redirects=True)

        if response.status_code in [200, 401, 403]:  # 401/403 are OK - means server is up
            print(f"✅ Confluence URL is accessible (status: {response.status_code})")
            return True
        else:
            print(f"⚠️  Confluence URL returned status: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Failed to access Confluence URL: {e}")
        return False

def main():
    """Main verification function."""
    print("🔐 Verifying Google Secret Manager Configuration for Confluence")
    print("=" * 65)

    success = True
    service_account = f"your-cloud-run-service-account@{PROJECT_ID}.iam.gserviceaccount.com"

    # Test primary secret
    print(f"\n1️⃣  Testing primary secret: {PRIMARY_SECRET}")
    primary_data = get_secret_value(PRIMARY_SECRET)

    if primary_data:
        success &= verify_secret_content(primary_data, PRIMARY_SECRET)
        success &= check_service_account_access(PRIMARY_SECRET, service_account)

        # Test Confluence URL if available
        confluence_url = primary_data.get("confluence_url")
        if confluence_url:
            verify_confluence_url_access(confluence_url)
    else:
        print(f"❌ Primary secret not found or not accessible")
        success = False

    # Test fallback secret
    print(f"\n2️⃣  Testing fallback secret: {FALLBACK_SECRET}")
    fallback_data = get_secret_value(FALLBACK_SECRET)

    if fallback_data:
        verify_secret_content(fallback_data, FALLBACK_SECRET)
        check_service_account_access(FALLBACK_SECRET, service_account)
        print("✅ Fallback secret available")
    else:
        print("⚠️  Fallback secret not found (optional)")

    # Summary
    print(f"\n📋 VERIFICATION SUMMARY")
    print("=" * 30)

    if success:
        print("✅ Secret Manager configuration is READY!")
        print(f"✅ Perimeter '{PERIMETER}' can access Confluence credentials")
        print("\n🚀 Your Cloud Run service should be able to:")
        print("   - Retrieve Confluence credentials from Secret Manager")
        print("   - Authenticate with Confluence API")
        print("   - Process document requests successfully")

        print(f"\n💡 Test your setup with:")
        print(f"   gcloud scheduler jobs run confluence-test-list-{PERIMETER}")

    else:
        print("❌ Secret Manager configuration has ISSUES!")
        print("\n🔧 Fix the issues above before testing Cloud Run")

        print(f"\n💡 Common fixes:")
        print(f"   - Run: ./scripts/setup_gcp_confluence_secrets.sh")
        print(f"   - Update SERVICE_ACCOUNT variable in this script")
        print(f"   - Verify your PAT token is valid")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
