#!/usr/bin/env python3
"""
Script pour nettoyer les fichiers temporaires et rapports de tests.
Usage: python scripts/cleanup_test_files.py [--all] [--dry-run]
"""

import argparse
import shutil
from pathlib import Path

def get_project_root() -> Path:
    """Retourne le répertoire racine du projet."""
    return Path(__file__).parent.parent

def cleanup_directories(dry_run: bool = False, all_files: bool = False):
    """Nettoie les répertoires temporaires."""
    project_root = get_project_root()
    
    # Répertoires à nettoyer - tout dans tmp/
    dirs_to_clean = [
        project_root / "tmp",  # Nettoie tout tmp/ (pytest, coverage, test_outputs, etc.)
        project_root / ".pytest_cache",
        project_root / ".coverage",
    ]
    
    # Fichiers individuels à nettoyer
    files_to_clean = []
    
    if all_files:
        dirs_to_clean.extend([
            project_root / "reports",
            # Conserver coverage_report pour compatibilité si il existe encore
            project_root / "coverage_report", 
        ])
        
        # Fichiers de rapport
        files_to_clean.extend([
            project_root / "coverage.xml",
            project_root / ".coverage.*",
        ])
    
    # Nettoyer les répertoires
    for dir_path in dirs_to_clean:
        if dir_path.exists():
            if dry_run:
                if dir_path.is_file():
                    print(f"[DRY-RUN] Suppression fichier: {dir_path}")
                else:
                    print(f"[DRY-RUN] Suppression répertoire: {dir_path}")
            else:
                try:
                    if dir_path.is_file():
                        dir_path.unlink()
                        print(f"✅ Fichier supprimé: {dir_path}")
                    else:
                        shutil.rmtree(dir_path)
                        print(f"✅ Répertoire supprimé: {dir_path}")
                except Exception as e:
                    print(f"❌ Erreur lors de la suppression de {dir_path}: {e}")
        else:
            print(f"ℹ️  N'existe pas: {dir_path}")
    
    # Nettoyer les fichiers individuels
    for file_pattern in files_to_clean:
        for file_path in project_root.glob(str(file_pattern)):
            if dry_run:
                print(f"[DRY-RUN] Suppression fichier: {file_path}")
            else:
                try:
                    file_path.unlink()
                    print(f"✅ Fichier supprimé: {file_path}")
                except Exception as e:
                    print(f"❌ Erreur lors de la suppression de {file_path}: {e}")
    
    # Fichiers Python compilés
    for pyc_file in project_root.rglob("*.pyc"):
        if dry_run:
            print(f"[DRY-RUN] Suppression .pyc: {pyc_file}")
        else:
            try:
                pyc_file.unlink()
                print(f"✅ Fichier .pyc supprimé: {pyc_file}")
            except Exception as e:
                print(f"❌ Erreur suppression .pyc {pyc_file}: {e}")
    
    # Répertoires __pycache__
    for pycache_dir in project_root.rglob("__pycache__"):
        if dry_run:
            print(f"[DRY-RUN] Suppression __pycache__: {pycache_dir}")
        else:
            try:
                shutil.rmtree(pycache_dir)
                print(f"✅ Répertoire __pycache__ supprimé: {pycache_dir}")
            except Exception as e:
                print(f"❌ Erreur suppression __pycache__ {pycache_dir}: {e}")

def main():
    parser = argparse.ArgumentParser(description="Nettoie les fichiers temporaires de tests")
    parser.add_argument("--dry-run", action="store_true", 
                       help="Affiche ce qui serait supprimé sans le faire")
    parser.add_argument("--all", action="store_true",
                       help="Nettoie aussi les rapports et autres fichiers")
    
    args = parser.parse_args()
    
    print("🧹 Nettoyage des fichiers temporaires...")
    cleanup_directories(dry_run=args.dry_run, all_files=args.all)
    
    if args.dry_run:
        print("\n💡 Utilisez sans --dry-run pour effectuer le nettoyage")
    else:
        print("\n✅ Nettoyage terminé!")

if __name__ == "__main__":
    main()
