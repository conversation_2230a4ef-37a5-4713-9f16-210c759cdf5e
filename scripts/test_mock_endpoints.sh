#!/bin/bash

# Script de test amélioré pour les points d'accès (endpoints) mockés,
# avec une meilleure gestion des erreurs.

LOAD_SCHEDULER_PORT=8092

echo "🧪 Test des points d'accès /loader/list avec des données GCS mockées..."
echo ""

# Vérification si le serveur est en cours d'exécution
echo "📡 Vérification de l'état du serveur..."
if ! curl -s --max-time 5 http://localhost:${LOAD_SCHEDULER_PORT}/docs > /dev/null 2>&1; then
    echo "❌ Le serveur ne fonctionne pas sur le port ${LOAD_SCHEDULER_PORT} !"
    echo ""
    echo "Pour démarrer le serveur, exécutez :"
    echo "  make start-mock"
    echo ""
    echo "Si vous rencontrez des erreurs d'authentification, il est possible que le serveur essaie de"
    echo "se connecter à des services externes. C'est un problème connu qui doit être corrigé."
    echo ""
    echo "Étapes de dépannage :"
    echo "1. Vérifiez que l'authentification mockée est correctement configurée"
    echo "2. Assurez-vous que BasicLoader n'effectue pas d'appels externes en mode mock"
    echo "3. Vérifiez que tous les loaders gèrent correctement USE_MOCK=true"
    exit 1
fi

echo "✅ Le serveur est en cours d'exécution"
echo ""

# Fonction pour tester un point d'accès
test_endpoint() {
    local perimeter=$1
    local bucket=$2
    local source_type=$3

    echo "🔍 Test de ${source_type} (${perimeter}) :"
    echo "   Bucket : gs://mock-bucket-${bucket}/test-data/getlist.json"

    response=$(curl -X POST "http://localhost:${LOAD_SCHEDULER_PORT}/loader/list/${perimeter}" \
        -H "Content-Type: application/json" \
        -d "{\"get_list_file\": \"gs://mock-bucket-${bucket}/test-data/getlist.json\"}" \
        -w "\\n---HTTPCODE:%{http_code}---" \
        -s --max-time 30 2>/dev/null)

    if [[ $? -ne 0 ]]; then
        echo "   ❌ La requête a échoué (timeout ou erreur de connexion)"
        return 1
    fi

    # Extraire le code HTTP et le corps de la réponse
    body=${response%---HTTPCODE:*}
    http_code=${response##*---HTTPCODE:}
    http_code=${http_code%---}

    echo "   Statut HTTP : $http_code"

    if [[ "$http_code" == "200" ]]; then
        echo "   ✅ Succès !"
        echo "   Aperçu de la réponse :"
        echo "$body" | head -3 | sed 's/^/      /'
        if [[ ${#body} -gt 200 ]]; then
            echo "      ... (tronqué)"
        fi
    elif [[ "$http_code" == "500" ]]; then
        echo "   ❌ Erreur Interne du Serveur (500)"
        echo "   Cela indique généralement un problème d'authentification ou de configuration."
        echo "   Aperçu de la réponse :"
        echo "$body" | head -3 | sed 's/^/      /'
    else
        echo "   ⚠️  Code de statut inattendu : $http_code"
        echo "   Aperçu de la réponse :"
        echo "$body" | head -3 | sed 's/^/      /'
    fi

    echo ""
}

# Test des différents types de source
test_endpoint "ebotman" "ebotman" "Confluence"
test_endpoint "mktsearch" "mktsearch" "SharePoint"
test_endpoint "devtools" "devtools" "Google Drive"

echo "🎯 Résumé du test :"
echo "   - Si vous voyez des erreurs 500, les loaders tentent de s'authentifier auprès de services externes."
echo "   - Si vous voyez des réponses 200, le système GCS mocké fonctionne correctement."
echo "   - Consultez les logs du serveur pour des informations d'erreur détaillées."
echo ""
echo "💡 Prochaines étapes pour corriger les problèmes d'authentification :"
echo "   1. Assurez-vous que BasicLoader vérifie la variable d'environnement USE_MOCK."
echo "   2. Mockez les appels d'authentification externes (OKAPI, etc.)."
echo "   3. Faites en sorte que tous les loaders gèrent le mode mock de manière élégante."