# kbot-load-scheduler

![](https://img.shields.io/badge/version-0.19.1-blue.svg)

![](https://img.shields.io/badge/python-3.12+-green.svg)

![](https://img.shields.io/badge/license-Proprietary-orange.svg)

`kbot-load-scheduler` est un composant central de l'écosystème Knowledge Bot, conçu pour orchestrer et automatiser le chargement, la synchronisation et la préparation des données provenant de diverses sources de connaissance. Son objectif est de garantir que le Knowledge Bot dispose toujours d'informations à jour et pertinentes, en gérant de manière robuste et efficace le cycle de vie des documents.

Ce système vise à :

- **Automatiser** le processus de mise à jour des connaissances.
- Assurer un **chargement fiable et résilient** des données.
- Permettre une **intégration facile** de nouvelles sources.
- Optimiser les **performances** de chargement.
- Fournir une **traçabilité** claire des opérations.

## 📚 Documentation Approfondie

Pour une analyse complète de l'architecture, des composants, des flux de données et des décisions de conception, veuillez consulter notre [Documentation Détaillée du Système](./docs/SYSTEM_OVERVIEW.md).

## 📋 Fonctionnalités Clés

- **Architecture modulaire de chargement** : Système extensible avec interface `AbstractLoader`.
- **Support de sources multiples** optimisées pour RAG :
  - **Confluence** (pages, blogs, pièces jointes) - **Spécialement optimisé pour systèmes RAG** avec export Markdown, filtrage intelligent, et support Confluence Server on-premise
  - **SharePoint** (documents, sites) avec extraction de relations
  - **Google Cloud Storage** (fichiers et documents)
  - **Sources personnalisées** via le loader "Basic"
- **Orchestration des chargements** : Planification automatisée, gestion d'étapes granulaires et reprise sur erreur via GCS.
- **REST API** : Interface complète pour l'intégration avec d'autres systèmes (kbot-back, kbot-embedding).
- **Sécurité** : Gestion centralisée des secrets (Google Secret Manager) et authentification aux API (PAT, OAuth).
- **Optimisations RAG** : Export Markdown, extraction de métadonnées relationnelles, conversion DrawIO, filtrage d'attachments.
- **Déploiement Cloud Native** : Conçu pour Cloud Run avec planification via Cloud Scheduler et optimisations pour environnements éphémères.

## 🏗️ Architecture Simplifiée

![Architecture Simplifiée](./docs/assets/simplify-architecture-diagram-02-06-2025-17_48_36.png)

*Pour un diagramme d'architecture détaillé, consultez la [Documentation Détaillée du Système](./docs/SYSTEM_OVERVIEW.md).*

## 🤖 Optimisations RAG (Retrieval-Augmented Generation)

Le kbot-load-scheduler est **spécialement optimisé pour alimenter des systèmes RAG** avec du contenu de qualité :

### ✨ Fonctionnalités RAG
- **Export Markdown natif** : Conversion optimale des pages Confluence pour traitement textuel
- **Extraction de métadonnées relationnelles** : Préservation des relations parent-enfant entre pages et attachments
- **Conversion DrawIO** : Transformation des diagrammes en métadonnées textuelles exploitables
- **Filtrage intelligent** : Sélection par labels, extensions de fichiers, et fraîcheur du contenu
- **Support Confluence Server** : Authentification PAT pour instances on-premise
- **Optimisations Cloud Run** : Architecture serverless adaptée aux déploiements éphémères

### 🎯 Cas d'Usage RAG Principal
```
Confluence Server (on-premise) → PAT Auth → Markdown Export → Relations Metadata → GCS → Vector Database
```

### 📚 Documentation RAG Spécialisée
- **Guide Confluence RAG** : [`src/kbotloadscheduler/loader/confluence/README.md`](./src/kbotloadscheduler/loader/confluence/README.md)
- **Guide Utilisateur RAG** : [`src/kbotloadscheduler/loader/confluence/USER_GUIDE.md`](./src/kbotloadscheduler/loader/confluence/USER_GUIDE.md)
- **Déploiement Cloud Run** : [`docs/CONFLUENCE_RAG_CLOUDRUN_DEPLOYMENT.md`](./docs/CONFLUENCE_RAG_CLOUDRUN_DEPLOYMENT.md)
- **Configuration d'exemple RAG** : [`src/kbotloadscheduler/loader/confluence/rag_source_config.json`](./src/kbotloadscheduler/loader/confluence/rag_source_config.json)

### 🛡️ **NOUVEAU** Solution de Robustesse Confluence
- **Intégration Retry Automatique** : [`docs/CONFLUENCE_INTEGRATION_SUCCESS.md`](./docs/CONFLUENCE_INTEGRATION_SUCCESS.md) ⭐
- **Documentation Complète** : [`docs/CONFLUENCE_DOCUMENTATION_INDEX.md`](./docs/CONFLUENCE_DOCUMENTATION_INDEX.md)
- **Guide de Production** : [`docs/CONFLUENCE_PRODUCTION_USAGE.md`](./docs/CONFLUENCE_PRODUCTION_USAGE.md)

> **🎯 Robustesse Automatique** : Tous les appels Confluence API bénéficient maintenant automatiquement d'une logique de retry robuste avec backoff exponentiel et jitter, sans configuration requise. Résistance intégrée au rate limiting et aux erreurs transitoires.

## 🚀 Installation

### Prérequis

- Python 3.12+
- Accès à Google Cloud Platform (GCP)
- Accès aux instances de service (Confluence, SharePoint, etc.) selon les besoins

**Pour déploiement RAG sur Cloud Run :** Voir le [Guide de Déploiement Cloud Run](./docs/CONFLUENCE_RAG_CLOUDRUN_DEPLOYMENT.md)

### Installation recommandée

Le projet utilise `pipenv` pour la gestion des dépendances et des environnements virtuels. La manière la plus simple de configurer votre environnement est d'utiliser la cible `init` du `Makefile`, qui s'occupera d'installer `pipenv` si nécessaire, de configurer l'environnement virtuel et d'installer toutes les dépendances (y compris celles pour le développement et les tests).

```bash
# Cloner le dépôt
git clone <https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler.git>
cd kbot-load-scheduler

# Initialiser l'environnement et installer toutes les dépendances
make init

```

Cela exécutera les étapes nécessaires, y compris `pipenv --python 3.12` (ou la version spécifiée dans le `Makefile`) et `pipenv run pip install -r requirements.txt -r tests/test-requirements.txt`.

## 🔧 Configuration

1. **Variables d'environnement** : Voir `.env.example` pour la structure. Créez un fichier `.env` pour votre configuration.
2. **Secrets d'accès aux sources** : Gérés via Google Secret Manager. Voir la documentation dans `src/kbotloadscheduler/secret/secret_manager.py` et le guide de configuration des secrets locaux pour les tests dans `conf/etc/secrets/tests/README_SECRETS.md`.

   Exemples de clés de secrets :

  - `{perimeter_code}-confluence-credentials`
  - `{perimeter_code}-sharepoint-credentials`

## 📊 Utilisation

### Démarrer le service

Pour démarrer le service localement pour le développement (nécessite un fichier `.env` configuré et que l'environnement `pipenv` soit activé ou que les commandes soient préfixées par `pipenv run`), utilisez :

```bash
# Démarrage local pour développement (nécessite un .env configuré)
make start
```

Cela exécutera `uvicorn` dans l'environnement `pipenv` avec les variables d'environnement définies dans la cible `start` du `Makefile`.

Alternativement, si vous avez activé l'environnement `pipenv` (avec `pipenv shell`) et configuré les variables d'environnement manuellement :

```bash
uvicorn src.kbotloadscheduler.main:app --host 0.0.0.0 --port 8080 --reload
# Note: le port 8080 est un exemple, le Makefile utilise ${LOAD_SCHEDULER_PORT} qui est 8092

```

### Utilisation de l'API

La documentation OpenAPI (Swagger UI) est disponible sur `http://localhost:8092/docs` (ou l'URL et le port de votre service déployé, selon la configuration de `make start`) lorsque le service est en cours d'exécution.

### Exemples d'appels (Gestion des sources)

```bash
# Créer une nouvelle source (adaptez le port si vous n'utilisez pas make start)
curl -X POST "<http://localhost:8092/sources/>" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "confluence_docs",
    "label": "Documentation Confluence",
    "src_type": "confluence",
    "configuration": "{\"spaces\": [\"DOCS\", \"TECH\"], \"max_results\": 1000}",
    "domain_code": "engineering",
    "perimeter_code": "main"
  }'

# Lister les sources (adaptez le port si vous n'utilisez pas make start)
curl -X GET "<http://localhost:8092/sources/>"
```

*Consultez la [Documentation Détaillée du Système](./docs/SYSTEM_OVERVIEW.md) ou la Swagger UI pour la liste complète des endpoints et leurs détails.*

### Utilisation de Docker

```bash
# Construire l'image
docker build -t kbot-load-scheduler .

# Exécuter le conteneur (exemple minimal)
docker run -p 8080:8080 \
  -e ENV=local \
  -e PATH_TO_SECRET_CONFIG=/app/conf/etc/secrets/local \
  # ... autres variables d'environnement nécessaires ...
  kbot-load-scheduler

```

## 🧪 Tests

### Exécuter les tests

Les tests sont exécutés en utilisant `pytest` dans l'environnement `pipenv`.

```bash
# Exécuter tous les tests unitaires et d'intégration (nécessite configuration d'environnement de test, voir Makefile)
make unit-tests

# Alternativement, pour exécuter pytest directement après avoir activé l'environnement pipenv (pipenv shell)
# et configuré les variables d'environnement de test manuellement :
# pytest

# Tests avec couverture (make unit-tests génère déjà la couverture)
# pytest --cov=src

# Tests spécifiques à un module (ex: Confluence)
# (Assurez-vous que les variables d'environnement de test sont définies)
# pipenv run pytest tests/loader/test_confluence_end_to_end.py -v

```

### 🔧 Tests avec Mocks et Mode Hybride

Pour le développement et les tests locaux, le projet fournit des guides détaillés :

#### Tests avec Mocks GCS/Auth
👉 **[Guide des Mocks GCS](./docs/GCS_MOCKING_GUIDE.md)**
- Tests entièrement locaux sans infrastructure cloud
- Mock de Google Cloud Storage et authentification
- Démarrage rapide avec `make start-mock`

#### Tests Hybrides (Mocks + Vraies Données)
👉 **[Guide Confluence Mode Hybride](./docs/CONFLUENCE_HYBRID_TESTING_GUIDE.md)**
- Combine mocks (GCS/Auth) avec vraie connexion Confluence
- Parfait pour tester avec de vraies données
- Configuration step-by-step avec exemples

```bash
# Mode mock complet (recommandé pour développement)
make start-mock
make test-mock-endpoint

# Mode hybride avec secrets locaux (⭐ NOUVEAU - recommandé pour vraies données)
make start-hybrid  # Utilise conf/etc/secrets/local/confluence-credentials/

# Mode hybride avec variables d'environnement
export USE_MOCKS=true MOCK_CONFLUENCE=false
export CONFLUENCE_URL="https://votre-confluence.atlassian.net"
export CONFLUENCE_USERNAME="..." CONFLUENCE_API_TOKEN="..."
make start-mock
```

### 📁 Organisation des fichiers de tests

**Répertoires de sortie contrôlés** :
- **Fichiers temporaires de tests** : `./tmp/pytest/` (au lieu de `/var/folders/`)
- **Rapports de couverture** : `./tmp/coverage/`
- **Rapports de tests E2E** : `./tmp/test_reports/`
- **Sorties de tests** : `./tmp/test_outputs/`

**Nettoyage des fichiers temporaires** :
```bash
# Voir ce qui serait supprimé
python scripts/cleanup_test_files.py --dry-run

# Nettoyer les fichiers temporaires
python scripts/cleanup_test_files.py

# Nettoyage complet (inclut rapports)
python scripts/cleanup_test_files.py --all
```

**Configuration pytest** :
- `pytest.ini` : Configuration standard avec répertoires de sortie contrôlés
- `pytest-all.ini` : Configuration pour tous les tests (y compris migration)
- L'option `--basetemp=./tmp/pytest` force pytest à utiliser un répertoire local
- Les rapports de couverture sont maintenant dans `./tmp/coverage/`

### Analyse de Sécurité (Bandit)

```bash
# Exécuter l'analyse de sécurité
make bandit
# ou via le script directement (si vous êtes dans l'environnement pipenv)
# ./scripts/bandit_report.sh

```

### 🔧 Améliorations Récentes des Tests

✅ **Corrections apportées** :

- Tests end-to-end Confluence améliorés.
- Mocks robustes (`MockConfluenceAPI`, `MockGCSClient`).
- Meilleure isolation des tests et validation Pydantic dans les fixtures.

📚 **Documentation des tests** :

- Guide des secrets pour les tests : `conf/etc/secrets/tests/README_SECRETS.md`
- Tests Confluence : `src/kbotloadscheduler/loader/confluence/tests/README.md` et `src/kbotloadscheduler/loader/confluence/docs/TESTING_MOCKS.md`

### 🎯 Script d'extraction VODCASTV V2.1

Le projet inclut un **script d'extraction spécialisé V2.1** pour Confluence situé dans `tests/integration/confluence/extract_vodcastv_ravenne_v2.py`. Ce script démontre l'utilisation avancée du loader Confluence refactorisé et offre des fonctionnalités d'extraction complètes :

**✨ Fonctionnalités clés** :
- **Extraction récursive** : Récupération automatique des pages enfants
- **Gestion des pièces jointes** : Téléchargement intelligent avec déduplication
- **Conversion Markdown** : Traitement HTML et Draw.io vers Markdown
- **Filtrage par extensions** : Support de multiples types de fichiers
- **Rapport complet** : Génération automatique d'un rapport d'extraction

**🔧 Utilisation** :
```bash
# Depuis le répertoire racine du projet
cd tests/integration/confluence/
python extract_vodcastv_ravenne_v2.py
```

**📊 Sortie** :
- `./tmp/vodcastv_ravenne_extraction_TIMESTAMP/pages/` - Fichiers Markdown des pages
- `./tmp/vodcastv_ravenne_extraction_TIMESTAMP/attachments/` - Pièces jointes organisées par catégorie
- `./tmp/vodcastv_ravenne_extraction_TIMESTAMP/EXTRACTION_REPORT.md` - Rapport détaillé

> **Note** : Ce script nécessite une configuration d'environnement de test valide avec des credentials Confluence. Voir `conf/etc/secrets/tests/README_SECRETS.md` pour la configuration des secrets locaux.

## 🧪 Testing

Le projet fournit des capacités de test complètes pour différents scénarios :

### Configuration rapide des tests
```bash
# Installer les dépendances
make init

# Configurer des données fictives pour les tests locaux
make setup-mock-data

# Exécuter les tests unitaires
make unit-tests

# Tester avec GCS simulé (aucune authentification requise)
make start-mock
# Dans un autre terminal :
make test-mock-endpoint
```

### Modes de test

| Mode | Commande | Authentification | Services externes | Idéal pour |
|------|---------|----------------|------------------|----------|
| **Tests unitaires** | `make unit-tests` | Aucune | Simulé | Développement, CI/CD |
| **Mock GCS** | `make start-mock` | Aucune | GCS simulé | Test API local |
| **Intégration** | `make start-test` | Requise | GCS réel | Validation pré-production |
| **Développement** | `make start` | Requise | Services réels | Développement normal |

### Commandes de test disponibles
```bash
# Testing
make unit-tests              # Tests unitaires rapides avec mocks
make integration-tests       # Tests d'intégration (nécessite auth)
make test-credentials       # Tester la configuration des credentials
make all-tests              # Tous les tests (unitaires + intégration)

# Modes de démarrage du serveur
make start                  # Développement avec GCP réel
make start-test            # Test avec secrets locaux + GCS réel
make start-mock            # Test local pur avec GCS simulé

# Simulation de GCS
make setup-mock-data       # Créer des données de test simulées
make test-mock-endpoint    # Tester /loader/list avec des données simulées

# Qualité du code
make lint-check            # Tous les contrôles de linting
make black                 # Vérifier le formatage du code
make ruff                  # Vérifier les imports et la qualité du code
make bandit                # Analyse de sécurité
```

📚 **Guide de test détaillé** : Voir [docs/TESTING_GUIDE.md](./docs/TESTING_GUIDE.md) pour une documentation complète sur les tests.

🛠 **Guide de simulation GCS** : Voir [docs/GCS_MOCKING_GUIDE.md](./docs/GCS_MOCKING_GUIDE.md) pour des tests locaux sans credentials GCP.

## 🛠️ Outils de Développement

### Formatage et Analyse du Code

Le projet utilise plusieurs outils pour maintenir la qualité et la cohérence du code :

- **Black** : Formatage automatique du code Python
- **isort** : Tri automatique des imports
- **autopep8** : Corrections automatiques des violations PEP8
- **Flake8** : Analyse statique du code (linting)
- **Bandit** : Analyse de sécurité

```bash
# Vérifier le formatage sans modifier les fichiers
make lint-check

# Appliquer le formatage automatiquement (Black + isort + autopep8)
make format

# Vérifier uniquement avec Black (sans modification)
make black

# Appliquer le formatage Black
make black-fix

# Vérifier uniquement les imports (sans modification)
make isort

# Appliquer le tri des imports
make isort-fix

# Vérifier les corrections autopep8 (sans modification)
make autopep8-check

# Appliquer les corrections autopep8
make autopep8

# Analyse Flake8 (génère un rapport)
make flake

# Analyse Flake8 avec rapport détaillé
make flake-fix

# Analyse de sécurité Bandit
make bandit
```

### Flux de Développement Recommandé

1. **Avant de commiter** : Exécuter `make lint-check` pour vérifier la conformité
2. **Pour corriger automatiquement** : Exécuter `make format` pour appliquer Black et isort
3. **Analyser la qualité** : Exécuter `make flake` et `make bandit` pour détecter les problèmes

## �️ Robustesse et Fiabilité

### 🔄 Solution de Retry Confluence (Nouveau)

Le système intègre maintenant une **solution de retry intelligente** pour gérer les erreurs intermittentes de l'API Confluence (notamment les erreurs HTTP 500 dues au rate limiting).

#### ✨ Fonctionnalités
- **Retry HTTP granulaire** : Nouvelle tentative au niveau des requêtes individuelles
- **Backoff exponentiel avec jitter** : Évite la surcharge serveur
- **Classification intelligente** : Distingue erreurs retriables vs fatales
- **Métriques intégrées** : Monitoring des performances et taux de succès
- **Intégration non-invasive** : Compatible avec le code existant

#### 📋 Documentation Détaillée
- **[📁 Index Complet](./docs/CONFLUENCE_DOCUMENTATION_INDEX.md)** : Navigation dans toute la documentation
- **[Solution Complète](./docs/CONFLUENCE_RATE_LIMITING_SOLUTION.md)** : Diagnostic et solution technique
- **[Guide de Production](./docs/CONFLUENCE_PRODUCTION_USAGE.md)** : Intégration et configuration
- **[Niveaux de Retry](./docs/CONFLUENCE_RETRY_LEVELS_EXPLANATION.md)** : Architecture à deux niveaux
- **[Validation](./docs/VALIDATION_SUMMARY.md)** : Tests et résultats

#### 🚀 Usage Rapide

```python
from kbotloadscheduler.loader.confluence.client.confluence_retry_patch import add_retry_capability

# Ajout non-invasif à votre client existant
client = ConfluenceClient(credentials)
add_retry_capability(client)
client.configure_retry(max_retries=3, base_delay=2.0)

# Utilisation avec retry automatique
results = client.search_content_with_retry(cql, limit=50)
stats = client.get_retry_stats()  # Monitoring
```

## 🚨 Gestion des Erreurs

Le projet utilise un système d'exceptions standardisé pour tous les loaders, offrant une gestion cohérente des erreurs avec classification automatique et support pour le retry.

### Hiérarchie d'Exceptions

```python
from kbotloadscheduler.exceptions import (
    # Exceptions de base
    LoaderException,
    LoaderConfigurationError,
    LoaderAuthenticationError,

    # Exceptions HTTP génériques
    HttpException,
    HttpAuthenticationError,
    HttpPermissionError,

    # Exceptions spécialisées par loader
    ConfluenceClientException,
    SharepointException,
    BasicApiException
)
```

### Fonctionnalités Clés

- **Classification automatique** : Critique/non-critique, retryable/non-retryable
- **Contexte enrichi** : Informations détaillées pour le debugging
- **Compatibilité backward** : Maintien des APIs existantes
- **Factory methods** : Création d'exceptions depuis objets Response
- **Logging intégré** : Niveaux appropriés selon la criticité

### Exemple d'Usage

```python
try:
    pages = confluence_client.get_space_content("DOCS")
except ConfluenceAuthenticationError as e:
    logger.error(f"Authentification échouée: {e}")
    # Logique de re-authentification
except ConfluencePermissionError as e:
    logger.warning(f"Permissions insuffisantes: {e}")
    # Logique de fallback
except ConfluenceClientException as e:
    if e.is_retryable:
        logger.info(f"Erreur retryable: {e}")
        # Logique de retry
    else:
        logger.error(f"Erreur critique: {e}")
        # Logique d'escalade
```

## 📖 Documentation Complémentaire

- **Documentation API** : Disponible via Swagger UI sur `/docs` lorsque le service est lancé.
- [**Documentation Détaillée du Système**](./docs/SYSTEM_OVERVIEW.md) : Architecture, flux, composants.
- **Guides des Loaders** :
  - Confluence : `src/kbotloadscheduler/loader/confluence/README.md`
  - SharePoint : `TODO`
  - GCS : `TODO`
- **Gestion des Secrets pour Tests** : `conf/etc/secrets/tests/README_SECRETS.md`
